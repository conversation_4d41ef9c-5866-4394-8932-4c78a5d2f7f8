-- Add Medical Departments Table and Data
-- This migration creates medical departments table and adds department field to patients

-- Create medical_departments table
CREATE TABLE IF NOT EXISTS public.medical_departments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    icon TEXT, -- Icon name for UI display
    color TEXT DEFAULT '#3B82F6', -- Color for UI theming
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add department_id to patients table
ALTER TABLE public.patients 
ADD COLUMN IF NOT EXISTS department_id UUID REFERENCES public.medical_departments(id),
ADD COLUMN IF NOT EXISTS department_notes TEXT;

-- Create index for department queries
CREATE INDEX IF NOT EXISTS idx_patients_department_id ON public.patients(department_id);
CREATE INDEX IF NOT EXISTS idx_medical_departments_active ON public.medical_departments(is_active);

-- Insert common medical departments
INSERT INTO public.medical_departments (name, description, icon, color) VALUES
('General Medicine', 'General medical consultation and primary care', 'Stethoscope', '#3B82F6'),
('Dentistry', 'Dental care, oral health, and dental procedures', 'Smile', '#10B981'),
('Gynecology', 'Women''s reproductive health and gynecological care', 'Heart', '#EC4899'),
('Pediatrics', 'Medical care for infants, children, and adolescents', 'Baby', '#F59E0B'),
('Cardiology', 'Heart and cardiovascular system care', 'HeartPulse', '#EF4444'),
('Dermatology', 'Skin, hair, and nail conditions treatment', 'Sparkles', '#8B5CF6'),
('Orthopedics', 'Bone, joint, and musculoskeletal system care', 'Bone', '#6B7280'),
('Neurology', 'Nervous system and brain disorders treatment', 'Brain', '#06B6D4'),
('Ophthalmology', 'Eye care and vision-related treatments', 'Eye', '#84CC16'),
('ENT (Ear, Nose, Throat)', 'Ear, nose, throat, and related structures care', 'Ear', '#F97316'),
('Psychiatry', 'Mental health and psychological disorders treatment', 'Brain', '#A855F7'),
('Urology', 'Urinary system and male reproductive health', 'Droplets', '#14B8A6'),
('Gastroenterology', 'Digestive system and gastrointestinal disorders', 'Pill', '#F43F5E'),
('Pulmonology', 'Respiratory system and lung disorders treatment', 'Wind', '#0EA5E9'),
('Endocrinology', 'Hormonal disorders and endocrine system care', 'Zap', '#8B5CF6'),
('Rheumatology', 'Joint, muscle, and autoimmune disorders treatment', 'Bone', '#6B7280'),
('Oncology', 'Cancer diagnosis, treatment, and care', 'Shield', '#DC2626'),
('Emergency Medicine', 'Emergency and urgent medical care', 'AlertTriangle', '#EF4444'),
('Radiology', 'Medical imaging and diagnostic procedures', 'Scan', '#6366F1'),
('Anesthesiology', 'Anesthesia and pain management', 'Syringe', '#64748B')
ON CONFLICT (name) DO NOTHING;

-- Create function to get active departments
CREATE OR REPLACE FUNCTION get_active_medical_departments()
RETURNS TABLE (
    id UUID,
    name TEXT,
    description TEXT,
    icon TEXT,
    color TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        d.id,
        d.name,
        d.description,
        d.icon,
        d.color
    FROM public.medical_departments d
    WHERE d.is_active = true
    ORDER BY d.name ASC;
END;
$$ LANGUAGE plpgsql;

-- Create function to get patient with department info
CREATE OR REPLACE FUNCTION get_patients_with_departments(user_uuid UUID)
RETURNS TABLE (
    id UUID,
    patient_name TEXT,
    email TEXT,
    phone_number TEXT,
    date_of_birth DATE,
    blood_type TEXT,
    insurance TEXT,
    emergency_contact TEXT,
    notes TEXT,
    registration_date DATE,
    workflow_status workflow_status_enum,
    sent_to_consultation_at TIMESTAMP WITH TIME ZONE,
    last_status_update TIMESTAMP WITH TIME ZONE,
    department_id UUID,
    department_name TEXT,
    department_color TEXT,
    department_icon TEXT,
    department_notes TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.patient_name,
        p.email,
        p.phone_number,
        p.date_of_birth,
        p.blood_type,
        p.insurance,
        p.emergency_contact,
        p.notes,
        p.registration_date,
        p.workflow_status,
        p.sent_to_consultation_at,
        p.last_status_update,
        p.department_id,
        d.name as department_name,
        d.color as department_color,
        d.icon as department_icon,
        p.department_notes
    FROM public.patients p
    LEFT JOIN public.medical_departments d ON p.department_id = d.id
    WHERE p.user_id = user_uuid
    ORDER BY p.registration_date DESC, p.last_status_update DESC;
END;
$$ LANGUAGE plpgsql;

-- Update the reception visible patients function to include department info
CREATE OR REPLACE FUNCTION get_reception_visible_patients()
RETURNS TABLE (
    id UUID,
    patient_name TEXT,
    email TEXT,
    phone_number TEXT,
    date_of_birth DATE,
    blood_type TEXT,
    insurance TEXT,
    emergency_contact TEXT,
    notes TEXT,
    registration_date DATE,
    workflow_status workflow_status_enum,
    sent_to_consultation_at TIMESTAMP WITH TIME ZONE,
    last_status_update TIMESTAMP WITH TIME ZONE,
    hours_since_sent NUMERIC,
    department_id UUID,
    department_name TEXT,
    department_color TEXT,
    department_icon TEXT,
    department_notes TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.patient_name,
        p.email,
        p.phone_number,
        p.date_of_birth,
        p.blood_type,
        p.insurance,
        p.emergency_contact,
        p.notes,
        p.registration_date,
        p.workflow_status,
        p.sent_to_consultation_at,
        p.last_status_update,
        CASE 
            WHEN p.sent_to_consultation_at IS NOT NULL 
            THEN EXTRACT(EPOCH FROM (now() - p.sent_to_consultation_at)) / 3600
            ELSE NULL 
        END as hours_since_sent,
        p.department_id,
        d.name as department_name,
        d.color as department_color,
        d.icon as department_icon,
        p.department_notes
    FROM public.patients p
    LEFT JOIN public.medical_departments d ON p.department_id = d.id
    WHERE 
        -- Show registered patients
        p.workflow_status = 'registered'
        OR 
        -- Show patients sent to consultation within last 24 hours
        (
            p.workflow_status = 'sent_to_consultation' 
            AND p.sent_to_consultation_at IS NOT NULL 
            AND p.sent_to_consultation_at > (now() - INTERVAL '24 hours')
        )
    ORDER BY p.registration_date DESC, p.last_status_update DESC;
END;
$$ LANGUAGE plpgsql;

-- Grant necessary permissions
GRANT SELECT ON public.medical_departments TO authenticated;
GRANT EXECUTE ON FUNCTION get_active_medical_departments() TO authenticated;
GRANT EXECUTE ON FUNCTION get_patients_with_departments(UUID) TO authenticated;

-- Enable RLS on medical_departments table
ALTER TABLE public.medical_departments ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for medical_departments (read-only for all authenticated users)
CREATE POLICY "Medical departments are viewable by authenticated users" ON public.medical_departments
    FOR SELECT USING (auth.role() = 'authenticated');
