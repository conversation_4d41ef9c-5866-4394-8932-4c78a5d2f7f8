
-- Add logo_url column to settings table
ALTER TABLE public.settings 
ADD COLUMN IF NOT EXISTS logo_url TEXT;

-- Add currency column to profiles table if it doesn't exist
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS currency TEXT DEFAULT 'KES';

-- Update default currency in settings table to KES (Kenyan Shilling)
ALTER TABLE public.settings 
ALTER COLUMN currency SET DEFAULT 'KES';

-- Clear existing currencies and add African currencies
DELETE FROM public.currencies;

INSERT INTO public.currencies (code, name, symbol) VALUES
  ('KES', 'Kenyan Shilling', 'KSh'),
  ('NGN', 'Nigerian Naira', '₦'),
  ('ZAR', 'South African Rand', 'R'),
  ('EGP', 'Egyptian Pound', 'E£'),
  ('MAD', 'Moroccan Dirham', 'DH'),
  ('TND', 'Tunisian Dinar', 'TND'),
  ('GHS', 'Ghanaian Cedi', 'GH₵'),
  ('UGX', 'Ugandan <PERSON>', 'USh'),
  ('TZS', 'Tanzanian <PERSON>lling', 'TSh'),
  ('ETB', 'Ethiopian Birr', 'Br'),
  ('RWF', 'Rwandan Franc', 'RF'),
  ('XOF', 'West African CFA Franc', 'CFA'),
  ('XAF', 'Central African CFA Franc', 'FCFA'),
  ('BWP', 'Botswana Pula', 'P'),
  ('MUR', 'Mauritian Rupee', '₨')
ON CONFLICT (code) DO NOTHING;

-- Create storage bucket for settings if it doesn't exist
INSERT INTO storage.buckets (id, name, public)
VALUES ('settings', 'settings', true)
ON CONFLICT (id) DO NOTHING;

-- RLS policies for settings bucket (fixed syntax)
CREATE POLICY "Anyone can view settings files" 
  ON storage.objects 
  FOR SELECT 
  USING (bucket_id = 'settings');

CREATE POLICY "Authenticated users can upload settings files" 
  ON storage.objects 
  FOR INSERT 
  TO authenticated
  WITH CHECK (bucket_id = 'settings');

CREATE POLICY "Users can update their own settings files" 
  ON storage.objects 
  FOR UPDATE 
  TO authenticated
  USING (bucket_id = 'settings');

CREATE POLICY "Users can delete their own settings files" 
  ON storage.objects 
  FOR DELETE 
  TO authenticated
  USING (bucket_id = 'settings');
