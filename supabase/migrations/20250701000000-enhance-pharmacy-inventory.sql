-- Enhance pharmacy_inventory table to support comprehensive inventory management
-- This migration adds additional columns to support the enhanced inventory system

-- Add new columns to pharmacy_inventory table
ALTER TABLE public.pharmacy_inventory 
ADD COLUMN IF NOT EXISTS generic_name TEXT,
ADD COLUMN IF NOT EXISTS manufacturer TEXT,
ADD COLUMN IF NOT EXISTS category TEXT,
ADD COLUMN IF NOT EXISTS type TEXT DEFAULT 'medicine' CHECK (type IN ('medicine', 'medical_supply', 'equipment', 'consumable', 'other')),
ADD COLUMN IF NOT EXISTS dosage_form TEXT,
ADD COLUMN IF NOT EXISTS strength TEXT,
ADD COLUMN IF NOT EXISTS unit TEXT DEFAULT 'tablets',
ADD COLUMN IF NOT EXISTS reorder_level INTEGER DEFAULT 10,
ADD COLUMN IF NOT EXISTS batch_number TEXT,
ADD COLUMN IF NOT EXISTS supplier TEXT,
ADD COLUMN IF NOT EXISTS location TEXT,
ADD COLUMN IF NOT EXISTS description TEXT;

-- Update existing records to have default values
UPDATE public.pharmacy_inventory 
SET 
  type = 'medicine',
  unit = 'tablets',
  reorder_level = 10,
  category = 'General'
WHERE type IS NULL OR unit IS NULL OR reorder_level IS NULL OR category IS NULL;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_pharmacy_inventory_type ON public.pharmacy_inventory(type);
CREATE INDEX IF NOT EXISTS idx_pharmacy_inventory_category ON public.pharmacy_inventory(category);
CREATE INDEX IF NOT EXISTS idx_pharmacy_inventory_status ON public.pharmacy_inventory(status);
CREATE INDEX IF NOT EXISTS idx_pharmacy_inventory_user_id ON public.pharmacy_inventory(user_id);

-- Add a function to automatically update the status based on stock levels
CREATE OR REPLACE FUNCTION update_pharmacy_inventory_status()
RETURNS TRIGGER AS $$
BEGIN
  -- Update status based on stock quantity vs reorder level
  IF NEW.stock_quantity <= COALESCE(NEW.reorder_level, 10) THEN
    NEW.status = 'Low Stock';
  ELSE
    NEW.status = 'In Stock';
  END IF;
  
  -- Update the updated_at timestamp
  NEW.updated_at = now();
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update status
DROP TRIGGER IF EXISTS trigger_update_pharmacy_inventory_status ON public.pharmacy_inventory;
CREATE TRIGGER trigger_update_pharmacy_inventory_status
  BEFORE INSERT OR UPDATE ON public.pharmacy_inventory
  FOR EACH ROW
  EXECUTE FUNCTION update_pharmacy_inventory_status();

-- Add some sample data for testing (optional)
INSERT INTO public.pharmacy_inventory (
  medication_name, 
  stock_quantity, 
  price, 
  expiry_date, 
  generic_name,
  manufacturer,
  category,
  type,
  dosage_form,
  strength,
  unit,
  reorder_level,
  batch_number,
  supplier,
  location,
  description,
  user_id
) VALUES 
(
  'Paracetamol - (Acetaminophen) 500mg tablet by PharmaCorp',
  500,
  0.50,
  '2025-12-31',
  'Acetaminophen',
  'PharmaCorp',
  'Pain Relief',
  'medicine',
  'tablet',
  '500mg',
  'tablets',
  100,
  'PC2024001',
  'MedSupply Co.',
  'Shelf A1',
  'Pain relief and fever reducer',
  (SELECT auth.uid())
),
(
  'Surgical Gloves - Latex-free size M by MedEquip Ltd.',
  50,
  15.00,
  '2026-06-30',
  NULL,
  'MedEquip Ltd.',
  'PPE',
  'medical_supply',
  NULL,
  NULL,
  'boxes',
  10,
  'SG2024001',
  'MedEquip Ltd.',
  'Storage Room B',
  'Latex-free surgical gloves, size M',
  (SELECT auth.uid())
)
ON CONFLICT (id) DO NOTHING; -- Only insert if not already exists

-- Add comment to table
COMMENT ON TABLE public.pharmacy_inventory IS 'Enhanced pharmacy inventory table supporting medicines, medical supplies, equipment, and consumables';

-- Add comments to new columns
COMMENT ON COLUMN public.pharmacy_inventory.type IS 'Product type: medicine, medical_supply, equipment, consumable, other';
COMMENT ON COLUMN public.pharmacy_inventory.category IS 'Product category within its type';
COMMENT ON COLUMN public.pharmacy_inventory.unit IS 'Unit of measurement: tablets, boxes, pieces, kg, etc.';
COMMENT ON COLUMN public.pharmacy_inventory.reorder_level IS 'Stock level at which to reorder';
COMMENT ON COLUMN public.pharmacy_inventory.location IS 'Storage location within the pharmacy';
