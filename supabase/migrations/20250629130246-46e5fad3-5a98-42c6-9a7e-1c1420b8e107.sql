
-- Create workflow status enum
CREATE TYPE public.workflow_status AS ENUM (
  'reception',
  'consultation',
  'laboratory',
  'follow_up_consultation',
  'pharmacy',
  'completed'
);

-- Create patient workflow table to track patient progress through departments
CREATE TABLE public.patient_workflows (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  patient_id UUID REFERENCES public.patients(id) ON DELETE CASCADE,
  user_id UUID NOT NULL,
  current_department workflow_status NOT NULL DEFAULT 'reception',
  previous_department workflow_status,
  assigned_to TEXT, -- staff member assigned in current department
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE(patient_id, user_id)
);

-- Create consultation records table
CREATE TABLE public.consultation_records (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  patient_id UUID REFERENCES public.patients(id) ON DELETE CASCADE,
  workflow_id UUID REFERENCES public.patient_workflows(id) ON DELETE CASCADE,
  user_id UUID NOT NULL,
  doctor_name TEXT NOT NULL,
  diagnosis TEXT,
  treatment_plan TEXT,
  vital_signs JSONB, -- {"blood_pressure": "120/80", "temperature": "37.5", "pulse": "72"}
  symptoms TEXT,
  consultation_date TIMESTAMP WITH TIME ZONE DEFAULT now(),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create laboratory records table
CREATE TABLE public.laboratory_records (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  patient_id UUID REFERENCES public.patients(id) ON DELETE CASCADE,
  workflow_id UUID REFERENCES public.patient_workflows(id) ON DELETE CASCADE,
  user_id UUID NOT NULL,
  technician_name TEXT NOT NULL,
  test_type TEXT NOT NULL,
  test_results JSONB, -- flexible JSON structure for different test results
  reference_ranges JSONB, -- normal ranges for the tests
  lab_notes TEXT,
  test_date TIMESTAMP WITH TIME ZONE DEFAULT now(),
  status TEXT DEFAULT 'pending', -- pending, completed, reviewed
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create pharmacy records table
CREATE TABLE public.pharmacy_records (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  patient_id UUID REFERENCES public.patients(id) ON DELETE CASCADE,
  workflow_id UUID REFERENCES public.patient_workflows(id) ON DELETE CASCADE,
  user_id UUID NOT NULL,
  pharmacist_name TEXT NOT NULL,
  medications JSONB NOT NULL, -- [{"name": "Paracetamol", "dosage": "500mg", "quantity": 20, "instructions": "Twice daily"}]
  total_cost NUMERIC DEFAULT 0,
  dispensed_date TIMESTAMP WITH TIME ZONE DEFAULT now(),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add workflow_id column to existing patients table to track current workflow
ALTER TABLE public.patients ADD COLUMN workflow_id UUID REFERENCES public.patient_workflows(id);

-- Enable RLS on all new tables
ALTER TABLE public.patient_workflows ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.consultation_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.laboratory_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.pharmacy_records ENABLE ROW LEVEL SECURITY;

-- RLS policies for patient_workflows
CREATE POLICY "Users can view their own patient workflows" 
  ON public.patient_workflows FOR SELECT 
  USING (user_id = auth.uid());

CREATE POLICY "Users can create their own patient workflows" 
  ON public.patient_workflows FOR INSERT 
  WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own patient workflows" 
  ON public.patient_workflows FOR UPDATE 
  USING (user_id = auth.uid());

CREATE POLICY "Users can delete their own patient workflows" 
  ON public.patient_workflows FOR DELETE 
  USING (user_id = auth.uid());

-- RLS policies for consultation_records
CREATE POLICY "Users can view their own consultation records" 
  ON public.consultation_records FOR SELECT 
  USING (user_id = auth.uid());

CREATE POLICY "Users can create their own consultation records" 
  ON public.consultation_records FOR INSERT 
  WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own consultation records" 
  ON public.consultation_records FOR UPDATE 
  USING (user_id = auth.uid());

CREATE POLICY "Users can delete their own consultation records" 
  ON public.consultation_records FOR DELETE 
  USING (user_id = auth.uid());

-- RLS policies for laboratory_records
CREATE POLICY "Users can view their own laboratory records" 
  ON public.laboratory_records FOR SELECT 
  USING (user_id = auth.uid());

CREATE POLICY "Users can create their own laboratory records" 
  ON public.laboratory_records FOR INSERT 
  WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own laboratory records" 
  ON public.laboratory_records FOR UPDATE 
  USING (user_id = auth.uid());

CREATE POLICY "Users can delete their own laboratory records" 
  ON public.laboratory_records FOR DELETE 
  USING (user_id = auth.uid());

-- RLS policies for pharmacy_records
CREATE POLICY "Users can view their own pharmacy records" 
  ON public.pharmacy_records FOR SELECT 
  USING (user_id = auth.uid());

CREATE POLICY "Users can create their own pharmacy records" 
  ON public.pharmacy_records FOR INSERT 
  WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own pharmacy records" 
  ON public.pharmacy_records FOR UPDATE 
  USING (user_id = auth.uid());

CREATE POLICY "Users can delete their own pharmacy records" 
  ON public.pharmacy_records FOR DELETE 
  USING (user_id = auth.uid());

-- Function to advance patient workflow to next department
CREATE OR REPLACE FUNCTION public.advance_patient_workflow(
  p_workflow_id UUID,
  p_user_id UUID,
  p_assigned_to TEXT DEFAULT NULL,
  p_notes TEXT DEFAULT NULL
)
RETURNS TABLE(success BOOLEAN, message TEXT, new_department TEXT)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_dept workflow_status;
  next_dept workflow_status;
BEGIN
  -- Get current department
  SELECT current_department INTO current_dept
  FROM public.patient_workflows
  WHERE id = p_workflow_id AND user_id = p_user_id;
  
  IF NOT FOUND THEN
    RETURN QUERY SELECT FALSE, 'Workflow not found', NULL::TEXT;
    RETURN;
  END IF;
  
  -- Determine next department
  CASE current_dept
    WHEN 'reception' THEN next_dept := 'consultation';
    WHEN 'consultation' THEN next_dept := 'laboratory';
    WHEN 'laboratory' THEN next_dept := 'follow_up_consultation';
    WHEN 'follow_up_consultation' THEN next_dept := 'completed';
    WHEN 'pharmacy' THEN next_dept := 'completed';
    ELSE
      RETURN QUERY SELECT FALSE, 'Workflow already completed', current_dept::TEXT;
      RETURN;
  END CASE;
  
  -- Update workflow
  UPDATE public.patient_workflows
  SET 
    previous_department = current_dept,
    current_department = next_dept,
    assigned_to = COALESCE(p_assigned_to, assigned_to),
    notes = COALESCE(p_notes, notes),
    updated_at = now()
  WHERE id = p_workflow_id AND user_id = p_user_id;
  
  RETURN QUERY SELECT TRUE, 'Workflow advanced successfully', next_dept::TEXT;
END;
$$;

-- Function to get workflow summary for a patient
CREATE OR REPLACE FUNCTION public.get_patient_workflow_summary(
  p_patient_id UUID,
  p_user_id UUID
)
RETURNS TABLE(
  workflow_id UUID,
  current_department TEXT,
  previous_department TEXT,
  assigned_to TEXT,
  consultation_count BIGINT,
  lab_test_count BIGINT,
  pharmacy_record_count BIGINT,
  workflow_notes TEXT,
  last_updated TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    pw.id,
    pw.current_department::TEXT,
    pw.previous_department::TEXT,
    pw.assigned_to,
    (SELECT COUNT(*) FROM public.consultation_records WHERE patient_id = p_patient_id AND user_id = p_user_id),
    (SELECT COUNT(*) FROM public.laboratory_records WHERE patient_id = p_patient_id AND user_id = p_user_id),
    (SELECT COUNT(*) FROM public.pharmacy_records WHERE patient_id = p_patient_id AND user_id = p_user_id),
    pw.notes,
    pk.updated_at
  FROM public.patient_workflows pw
  WHERE pw.patient_id = p_patient_id AND pw.user_id = p_user_id;
END;
$$;
