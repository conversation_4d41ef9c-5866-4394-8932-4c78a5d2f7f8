
-- Create demo schema
CREATE SCHEMA IF NOT EXISTS demo;

-- Create demo tables (mirrors of main tables but in demo schema)
CREATE TABLE demo.patients (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  patient_name text NOT NULL,
  email text NOT NULL,
  phone_number text NOT NULL,
  date_of_birth date NOT NULL,
  blood_type text,
  insurance text,
  registration_date date DEFAULT CURRENT_DATE,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

CREATE TABLE demo.doctors (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name text NOT NULL,
  specialty text NOT NULL,
  email text NOT NULL,
  phone_number text NOT NULL,
  qualification text,
  experience integer,
  bio text,
  photo text,
  status text DEFAULT 'active',
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

CREATE TABLE demo.departments (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  department_name text NOT NULL,
  description text,
  department_head text,
  email_address text NOT NULL,
  phone_number text NOT NULL,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

CREATE TABLE demo.appointments (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  patient_name text NOT NULL,
  doctor text NOT NULL,
  department text NOT NULL,
  date_time timestamp without time zone NOT NULL,
  type text,
  status text NOT NULL,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

CREATE TABLE demo.medical_records (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  patient_name text NOT NULL,
  doctor text NOT NULL,
  date date NOT NULL,
  record_type text,
  summary text,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

CREATE TABLE demo.lab_tests (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  patient_name text NOT NULL,
  patient_id text,
  test_name text NOT NULL,
  test_date date NOT NULL,
  status text,
  lab_technician text,
  summary text,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

CREATE TABLE demo.pharmacy_inventory (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  medication_name text NOT NULL,
  stock_quantity integer NOT NULL,
  price numeric NOT NULL,
  expiry_date date NOT NULL,
  status text,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

CREATE TABLE demo.billing_invoices (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  patient text NOT NULL,
  invoice_date date DEFAULT CURRENT_DATE,
  due_date date,
  total_revenue numeric,
  total_expenses numeric,
  net_profit_loss numeric,
  status text,
  invoice_items jsonb,
  notes text,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

CREATE TABLE demo.revenue_entries (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  description text NOT NULL,
  category text NOT NULL,
  amount numeric NOT NULL,
  date date NOT NULL,
  payment_method text NOT NULL,
  notes text,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

CREATE TABLE demo.expense_entries (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  description text NOT NULL,
  category text NOT NULL,
  vendor text NOT NULL,
  amount numeric NOT NULL,
  date date NOT NULL,
  payment_method text NOT NULL,
  notes text,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

-- Insert synthetic data for demo
INSERT INTO demo.departments (department_name, description, department_head, email_address, phone_number) VALUES
('Cardiology', 'Heart and cardiovascular system care', 'Dr. Sarah Johnson', '<EMAIL>', '******-0101'),
('Neurology', 'Brain and nervous system disorders', 'Dr. Michael Chen', '<EMAIL>', '******-0102'),
('Pediatrics', 'Medical care for infants, children, and adolescents', 'Dr. Emily Rodriguez', '<EMAIL>', '******-0103'),
('Emergency Medicine', 'Acute care and emergency treatment', 'Dr. James Wilson', '<EMAIL>', '******-0104'),
('Orthopedics', 'Bone, joint, and muscle disorders', 'Dr. Lisa Thompson', '<EMAIL>', '******-0105');

INSERT INTO demo.doctors (name, specialty, email, phone_number, qualification, experience, bio) VALUES
('Dr. Sarah Johnson', 'Cardiology', '<EMAIL>', '******-1001', 'MD, Cardiology', 15, 'Specialized in interventional cardiology with 15 years of experience.'),
('Dr. Michael Chen', 'Neurology', '<EMAIL>', '******-1002', 'MD, PhD Neurology', 12, 'Expert in neurological disorders and brain imaging.'),
('Dr. Emily Rodriguez', 'Pediatrics', '<EMAIL>', '******-1003', 'MD, Pediatrics', 8, 'Dedicated to providing comprehensive care for children.'),
('Dr. James Wilson', 'Emergency Medicine', '<EMAIL>', '******-1004', 'MD, Emergency Medicine', 10, 'Experienced emergency physician with trauma expertise.'),
('Dr. Lisa Thompson', 'Orthopedics', '<EMAIL>', '******-1005', 'MD, Orthopedic Surgery', 18, 'Specialist in joint replacement and sports medicine.');

INSERT INTO demo.patients (patient_name, email, phone_number, date_of_birth, blood_type, insurance) VALUES
('John Smith', '<EMAIL>', '******-2001', '1985-03-15', 'A+', 'Blue Cross Blue Shield'),
('Maria Garcia', '<EMAIL>', '******-2002', '1992-07-22', 'O-', 'Aetna'),
('Robert Johnson', '<EMAIL>', '******-2003', '1978-11-08', 'B+', 'Cigna'),
('Jennifer Lee', '<EMAIL>', '******-2004', '1989-05-12', 'AB+', 'United Healthcare'),
('David Brown', '<EMAIL>', '******-2005', '1995-09-30', 'A-', 'Medicare'),
('Lisa Wilson', '<EMAIL>', '******-2006', '1987-12-03', 'O+', 'Blue Cross Blue Shield'),
('Michael Davis', '<EMAIL>', '******-2007', '1976-04-18', 'B-', 'Medicaid');

INSERT INTO demo.appointments (patient_name, doctor, department, date_time, type, status) VALUES
('John Smith', 'Dr. Sarah Johnson', 'Cardiology', '2025-01-15 10:00:00', 'Consultation', 'scheduled'),
('Maria Garcia', 'Dr. Emily Rodriguez', 'Pediatrics', '2025-01-15 14:30:00', 'Check-up', 'scheduled'),
('Robert Johnson', 'Dr. Michael Chen', 'Neurology', '2025-01-16 09:15:00', 'Follow-up', 'scheduled'),
('Jennifer Lee', 'Dr. Lisa Thompson', 'Orthopedics', '2025-01-16 11:00:00', 'Consultation', 'scheduled'),
('David Brown', 'Dr. James Wilson', 'Emergency Medicine', '2025-01-14 08:30:00', 'Emergency', 'completed'),
('Lisa Wilson', 'Dr. Sarah Johnson', 'Cardiology', '2025-01-13 15:45:00', 'Follow-up', 'completed');

INSERT INTO demo.medical_records (patient_name, doctor, date, record_type, summary) VALUES
('John Smith', 'Dr. Sarah Johnson', '2025-01-10', 'Consultation', 'Patient presented with chest pain. ECG normal. Recommended stress test.'),
('Maria Garcia', 'Dr. Emily Rodriguez', '2025-01-08', 'Annual Check-up', 'Routine pediatric examination. All vital signs normal. Vaccinations up to date.'),
('David Brown', 'Dr. James Wilson', '2025-01-14', 'Emergency Visit', 'Patient arrived with severe abdominal pain. Diagnosed with appendicitis. Surgery recommended.');

INSERT INTO demo.lab_tests (patient_name, patient_id, test_name, test_date, status, lab_technician, summary) VALUES
('John Smith', 'P001', 'Complete Blood Count', '2025-01-11', 'completed', 'Tech. Amanda Foster', 'All values within normal range'),
('Maria Garcia', 'P002', 'Cholesterol Panel', '2025-01-09', 'completed', 'Tech. Robert Martinez', 'Slightly elevated LDL, recommend dietary changes'),
('Robert Johnson', 'P003', 'MRI Brain', '2025-01-12', 'pending', 'Tech. Sarah Kim', 'Scheduled for January 17th'),
('David Brown', 'P005', 'CT Abdomen', '2025-01-14', 'completed', 'Tech. Michael Wong', 'Confirmed appendicitis, surgery indicated');

INSERT INTO demo.pharmacy_inventory (medication_name, stock_quantity, price, expiry_date, status) VALUES
('Acetaminophen 500mg', 500, 12.99, '2026-12-31', 'In Stock'),
('Amoxicillin 250mg', 300, 24.50, '2025-08-15', 'In Stock'),
('Lisinopril 10mg', 150, 18.75, '2026-03-20', 'In Stock'),
('Metformin 500mg', 200, 22.00, '2025-11-10', 'Low Stock'),
('Ibuprofen 200mg', 75, 15.99, '2025-06-30', 'Low Stock'),
('Atorvastatin 20mg', 400, 35.00, '2026-09-12', 'In Stock');

INSERT INTO demo.billing_invoices (patient, invoice_date, due_date, total_revenue, total_expenses, net_profit_loss, status, notes) VALUES
('John Smith', '2025-01-10', '2025-02-10', 350.00, 75.00, 275.00, 'pending', 'Cardiology consultation and ECG'),
('Maria Garcia', '2025-01-08', '2025-02-08', 150.00, 30.00, 120.00, 'paid', 'Pediatric check-up'),
('David Brown', '2025-01-14', '2025-02-14', 2500.00, 800.00, 1700.00, 'pending', 'Emergency appendectomy');

INSERT INTO demo.revenue_entries (description, category, amount, date, payment_method) VALUES
('Patient Consultation Fees', 'Medical Services', 1250.00, '2025-01-14', 'Credit Card'),
('Laboratory Test Fees', 'Diagnostic Services', 800.00, '2025-01-14', 'Insurance'),
('Pharmacy Sales', 'Medication Sales', 450.00, '2025-01-14', 'Cash'),
('Surgery Fees', 'Surgical Services', 2500.00, '2025-01-14', 'Insurance');

INSERT INTO demo.expense_entries (description, category, vendor, amount, date, payment_method) VALUES
('Medical Supplies', 'Supplies', 'MedSupply Corp', 1200.00, '2025-01-14', 'Check'),
('Equipment Maintenance', 'Maintenance', 'TechCare Inc', 350.00, '2025-01-13', 'Credit Card'),
('Pharmaceutical Inventory', 'Medication', 'PharmaCorp', 2800.00, '2025-01-12', 'Wire Transfer'),
('Utilities', 'Utilities', 'City Power Company', 450.00, '2025-01-10', 'Auto-Pay');

-- Create function to get demo dashboard stats
CREATE OR REPLACE FUNCTION demo.get_dashboard_stats()
RETURNS TABLE(total_patients bigint, today_appointments bigint, total_doctors bigint, total_departments bigint, total_revenue numeric, total_expenses numeric, occupancy_rate numeric)
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
BEGIN
  RETURN QUERY
  SELECT 
    (SELECT COUNT(*) FROM demo.patients),
    (SELECT COUNT(*) FROM demo.appointments WHERE date_time::date = CURRENT_DATE),
    (SELECT COUNT(*) FROM demo.doctors),
    (SELECT COUNT(*) FROM demo.departments),
    (SELECT COALESCE(SUM(amount), 0) FROM demo.revenue_entries),
    (SELECT COALESCE(SUM(amount), 0) FROM demo.expense_entries),
    -- Simple occupancy rate calculation
    CASE 
      WHEN (SELECT COUNT(*) FROM demo.appointments WHERE date_time::date = CURRENT_DATE) > 0 
      THEN 78.5  -- Demo occupancy rate
      ELSE 0.0 
    END;
END;
$function$;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA demo TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL TABLES IN SCHEMA demo TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA demo TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA demo TO postgres, anon, authenticated, service_role;
