-- Add prescribed_medicines column to consultation_records table
-- This migration adds the missing prescribed_medicines column that is referenced in the application code

-- Add prescribed_medicines column to consultation_records table
ALTER TABLE public.consultation_records 
ADD COLUMN IF NOT EXISTS prescribed_medicines JSONB;

-- Add comment to explain the column structure
COMMENT ON COLUMN public.consultation_records.prescribed_medicines IS 'Array of prescribed medicines in JSON format: [{"name": "Medicine Name", "dosage": "500mg", "frequency": "Twice daily", "duration": "7 days", "instructions": "Take after meals"}]';

-- Create index for better performance when querying prescribed medicines
CREATE INDEX IF NOT EXISTS idx_consultation_records_prescribed_medicines ON public.consultation_records USING GIN (prescribed_medicines);

-- Update existing consultation records to have empty array for prescribed_medicines if null
UPDATE public.consultation_records 
SET prescribed_medicines = '[]'::jsonb 
WHERE prescribed_medicines IS NULL;
