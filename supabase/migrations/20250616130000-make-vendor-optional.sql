-- Make vendor field optional in expense_entries table
-- This migration alters the existing table to allow NULL values in the vendor column

-- First, check if the table exists and alter the vendor column to allow NULL
DO $$
BEGIN
    -- Check if the expense_entries table exists
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'expense_entries') THEN
        -- Check if vendor column exists and is NOT NULL
        IF EXISTS (
            SELECT FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'expense_entries' 
            AND column_name = 'vendor' 
            AND is_nullable = 'NO'
        ) THEN
            -- Alter the vendor column to allow NULL values
            ALTER TABLE public.expense_entries ALTER COLUMN vendor DROP NOT NULL;
            RAISE NOTICE 'Successfully made vendor column nullable in expense_entries table';
        ELSE
            RAISE NOTICE 'Vendor column is already nullable or does not exist';
        END IF;
    ELSE
        RAISE NOTICE 'expense_entries table does not exist yet';
    END IF;
END $$;
