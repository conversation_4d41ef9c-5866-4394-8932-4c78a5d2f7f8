-- Add lab_tests_required column to consultation_records table
-- This migration adds the missing lab_tests_required column that is referenced in the ConsultationForm

-- Add lab_tests_required column to consultation_records table
ALTER TABLE public.consultation_records 
ADD COLUMN IF NOT EXISTS lab_tests_required BOOLEAN DEFAULT false;

-- Add comment to explain the column
COMMENT ON COLUMN public.consultation_records.lab_tests_required IS 'Indicates whether laboratory tests are required for this consultation';

-- Create index for better performance when querying lab tests requirement
CREATE INDEX IF NOT EXISTS idx_consultation_records_lab_tests_required ON public.consultation_records(lab_tests_required);

-- Update existing consultation records to set lab_tests_required based on whether they have associated lab records
UPDATE public.consultation_records 
SET lab_tests_required = true 
WHERE id IN (
  SELECT DISTINCT c.id 
  FROM public.consultation_records c
  INNER JOIN public.laboratory_records l ON c.patient_id = l.patient_id
  WHERE c.lab_tests_required IS NULL
);

-- Set remaining records to false (no lab tests required)
UPDATE public.consultation_records 
SET lab_tests_required = false 
WHERE lab_tests_required IS NULL;
