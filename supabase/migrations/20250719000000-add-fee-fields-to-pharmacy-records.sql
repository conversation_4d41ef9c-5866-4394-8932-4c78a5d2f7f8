-- Add fee fields to pharmacy_records table
-- This migration adds fee tracking fields to the pharmacy_records table

-- Add fee fields to pharmacy_records table
ALTER TABLE public.pharmacy_records 
ADD COLUMN IF NOT EXISTS fee_amount DECIMAL(10,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS fee_paid BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS fee_notes TEXT;

-- Create indexes for better performance on fee queries
CREATE INDEX IF NOT EXISTS idx_pharmacy_records_fee_paid ON public.pharmacy_records(fee_paid);

-- Update existing records to have default fee values (safe operation)
UPDATE public.pharmacy_records 
SET fee_amount = 0.00, fee_paid = false 
WHERE fee_amount IS NULL;

-- Add comments to document the fee fields
COMMENT ON COLUMN public.pharmacy_records.fee_amount IS 'Pharmacy service fee amount in the system currency';
COMMENT ON COLUMN public.pharmacy_records.fee_paid IS 'Whether the pharmacy service fee has been paid';
COMMENT ON COLUMN public.pharmacy_records.fee_notes IS 'Additional notes about the pharmacy fee payment';
