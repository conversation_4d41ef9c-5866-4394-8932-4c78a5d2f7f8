-- Enhanced Settings System Migration
-- This migration enhances the settings table with comprehensive configuration options

-- Drop existing settings table if needed and recreate with enhanced structure
DROP TABLE IF EXISTS public.settings CASCADE;

-- Create comprehensive settings table
CREATE TABLE public.settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  
  -- General Hospital Information
  hospital_name TEXT,
  hospital_code TEXT,
  address TEXT,
  city TEXT,
  state TEXT,
  postal_code TEXT,
  country TEXT DEFAULT 'Kenya',
  phone_number TEXT,
  email TEXT,
  website TEXT,
  logo_url TEXT,
  
  -- Currency and Localization
  currency TEXT DEFAULT 'KES',
  timezone TEXT DEFAULT 'Africa/Nairobi',
  date_format TEXT DEFAULT 'DD/MM/YYYY',
  time_format TEXT DEFAULT '24h',
  language TEXT DEFAULT 'en',
  
  -- Business Settings
  business_hours_start TIME DEFAULT '08:00',
  business_hours_end TIME DEFAULT '18:00',
  working_days TEXT[] DEFAULT ARRAY['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
  appointment_duration INTEGER DEFAULT 30, -- minutes
  max_appointments_per_day INTEGER DEFAULT 50,
  
  -- Financial Settings
  tax_rate DECIMAL(5,2) DEFAULT 0.00,
  tax_name TEXT DEFAULT 'VAT',
  invoice_prefix TEXT DEFAULT 'INV',
  receipt_prefix TEXT DEFAULT 'RCP',
  auto_invoice_numbering BOOLEAN DEFAULT true,
  payment_terms_days INTEGER DEFAULT 30,
  
  -- Notification Settings
  email_notifications BOOLEAN DEFAULT true,
  sms_notifications BOOLEAN DEFAULT false,
  appointment_reminders BOOLEAN DEFAULT true,
  reminder_hours_before INTEGER DEFAULT 24,
  low_stock_alerts BOOLEAN DEFAULT true,
  low_stock_threshold INTEGER DEFAULT 10,
  
  -- Security Settings
  session_timeout_minutes INTEGER DEFAULT 480, -- 8 hours
  password_expiry_days INTEGER DEFAULT 90,
  max_login_attempts INTEGER DEFAULT 5,
  two_factor_auth BOOLEAN DEFAULT false,
  
  -- Pharmacy Settings
  pharmacy_enabled BOOLEAN DEFAULT true,
  auto_deduct_inventory BOOLEAN DEFAULT true,
  require_prescription BOOLEAN DEFAULT false,
  pharmacy_markup_percentage DECIMAL(5,2) DEFAULT 0.00,
  
  -- Laboratory Settings
  lab_enabled BOOLEAN DEFAULT true,
  auto_generate_lab_numbers BOOLEAN DEFAULT true,
  lab_result_approval_required BOOLEAN DEFAULT true,
  lab_report_template TEXT,
  
  -- Consultation Settings
  consultation_enabled BOOLEAN DEFAULT true,
  require_appointment BOOLEAN DEFAULT false,
  allow_walk_ins BOOLEAN DEFAULT true,
  consultation_fee DECIMAL(10,2) DEFAULT 0.00,
  
  -- Backup and Data Settings
  auto_backup_enabled BOOLEAN DEFAULT false,
  backup_frequency TEXT DEFAULT 'weekly', -- daily, weekly, monthly
  data_retention_months INTEGER DEFAULT 60, -- 5 years
  
  -- Integration Settings
  paystack_public_key TEXT,
  paystack_secret_key TEXT,
  sms_api_key TEXT,
  email_smtp_host TEXT,
  email_smtp_port INTEGER DEFAULT 587,
  email_smtp_username TEXT,
  email_smtp_password TEXT,
  
  -- Custom Fields (JSON for flexibility)
  custom_fields JSONB DEFAULT '{}',
  
  -- Audit Fields
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  
  -- Ensure one settings record per user
  UNIQUE(user_id)
);

-- Create indexes for better performance
CREATE INDEX idx_settings_user_id ON public.settings(user_id);
CREATE INDEX idx_settings_hospital_name ON public.settings(hospital_name);
CREATE INDEX idx_settings_currency ON public.settings(currency);

-- Enable RLS
ALTER TABLE public.settings ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view their own settings"
  ON public.settings
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own settings"
  ON public.settings
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own settings"
  ON public.settings
  FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own settings"
  ON public.settings
  FOR DELETE
  USING (auth.uid() = user_id);

-- Create function to automatically update updated_at
CREATE OR REPLACE FUNCTION update_settings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updated_at
CREATE TRIGGER trigger_update_settings_updated_at
  BEFORE UPDATE ON public.settings
  FOR EACH ROW
  EXECUTE FUNCTION update_settings_updated_at();

-- Create function to initialize default settings for new users
CREATE OR REPLACE FUNCTION create_default_settings()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.settings (user_id, hospital_name)
  VALUES (NEW.id, 'My Hospital')
  ON CONFLICT (user_id) DO NOTHING;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-create settings when user signs up
CREATE TRIGGER trigger_create_default_settings
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION create_default_settings();

-- Insert default settings for existing users (if any)
INSERT INTO public.settings (user_id, hospital_name)
SELECT id, 'My Hospital'
FROM auth.users
WHERE id NOT IN (SELECT user_id FROM public.settings)
ON CONFLICT (user_id) DO NOTHING;
