-- Add Patient Workflow Status Tracking
-- This migration adds workflow status tracking to patients table

-- Add workflow status columns to patients table
ALTER TABLE public.patients 
ADD COLUMN IF NOT EXISTS workflow_status TEXT DEFAULT 'registered',
ADD COLUMN IF NOT EXISTS sent_to_consultation_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS consultation_completed_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS sent_to_laboratory_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS laboratory_completed_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS workflow_completed_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS last_status_update TIMESTAMP WITH TIME ZONE DEFAULT now();

-- Create enum for workflow status if it doesn't exist
DO $$ BEGIN
    CREATE TYPE workflow_status_enum AS ENUM (
        'registered',           -- Just registered, visible in reception
        'sent_to_consultation', -- Sent to consultation, hidden from reception after 24h
        'in_consultation',      -- Currently in consultation
        'consultation_completed', -- Consultation done, ready for lab
        'sent_to_laboratory',   -- Sent to laboratory
        'in_laboratory',        -- Currently in laboratory
        'laboratory_completed', -- Laboratory done, workflow complete
        'workflow_completed'    -- Full workflow completed
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Update the workflow_status column to use the enum
ALTER TABLE public.patients 
ALTER COLUMN workflow_status TYPE workflow_status_enum 
USING workflow_status::workflow_status_enum;

-- Create index for workflow status queries
CREATE INDEX IF NOT EXISTS idx_patients_workflow_status ON public.patients(workflow_status);
CREATE INDEX IF NOT EXISTS idx_patients_sent_to_consultation_at ON public.patients(sent_to_consultation_at);
CREATE INDEX IF NOT EXISTS idx_patients_last_status_update ON public.patients(last_status_update);

-- Create function to update workflow status
CREATE OR REPLACE FUNCTION update_patient_workflow_status(
    patient_id UUID,
    new_status workflow_status_enum
) RETURNS void AS $$
BEGIN
    UPDATE public.patients 
    SET 
        workflow_status = new_status,
        last_status_update = now(),
        sent_to_consultation_at = CASE 
            WHEN new_status = 'sent_to_consultation' THEN now() 
            ELSE sent_to_consultation_at 
        END,
        consultation_completed_at = CASE 
            WHEN new_status = 'consultation_completed' THEN now() 
            ELSE consultation_completed_at 
        END,
        sent_to_laboratory_at = CASE 
            WHEN new_status = 'sent_to_laboratory' THEN now() 
            ELSE sent_to_laboratory_at 
        END,
        laboratory_completed_at = CASE 
            WHEN new_status = 'laboratory_completed' THEN now() 
            ELSE laboratory_completed_at 
        END,
        workflow_completed_at = CASE 
            WHEN new_status = 'workflow_completed' THEN now() 
            ELSE workflow_completed_at 
        END
    WHERE id = patient_id;
END;
$$ LANGUAGE plpgsql;

-- Create function to automatically update workflow status based on records
CREATE OR REPLACE FUNCTION auto_update_workflow_status() RETURNS TRIGGER AS $$
BEGIN
    -- When consultation record is created, update patient status
    IF TG_TABLE_NAME = 'consultation_records' AND TG_OP = 'INSERT' THEN
        PERFORM update_patient_workflow_status(NEW.patient_id, 'consultation_completed');
        RETURN NEW;
    END IF;
    
    -- When laboratory record is created, update patient status
    IF TG_TABLE_NAME = 'laboratory_records' AND TG_OP = 'INSERT' THEN
        PERFORM update_patient_workflow_status(NEW.patient_id, 'laboratory_completed');
        RETURN NEW;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for automatic workflow status updates
DROP TRIGGER IF EXISTS trigger_consultation_workflow_update ON public.consultation_records;
CREATE TRIGGER trigger_consultation_workflow_update
    AFTER INSERT ON public.consultation_records
    FOR EACH ROW
    EXECUTE FUNCTION auto_update_workflow_status();

DROP TRIGGER IF EXISTS trigger_laboratory_workflow_update ON public.laboratory_records;
CREATE TRIGGER trigger_laboratory_workflow_update
    AFTER INSERT ON public.laboratory_records
    FOR EACH ROW
    EXECUTE FUNCTION auto_update_workflow_status();

-- Create function to get patients visible in reception (excluding those sent to consultation > 24h ago)
CREATE OR REPLACE FUNCTION get_reception_visible_patients()
RETURNS TABLE (
    id UUID,
    patient_name TEXT,
    email TEXT,
    phone_number TEXT,
    date_of_birth DATE,
    blood_type TEXT,
    insurance TEXT,
    emergency_contact TEXT,
    notes TEXT,
    registration_date DATE,
    workflow_status workflow_status_enum,
    sent_to_consultation_at TIMESTAMP WITH TIME ZONE,
    last_status_update TIMESTAMP WITH TIME ZONE,
    hours_since_sent NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.patient_name,
        p.email,
        p.phone_number,
        p.date_of_birth,
        p.blood_type,
        p.insurance,
        p.emergency_contact,
        p.notes,
        p.registration_date,
        p.workflow_status,
        p.sent_to_consultation_at,
        p.last_status_update,
        CASE 
            WHEN p.sent_to_consultation_at IS NOT NULL 
            THEN EXTRACT(EPOCH FROM (now() - p.sent_to_consultation_at)) / 3600
            ELSE NULL 
        END as hours_since_sent
    FROM public.patients p
    WHERE 
        -- Show registered patients
        p.workflow_status = 'registered'
        OR 
        -- Show patients sent to consultation within last 24 hours
        (
            p.workflow_status = 'sent_to_consultation' 
            AND p.sent_to_consultation_at IS NOT NULL 
            AND p.sent_to_consultation_at > (now() - INTERVAL '24 hours')
        )
    ORDER BY p.registration_date DESC, p.last_status_update DESC;
END;
$$ LANGUAGE plpgsql;

-- Update existing patients to have proper workflow status
UPDATE public.patients 
SET 
    workflow_status = CASE 
        WHEN id IN (
            SELECT DISTINCT patient_id 
            FROM public.laboratory_records 
            WHERE patient_id IS NOT NULL
        ) THEN 'laboratory_completed'::workflow_status_enum
        WHEN id IN (
            SELECT DISTINCT patient_id 
            FROM public.consultation_records 
            WHERE patient_id IS NOT NULL
        ) THEN 'consultation_completed'::workflow_status_enum
        ELSE 'registered'::workflow_status_enum
    END,
    last_status_update = now()
WHERE workflow_status IS NULL OR workflow_status = 'registered';

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION update_patient_workflow_status(UUID, workflow_status_enum) TO authenticated;
GRANT EXECUTE ON FUNCTION get_reception_visible_patients() TO authenticated;
