-- Create the main doctors table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.doctors (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name text NOT NULL,
  specialty text NOT NULL,
  email text NOT NULL,
  phone_number text NOT NULL,
  qualification text,
  experience integer,
  bio text,
  photo text,
  status text DEFAULT 'active',
  user_id uuid REFERENCES auth.users,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

-- Create the main departments table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.departments (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  department_name text NOT NULL,
  description text,
  department_head text,
  email_address text NOT NULL,
  phone_number text NOT NULL,
  user_id uuid REFERENCES auth.users,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

-- Create the main settings table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.settings (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  hospital_name text,
  address text,
  phone_number text,
  email text,
  currency text DEFAULT 'KES',
  logo_url text,
  user_id uuid REFERENCES auth.users,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

-- Create the main revenue_entries table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.revenue_entries (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  description text NOT NULL,
  amount numeric NOT NULL,
  date date NOT NULL,
  category text NOT NULL,
  payment_method text NOT NULL,
  notes text,
  user_id uuid REFERENCES auth.users,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

-- Create the main expense_entries table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.expense_entries (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  description text NOT NULL,
  amount numeric NOT NULL,
  date date NOT NULL,
  category text NOT NULL,
  vendor text, -- Made nullable/optional
  payment_method text NOT NULL,
  notes text,
  user_id uuid REFERENCES auth.users,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

-- Create the main pharmacy_inventory table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.pharmacy_inventory (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  medication_name text NOT NULL,
  stock_quantity integer NOT NULL,
  price numeric NOT NULL,
  expiry_date date NOT NULL,
  status text,
  user_id uuid REFERENCES auth.users,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

-- Create the main appointments table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.appointments (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  patient_name text NOT NULL,
  doctor text NOT NULL,
  department text, -- Made nullable/optional
  date_time timestamp without time zone NOT NULL,
  type text,
  status text NOT NULL,
  user_id uuid REFERENCES auth.users,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

-- Add RLS policies for the revenue_entries table
ALTER TABLE public.revenue_entries ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own revenue entries"
  ON public.revenue_entries
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own revenue entries"
  ON public.revenue_entries
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own revenue entries"
  ON public.revenue_entries
  FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own revenue entries"
  ON public.revenue_entries
  FOR DELETE
  USING (auth.uid() = user_id);

-- Add RLS policies for the expense_entries table
ALTER TABLE public.expense_entries ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own expense entries"
  ON public.expense_entries
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own expense entries"
  ON public.expense_entries
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own expense entries"
  ON public.expense_entries
  FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own expense entries"
  ON public.expense_entries
  FOR DELETE
  USING (auth.uid() = user_id);

-- Add RLS policies for the settings table
ALTER TABLE public.settings ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own settings"
  ON public.settings
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own settings"
  ON public.settings
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own settings"
  ON public.settings
  FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own settings"
  ON public.settings
  FOR DELETE
  USING (auth.uid() = user_id);

-- Add RLS policies for the pharmacy_inventory table
ALTER TABLE public.pharmacy_inventory ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own pharmacy inventory"
  ON public.pharmacy_inventory
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own pharmacy inventory"
  ON public.pharmacy_inventory
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own pharmacy inventory"
  ON public.pharmacy_inventory
  FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own pharmacy inventory"
  ON public.pharmacy_inventory
  FOR DELETE
  USING (auth.uid() = user_id);

-- Add RLS policies for the doctors table
ALTER TABLE public.doctors ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own doctors"
  ON public.doctors
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own doctors"
  ON public.doctors
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own doctors"
  ON public.doctors
  FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own doctors"
  ON public.doctors
  FOR DELETE
  USING (auth.uid() = user_id);

-- Add RLS policies for the departments table
ALTER TABLE public.departments ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own departments"
  ON public.departments
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own departments"
  ON public.departments
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own departments"
  ON public.departments
  FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own departments"
  ON public.departments
  FOR DELETE
  USING (auth.uid() = user_id);

-- Add RLS policies for the appointments table to ensure users can see their own data
ALTER TABLE public.appointments ENABLE ROW LEVEL SECURITY;

-- Create policy that allows users to SELECT their own appointments
CREATE POLICY "Users can view their own appointments"
  ON public.appointments
  FOR SELECT
  USING (auth.uid() = user_id);

-- Create policy that allows users to INSERT their own appointments
CREATE POLICY "Users can create their own appointments"
  ON public.appointments
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Create policy that allows users to UPDATE their own appointments
CREATE POLICY "Users can update their own appointments"
  ON public.appointments
  FOR UPDATE
  USING (auth.uid() = user_id);

-- Create policy that allows users to DELETE their own appointments
CREATE POLICY "Users can delete their own appointments"
  ON public.appointments
  FOR DELETE
  USING (auth.uid() = user_id);
