-- Update the reception visible patients function to include fee fields
-- This migration updates the get_reception_visible_patients function to include fee information

-- Update the reception visible patients function to include fee fields
CREATE OR REPLACE FUNCTION get_reception_visible_patients()
RETURNS TABLE (
    id UUID,
    patient_name TEXT,
    email TEXT,
    phone_number TEXT,
    date_of_birth DATE,
    blood_type TEXT,
    insurance TEXT,
    emergency_contact TEXT,
    notes TEXT,
    registration_date DATE,
    workflow_status workflow_status_enum,
    sent_to_consultation_at TIMESTAMP WITH TIME ZONE,
    last_status_update TIMESTAMP WITH TIME ZONE,
    hours_since_sent NUMERIC,
    department_id UUID,
    department_name TEXT,
    department_color TEXT,
    department_icon TEXT,
    department_notes TEXT,
    fee_amount DECIMAL(10,2),
    fee_paid BOOLEAN,
    fee_notes TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.patient_name,
        p.email,
        p.phone_number,
        p.date_of_birth,
        p.blood_type,
        p.insurance,
        p.emergency_contact,
        p.notes,
        p.registration_date,
        p.workflow_status,
        p.sent_to_consultation_at,
        p.last_status_update,
        CASE 
            WHEN p.sent_to_consultation_at IS NOT NULL 
            THEN EXTRACT(EPOCH FROM (now() - p.sent_to_consultation_at)) / 3600
            ELSE NULL 
        END as hours_since_sent,
        p.department_id,
        d.name as department_name,
        d.color as department_color,
        d.icon as department_icon,
        p.department_notes,
        p.fee_amount,
        p.fee_paid,
        p.fee_notes
    FROM public.patients p
    LEFT JOIN public.medical_departments d ON p.department_id = d.id
    WHERE 
        -- Show registered patients
        p.workflow_status = 'registered'
        OR 
        -- Show patients sent to consultation within last 24 hours
        (
            p.workflow_status = 'sent_to_consultation' 
            AND p.sent_to_consultation_at IS NOT NULL 
            AND p.sent_to_consultation_at > (now() - INTERVAL '24 hours')
        )
    ORDER BY p.registration_date DESC, p.last_status_update DESC;
END;
$$ LANGUAGE plpgsql;

-- Also update the get_patients_with_departments function to include fee fields
CREATE OR REPLACE FUNCTION get_patients_with_departments(user_uuid UUID)
RETURNS TABLE (
    id UUID,
    patient_name TEXT,
    email TEXT,
    phone_number TEXT,
    date_of_birth DATE,
    blood_type TEXT,
    insurance TEXT,
    emergency_contact TEXT,
    notes TEXT,
    registration_date DATE,
    workflow_status workflow_status_enum,
    sent_to_consultation_at TIMESTAMP WITH TIME ZONE,
    last_status_update TIMESTAMP WITH TIME ZONE,
    department_id UUID,
    department_name TEXT,
    department_color TEXT,
    department_icon TEXT,
    department_notes TEXT,
    fee_amount DECIMAL(10,2),
    fee_paid BOOLEAN,
    fee_notes TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.patient_name,
        p.email,
        p.phone_number,
        p.date_of_birth,
        p.blood_type,
        p.insurance,
        p.emergency_contact,
        p.notes,
        p.registration_date,
        p.workflow_status,
        p.sent_to_consultation_at,
        p.last_status_update,
        p.department_id,
        d.name as department_name,
        d.color as department_color,
        d.icon as department_icon,
        p.department_notes,
        p.fee_amount,
        p.fee_paid,
        p.fee_notes
    FROM public.patients p
    LEFT JOIN public.medical_departments d ON p.department_id = d.id
    WHERE p.user_id = user_uuid
    ORDER BY p.registration_date DESC, p.last_status_update DESC;
END;
$$ LANGUAGE plpgsql;
