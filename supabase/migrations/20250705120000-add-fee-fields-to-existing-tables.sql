-- Add fee fields to existing tables without affecting current data
-- This migration safely adds fee fields to your existing hospital management tables

-- Add fee fields to patients table (registration fees)
ALTER TABLE public.patients 
ADD COLUMN IF NOT EXISTS fee_amount DECIMAL(10,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS fee_paid BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS fee_notes TEXT;

-- Add fee fields to consultation_records table (consultation fees)
ALTER TABLE public.consultation_records 
ADD COLUMN IF NOT EXISTS fee_amount DECIMAL(10,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS fee_paid BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS fee_notes TEXT;

-- Add fee fields to laboratory_records table (laboratory fees)
ALTER TABLE public.laboratory_records 
ADD COLUMN IF NOT EXISTS fee_amount DECIMAL(10,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS fee_paid BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS fee_notes TEXT;

-- Add missing time fields to laboratory_records if they don't exist
ALTER TABLE public.laboratory_records 
ADD COLUMN IF NOT EXISTS start_time TIME,
ADD COLUMN IF NOT EXISTS end_time TIME;

-- Add consultation_type field to distinguish initial vs follow-up consultations
ALTER TABLE public.consultation_records 
ADD COLUMN IF NOT EXISTS consultation_type TEXT DEFAULT 'initial';

-- Add prescribed_medicines fields if they don't exist
ALTER TABLE public.consultation_records 
ADD COLUMN IF NOT EXISTS prescribed_medicines JSONB DEFAULT '[]'::jsonb;

ALTER TABLE public.laboratory_records 
ADD COLUMN IF NOT EXISTS prescribed_medicines JSONB DEFAULT '[]'::jsonb;

-- Create indexes for better performance on fee queries
CREATE INDEX IF NOT EXISTS idx_patients_fee_paid ON public.patients(fee_paid);
CREATE INDEX IF NOT EXISTS idx_consultation_records_fee_paid ON public.consultation_records(fee_paid);
CREATE INDEX IF NOT EXISTS idx_laboratory_records_fee_paid ON public.laboratory_records(fee_paid);
CREATE INDEX IF NOT EXISTS idx_consultation_records_consultation_type ON public.consultation_records(consultation_type);

-- Update existing records to have default fee values (safe operation)
UPDATE public.patients 
SET fee_amount = 0.00, fee_paid = false 
WHERE fee_amount IS NULL;

UPDATE public.consultation_records 
SET fee_amount = 0.00, fee_paid = false 
WHERE fee_amount IS NULL;

UPDATE public.laboratory_records 
SET fee_amount = 0.00, fee_paid = false 
WHERE fee_amount IS NULL;

-- Add comments to document the fee fields
COMMENT ON COLUMN public.patients.fee_amount IS 'Registration fee amount in the system currency';
COMMENT ON COLUMN public.patients.fee_paid IS 'Whether the registration fee has been paid';
COMMENT ON COLUMN public.patients.fee_notes IS 'Additional notes about the registration fee payment';

COMMENT ON COLUMN public.consultation_records.fee_amount IS 'Consultation fee amount in the system currency';
COMMENT ON COLUMN public.consultation_records.fee_paid IS 'Whether the consultation fee has been paid';
COMMENT ON COLUMN public.consultation_records.fee_notes IS 'Additional notes about the consultation fee payment';

COMMENT ON COLUMN public.laboratory_records.fee_amount IS 'Laboratory test fee amount in the system currency';
COMMENT ON COLUMN public.laboratory_records.fee_paid IS 'Whether the laboratory fee has been paid';
COMMENT ON COLUMN public.laboratory_records.fee_notes IS 'Additional notes about the laboratory fee payment';
