-- Add Follow-up Consultation to Workflow
-- This migration adds a follow-up consultation step after laboratory completion

-- First, add the new enum value to workflow_status
ALTER TYPE public.workflow_status ADD VALUE IF NOT EXISTS 'follow_up_consultation';

-- Update the advance_patient_workflow function to handle the new flow
CREATE OR REPLACE FUNCTION public.advance_patient_workflow(
  p_workflow_id UUID,
  p_user_id UUID,
  p_assigned_to TEXT DEFAULT NULL,
  p_notes TEXT DEFAULT NULL
)
RETURNS TABLE(success BOOLEAN, message TEXT, new_department TEXT)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_dept workflow_status;
  next_dept workflow_status;
BEGIN
  -- Get current department
  SELECT current_department INTO current_dept
  FROM public.patient_workflows
  WHERE id = p_workflow_id AND user_id = p_user_id;
  
  IF NOT FOUND THEN
    RETURN QUERY SELECT FALSE, 'Workflow not found', NULL::TEXT;
    RETURN;
  END IF;
  
  -- Determine next department with new follow-up consultation flow
  CASE current_dept
    WHEN 'reception' THEN next_dept := 'consultation';
    WHEN 'consultation' THEN next_dept := 'laboratory';
    WHEN 'laboratory' THEN next_dept := 'follow_up_consultation';
    WHEN 'follow_up_consultation' THEN next_dept := 'completed';
    WHEN 'pharmacy' THEN next_dept := 'completed';
    ELSE 
      RETURN QUERY SELECT FALSE, 'Workflow already completed', current_dept::TEXT;
      RETURN;
  END CASE;
  
  -- Update workflow
  UPDATE public.patient_workflows
  SET 
    previous_department = current_dept,
    current_department = next_dept,
    assigned_to = COALESCE(p_assigned_to, assigned_to),
    notes = COALESCE(p_notes, notes),
    updated_at = now()
  WHERE id = p_workflow_id AND user_id = p_user_id;
  
  RETURN QUERY SELECT TRUE, 'Workflow advanced successfully', next_dept::TEXT;
END;
$$;

-- Create a function to check if a patient needs follow-up consultation
CREATE OR REPLACE FUNCTION public.needs_follow_up_consultation(
  p_patient_id UUID,
  p_user_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  has_lab_record BOOLEAN := FALSE;
  has_follow_up_record BOOLEAN := FALSE;
BEGIN
  -- Check if patient has laboratory record
  SELECT EXISTS(
    SELECT 1 FROM public.laboratory_records 
    WHERE patient_id = p_patient_id AND user_id = p_user_id
  ) INTO has_lab_record;
  
  -- Check if patient already has follow-up consultation record
  SELECT EXISTS(
    SELECT 1 FROM public.consultation_records 
    WHERE patient_id = p_patient_id AND user_id = p_user_id 
    AND consultation_type = 'follow_up'
  ) INTO has_follow_up_record;
  
  -- Needs follow-up if has lab record but no follow-up consultation yet
  RETURN has_lab_record AND NOT has_follow_up_record;
END;
$$;

-- Add consultation_type column to consultation_records if it doesn't exist
ALTER TABLE public.consultation_records 
ADD COLUMN IF NOT EXISTS consultation_type TEXT DEFAULT 'initial';

-- Create index for consultation type queries
CREATE INDEX IF NOT EXISTS idx_consultation_records_type ON public.consultation_records(consultation_type);

-- Update existing consultation records to mark them as initial
UPDATE public.consultation_records 
SET consultation_type = 'initial' 
WHERE consultation_type IS NULL;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.needs_follow_up_consultation(UUID, UUID) TO authenticated;

-- Add comments for documentation
COMMENT ON FUNCTION public.advance_patient_workflow IS 'Advances patient workflow with new follow-up consultation step after laboratory';
COMMENT ON FUNCTION public.needs_follow_up_consultation IS 'Checks if a patient needs follow-up consultation after laboratory tests';
COMMENT ON COLUMN public.consultation_records.consultation_type IS 'Type of consultation: initial or follow_up';
