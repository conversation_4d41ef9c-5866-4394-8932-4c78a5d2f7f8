import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
  validatePatientData,
  validateConsultationData,
  validateLaboratoryData,
  validatePharmacyData,
  validateWorkflowTransition,
  validateInventoryAvailability
} from '../utils/workflowValidation';

describe('Workflow Validation', () => {
  describe('Patient Data Validation', () => {
    it('should validate correct patient data', () => {
      const validPatient = {
        patient_name: '<PERSON>',
        email: '<EMAIL>',
        phone_number: '+**********',
        date_of_birth: '1990-01-01',
        gender: 'male',
        blood_type: 'O+'
      };

      const result = validatePatientData(validPatient);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should validate patient data without email', () => {
      const validPatientNoEmail = {
        patient_name: '<PERSON>',
        phone_number: '+**********',
        date_of_birth: '1990-01-01',
        gender: 'female'
      };

      const result = validatePatientData(validPatientNoEmail);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject invalid email when provided', () => {
      const invalidPatient = {
        patient_name: 'John Doe',
        email: 'invalid-email',
        phone_number: '+**********',
        date_of_birth: '1990-01-01'
      };

      const result = validatePatientData(invalidPatient);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Please enter a valid email address');
    });

    it('should reject future birth date', () => {
      const futureDate = new Date();
      futureDate.setFullYear(futureDate.getFullYear() + 1);
      
      const invalidPatient = {
        patient_name: 'John Doe',
        email: '<EMAIL>',
        phone_number: '+**********',
        date_of_birth: futureDate.toISOString().split('T')[0]
      };

      const result = validatePatientData(invalidPatient);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Date of birth cannot be in the future');
    });

    it('should warn for very old patients', () => {
      const oldDate = new Date();
      oldDate.setFullYear(oldDate.getFullYear() - 105);
      
      const oldPatient = {
        patient_name: 'John Doe',
        email: '<EMAIL>',
        phone_number: '+**********',
        date_of_birth: oldDate.toISOString().split('T')[0]
      };

      const result = validatePatientData(oldPatient);
      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('Patient age is over 100 years - please verify');
    });
  });

  describe('Consultation Data Validation', () => {
    it('should validate correct consultation data', () => {
      const validConsultation = {
        doctor_name: 'Dr. Smith',
        consultation_date: '2024-01-15',
        symptoms: 'Patient complains of headache and fever for the past 3 days',
        diagnosis: 'Viral infection',
        treatment_plan: 'Rest and fluids'
      };

      const result = validateConsultationData(validConsultation);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should require doctor name', () => {
      const invalidConsultation = {
        doctor_name: '',
        consultation_date: '2024-01-15',
        symptoms: 'Patient complains of headache'
      };

      const result = validateConsultationData(invalidConsultation);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Doctor name is required');
    });

    it('should warn for brief symptoms', () => {
      const briefConsultation = {
        doctor_name: 'Dr. Smith',
        consultation_date: '2024-01-15',
        symptoms: 'Headache'
      };

      const result = validateConsultationData(briefConsultation);
      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('Symptoms description seems very brief - consider adding more details');
    });
  });

  describe('Laboratory Data Validation', () => {
    it('should validate correct laboratory data', () => {
      const validLab = {
        test_type: 'Blood Test',
        technician_name: 'Tech Johnson',
        test_date: '2024-01-15',
        status: 'completed' as const,
        results: 'Normal values'
      };

      const result = validateLaboratoryData(validLab);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should require test type', () => {
      const invalidLab = {
        test_type: '',
        technician_name: 'Tech Johnson',
        test_date: '2024-01-15',
        status: 'pending' as const
      };

      const result = validateLaboratoryData(invalidLab);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Test type is required');
    });

    it('should warn for completed tests without results', () => {
      const incompleteTest = {
        test_type: 'Blood Test',
        technician_name: 'Tech Johnson',
        test_date: '2024-01-15',
        status: 'completed' as const,
        results: ''
      };

      const result = validateLaboratoryData(incompleteTest);
      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('Test is marked as completed but no results are provided');
    });
  });

  describe('Pharmacy Data Validation', () => {
    it('should validate correct pharmacy data', () => {
      const validPharmacy = {
        pharmacist_name: 'Pharmacist Brown',
        dispensed_date: '2024-01-15',
        medications: [
          { name: 'Aspirin', quantity: 30, dosage: '100mg' },
          { name: 'Ibuprofen', quantity: 20, dosage: '200mg' }
        ],
        total_cost: 25.50
      };

      const result = validatePharmacyData(validPharmacy);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should require at least one medication', () => {
      const invalidPharmacy = {
        pharmacist_name: 'Pharmacist Brown',
        dispensed_date: '2024-01-15',
        medications: [],
        total_cost: 0
      };

      const result = validatePharmacyData(invalidPharmacy);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('At least one medication must be specified');
    });

    it('should validate medication quantities', () => {
      const invalidPharmacy = {
        pharmacist_name: 'Pharmacist Brown',
        dispensed_date: '2024-01-15',
        medications: [
          { name: 'Aspirin', quantity: 0 },
          { name: '', quantity: 10 }
        ],
        total_cost: 25.50
      };

      const result = validatePharmacyData(invalidPharmacy);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Medication 1: Quantity must be greater than 0');
      expect(result.errors).toContain('Medication 2: Name is required');
    });

    it('should warn for large quantities', () => {
      const largeQuantityPharmacy = {
        pharmacist_name: 'Pharmacist Brown',
        dispensed_date: '2024-01-15',
        medications: [
          { name: 'Aspirin', quantity: 1500 }
        ],
        total_cost: 25.50
      };

      const result = validatePharmacyData(largeQuantityPharmacy);
      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('Medication 1: Very large quantity (1500) - please verify');
    });
  });

  describe('Workflow Transition Validation', () => {
    it('should allow valid transitions', () => {
      const validTransitions = [
        ['reception', 'consultation'],
        ['consultation', 'laboratory'],
        ['consultation', 'pharmacy'],
        ['laboratory', 'pharmacy'],
        ['pharmacy', 'completed']
      ];

      validTransitions.forEach(([from, to]) => {
        const result = validateWorkflowTransition(from, to);
        expect(result.isValid).toBe(true);
      });
    });

    it('should reject invalid transitions', () => {
      const invalidTransitions = [
        ['reception', 'pharmacy'],
        ['laboratory', 'consultation'],
        ['completed', 'pharmacy'],
        ['pharmacy', 'reception']
      ];

      invalidTransitions.forEach(([from, to]) => {
        const result = validateWorkflowTransition(from, to);
        expect(result.isValid).toBe(false);
        expect(result.errors).toContain(`Invalid transition from ${from} to ${to}`);
      });
    });

    it('should warn when skipping laboratory', () => {
      const result = validateWorkflowTransition('consultation', 'pharmacy');
      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('Skipping laboratory department - ensure no tests are required');
    });
  });

  describe('Inventory Availability Validation', () => {
    const mockInventory = [
      { medication_name: 'Aspirin', stock_quantity: 100 },
      { medication_name: 'Ibuprofen', stock_quantity: 8 },
      { medication_name: 'Acetaminophen', stock_quantity: 50 }
    ];

    it('should validate sufficient inventory', () => {
      const medications = [
        { name: 'Aspirin', quantity: 30 },
        { name: 'Acetaminophen', quantity: 20 }
      ];

      const result = validateInventoryAvailability(medications, mockInventory);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject insufficient inventory', () => {
      const medications = [
        { name: 'Aspirin', quantity: 150 },
        { name: 'Ibuprofen', quantity: 10 }
      ];

      const result = validateInventoryAvailability(medications, mockInventory);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Insufficient stock for "Aspirin". Available: 100, Required: 150');
      expect(result.errors).toContain('Insufficient stock for "Ibuprofen". Available: 8, Required: 10');
    });

    it('should reject medications not in inventory', () => {
      const medications = [
        { name: 'Unknown Medicine', quantity: 10 }
      ];

      const result = validateInventoryAvailability(medications, mockInventory);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Medication "Unknown Medicine" not found in inventory');
    });

    it('should warn for low stock after dispensing', () => {
      const medications = [
        { name: 'Ibuprofen', quantity: 5 }
      ];

      const result = validateInventoryAvailability(medications, mockInventory);
      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('Low stock warning for "Ibuprofen". Remaining after dispensing: 3');
    });
  });
});

describe('Workflow Integration Tests', () => {
  it('should handle complete patient workflow', () => {
    // Test complete workflow from registration to completion
    const patientData = {
      patient_name: 'Jane Smith',
      email: '<EMAIL>',
      phone_number: '+**********',
      date_of_birth: '1985-05-15',
      gender: 'female',
      blood_type: 'A+'
    };

    const consultationData = {
      doctor_name: 'Dr. Wilson',
      consultation_date: '2024-01-15',
      symptoms: 'Patient reports persistent cough and mild fever for 5 days',
      diagnosis: 'Upper respiratory infection',
      treatment_plan: 'Antibiotics and rest'
    };

    const labData = {
      test_type: 'Blood Count',
      technician_name: 'Lab Tech Davis',
      test_date: '2024-01-15',
      status: 'completed' as const,
      results: 'Slightly elevated white blood cell count'
    };

    const pharmacyData = {
      pharmacist_name: 'Pharmacist Lee',
      dispensed_date: '2024-01-15',
      medications: [
        { name: 'Amoxicillin', quantity: 21, dosage: '500mg' },
        { name: 'Cough Syrup', quantity: 1, dosage: '10ml as needed' }
      ],
      total_cost: 45.75
    };

    // Validate each stage
    expect(validatePatientData(patientData).isValid).toBe(true);
    expect(validateConsultationData(consultationData).isValid).toBe(true);
    expect(validateLaboratoryData(labData).isValid).toBe(true);
    expect(validatePharmacyData(pharmacyData).isValid).toBe(true);

    // Validate workflow transitions
    expect(validateWorkflowTransition('reception', 'consultation').isValid).toBe(true);
    expect(validateWorkflowTransition('consultation', 'laboratory').isValid).toBe(true);
    expect(validateWorkflowTransition('laboratory', 'pharmacy').isValid).toBe(true);
    expect(validateWorkflowTransition('pharmacy', 'completed').isValid).toBe(true);
  });

  it('should handle workflow with skipped laboratory', () => {
    const consultationData = {
      doctor_name: 'Dr. Wilson',
      consultation_date: '2024-01-15',
      symptoms: 'Minor headache, no fever',
      diagnosis: 'Tension headache',
      treatment_plan: 'Over-the-counter pain relief'
    };

    const pharmacyData = {
      pharmacist_name: 'Pharmacist Lee',
      dispensed_date: '2024-01-15',
      medications: [
        { name: 'Ibuprofen', quantity: 20, dosage: '200mg' }
      ],
      total_cost: 8.50
    };

    expect(validateConsultationData(consultationData).isValid).toBe(true);
    expect(validatePharmacyData(pharmacyData).isValid).toBe(true);
    
    const transitionResult = validateWorkflowTransition('consultation', 'pharmacy');
    expect(transitionResult.isValid).toBe(true);
    expect(transitionResult.warnings).toContain('Skipping laboratory department - ensure no tests are required');
  });
});
