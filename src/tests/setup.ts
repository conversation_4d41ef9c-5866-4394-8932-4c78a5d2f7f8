import { beforeAll, afterEach, afterAll } from 'vitest';
import { cleanup } from '@testing-library/react';
import '@testing-library/jest-dom';

// Mock Supabase
const mockSupabase = {
  auth: {
    getUser: () => Promise.resolve({ data: { user: { id: 'test-user' } }, error: null }),
    signInWithPassword: () => Promise.resolve({ data: { user: { id: 'test-user' } }, error: null }),
    signOut: () => Promise.resolve({ error: null }),
    onAuthStateChange: () => ({ data: { subscription: { unsubscribe: () => {} } } })
  },
  from: () => ({
    select: () => ({ data: [], error: null }),
    insert: () => ({ data: {}, error: null }),
    update: () => ({ data: {}, error: null }),
    delete: () => ({ data: {}, error: null }),
    eq: () => ({ data: [], error: null }),
    single: () => ({ data: {}, error: null })
  }),
  rpc: () => Promise.resolve({ data: [], error: null })
};

// Mock the Supabase client
vi.mock('@/integrations/supabase/client', () => ({
  supabase: mockSupabase
}));

// Mock toast notifications
vi.mock('@/hooks/use-toast', () => ({
  toast: vi.fn(),
  useToast: () => ({
    toast: vi.fn()
  })
}));

// Cleanup after each test case
afterEach(() => {
  cleanup();
});

// Global test setup
beforeAll(() => {
  // Setup any global test configuration
});

afterAll(() => {
  // Cleanup after all tests
});
