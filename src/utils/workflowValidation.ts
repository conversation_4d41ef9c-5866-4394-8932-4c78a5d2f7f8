// Workflow validation utilities for data integrity and consistency

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface PatientData {
  patient_name: string;
  email?: string;
  phone_number: string;
  date_of_birth: string;
  gender?: string;
  blood_type?: string;
  insurance?: string;
  emergency_contact?: string;
}

export interface ConsultationData {
  doctor_name: string;
  consultation_date: string;
  symptoms: string;
  diagnosis?: string;
  treatment_plan?: string;
  follow_up_date?: string;
  notes?: string;
}

export interface LaboratoryData {
  test_type: string;
  technician_name: string;
  test_date: string;
  results?: string;
  lab_notes?: string;
  status: 'pending' | 'completed' | 'reviewed';
}

export interface PharmacyData {
  pharmacist_name: string;
  dispensed_date: string;
  medications: Array<{
    name: string;
    quantity: number;
    dosage?: string;
    instructions?: string;
  }>;
  total_cost: number;
  notes?: string;
}

// Patient data validation
export const validatePatientData = (data: Partial<PatientData>): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required fields
  if (!data.patient_name?.trim()) {
    errors.push('Patient name is required');
  } else if (data.patient_name.length < 2) {
    errors.push('Patient name must be at least 2 characters long');
  }

  // Email is optional, but if provided, it must be valid
  if (data.email?.trim() && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
    errors.push('Please enter a valid email address');
  }

  if (!data.phone_number?.trim()) {
    errors.push('Phone number is required');
  } else if (!/^\+?[\d\s\-\(\)]{10,}$/.test(data.phone_number)) {
    errors.push('Please enter a valid phone number');
  }

  if (!data.date_of_birth) {
    errors.push('Date of birth is required');
  } else {
    const birthDate = new Date(data.date_of_birth);
    const today = new Date();
    const age = today.getFullYear() - birthDate.getFullYear();
    
    if (birthDate > today) {
      errors.push('Date of birth cannot be in the future');
    } else if (age > 150) {
      errors.push('Please verify the date of birth - age seems unusually high');
    } else if (age < 0) {
      errors.push('Invalid date of birth');
    }
    
    if (age > 100) {
      warnings.push('Patient age is over 100 years - please verify');
    }
  }

  // Optional field validations
  if (data.blood_type && !['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'].includes(data.blood_type)) {
    errors.push('Invalid blood type selected');
  }

  if (data.gender && !['male', 'female', 'other'].includes(data.gender.toLowerCase())) {
    errors.push('Invalid gender selected');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

// Consultation data validation
export const validateConsultationData = (data: Partial<ConsultationData>): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!data.doctor_name?.trim()) {
    errors.push('Doctor name is required');
  }

  if (!data.consultation_date) {
    errors.push('Consultation date is required');
  } else {
    const consultationDate = new Date(data.consultation_date);
    const today = new Date();
    
    if (consultationDate > today) {
      errors.push('Consultation date cannot be in the future');
    }
    
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(today.getDate() - 30);
    
    if (consultationDate < thirtyDaysAgo) {
      warnings.push('Consultation date is more than 30 days ago');
    }
  }

  if (!data.symptoms?.trim()) {
    errors.push('Patient symptoms are required');
  } else if (data.symptoms.length < 10) {
    warnings.push('Symptoms description seems very brief - consider adding more details');
  }

  if (data.follow_up_date) {
    const followUpDate = new Date(data.follow_up_date);
    const consultationDate = new Date(data.consultation_date || new Date());
    
    if (followUpDate <= consultationDate) {
      errors.push('Follow-up date must be after consultation date');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

// Laboratory data validation
export const validateLaboratoryData = (data: Partial<LaboratoryData>): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!data.test_type?.trim()) {
    errors.push('Test type is required');
  }

  if (!data.technician_name?.trim()) {
    errors.push('Technician name is required');
  }

  if (!data.test_date) {
    errors.push('Test date is required');
  } else {
    const testDate = new Date(data.test_date);
    const today = new Date();
    
    if (testDate > today) {
      errors.push('Test date cannot be in the future');
    }
  }

  if (!data.status) {
    errors.push('Test status is required');
  } else if (!['pending', 'completed', 'reviewed'].includes(data.status)) {
    errors.push('Invalid test status');
  }

  if (data.status === 'completed' && !data.results?.trim()) {
    warnings.push('Test is marked as completed but no results are provided');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

// Pharmacy data validation
export const validatePharmacyData = (data: Partial<PharmacyData>): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!data.pharmacist_name?.trim()) {
    errors.push('Pharmacist name is required');
  }

  if (!data.dispensed_date) {
    errors.push('Dispensed date is required');
  } else {
    const dispensedDate = new Date(data.dispensed_date);
    const today = new Date();
    
    if (dispensedDate > today) {
      errors.push('Dispensed date cannot be in the future');
    }
  }

  if (!data.medications || data.medications.length === 0) {
    errors.push('At least one medication must be specified');
  } else {
    data.medications.forEach((medication, index) => {
      if (!medication.name?.trim()) {
        errors.push(`Medication ${index + 1}: Name is required`);
      }
      
      if (!medication.quantity || medication.quantity <= 0) {
        errors.push(`Medication ${index + 1}: Quantity must be greater than 0`);
      }
      
      if (medication.quantity > 1000) {
        warnings.push(`Medication ${index + 1}: Very large quantity (${medication.quantity}) - please verify`);
      }
    });
  }

  if (data.total_cost !== undefined) {
    if (data.total_cost < 0) {
      errors.push('Total cost cannot be negative');
    }
    
    if (data.total_cost > 10000) {
      warnings.push(`Very high total cost ($${data.total_cost}) - please verify`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

// Workflow state validation
export const validateWorkflowTransition = (
  currentDepartment: string, 
  targetDepartment: string
): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  const validTransitions: Record<string, string[]> = {
    'reception': ['consultation'],
    'consultation': ['laboratory', 'pharmacy'], // Can skip lab if no tests needed
    'laboratory': ['pharmacy'],
    'pharmacy': ['completed'],
    'completed': [] // No further transitions
  };

  if (!validTransitions[currentDepartment]) {
    errors.push(`Invalid current department: ${currentDepartment}`);
  } else if (!validTransitions[currentDepartment].includes(targetDepartment)) {
    errors.push(`Invalid transition from ${currentDepartment} to ${targetDepartment}`);
  }

  // Special case: skipping laboratory
  if (currentDepartment === 'consultation' && targetDepartment === 'pharmacy') {
    warnings.push('Skipping laboratory department - ensure no tests are required');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

// Inventory validation for pharmacy
export const validateInventoryAvailability = (
  medications: Array<{ name: string; quantity: number }>,
  inventory: Array<{ medication_name: string; stock_quantity: number }>
): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  medications.forEach(medication => {
    const inventoryItem = inventory.find(
      item => item.medication_name.toLowerCase() === medication.name.toLowerCase()
    );

    if (!inventoryItem) {
      errors.push(`Medication "${medication.name}" not found in inventory`);
    } else if (inventoryItem.stock_quantity < medication.quantity) {
      errors.push(
        `Insufficient stock for "${medication.name}". Available: ${inventoryItem.stock_quantity}, Required: ${medication.quantity}`
      );
    } else if (inventoryItem.stock_quantity - medication.quantity < 5) {
      warnings.push(
        `Low stock warning for "${medication.name}". Remaining after dispensing: ${inventoryItem.stock_quantity - medication.quantity}`
      );
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};
