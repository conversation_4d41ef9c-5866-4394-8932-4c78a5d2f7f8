import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

interface PatientRecord {
  patient: any;
  consultation?: any;
  laboratory?: any;
  pharmacy?: any;
  hospitalName?: string;
}

export const generatePatientPDF = async (patientRecord: PatientRecord) => {
  const { patient, consultation, laboratory, pharmacy, hospitalName } = patientRecord;
  
  // Create new PDF document
  const pdf = new jsPDF('p', 'mm', 'a4');
  const pageWidth = pdf.internal.pageSize.getWidth();
  const pageHeight = pdf.internal.pageSize.getHeight();
  let yPosition = 20;

  // Helper function to add text with word wrap
  const addText = (text: string, x: number, y: number, maxWidth?: number, fontSize = 10) => {
    pdf.setFontSize(fontSize);
    if (maxWidth) {
      const lines = pdf.splitTextToSize(text, maxWidth);
      pdf.text(lines, x, y);
      return y + (lines.length * (fontSize * 0.35));
    } else {
      pdf.text(text, x, y);
      return y + (fontSize * 0.35);
    }
  };

  // Helper function to add section header
  const addSectionHeader = (title: string, y: number) => {
    pdf.setFillColor(59, 130, 246); // Blue background
    pdf.rect(10, y - 5, pageWidth - 20, 8, 'F');
    pdf.setTextColor(255, 255, 255); // White text
    pdf.setFontSize(12);
    pdf.setFont('helvetica', 'bold');
    pdf.text(title, 15, y);
    pdf.setTextColor(0, 0, 0); // Reset to black
    pdf.setFont('helvetica', 'normal');
    return y + 10;
  };

  // Header
  pdf.setFillColor(34, 197, 94); // Green background
  pdf.rect(0, 0, pageWidth, 25, 'F');
  pdf.setTextColor(255, 255, 255);
  pdf.setFontSize(18);
  pdf.setFont('helvetica', 'bold');
  pdf.text(hospitalName?.toUpperCase() || 'HEALTHCARE MANAGEMENT SYSTEM', 15, 12);
  pdf.setFontSize(12);
  pdf.text('Complete Patient Medical Record', 15, 18);
  pdf.setTextColor(0, 0, 0);
  pdf.setFont('helvetica', 'normal');

  yPosition = 35;

  // Patient Information Section
  yPosition = addSectionHeader('PATIENT INFORMATION', yPosition);
  
  const patientAge = patient.date_of_birth ? 
    new Date().getFullYear() - new Date(patient.date_of_birth).getFullYear() : 'Unknown';

  yPosition = addText(`Name: ${patient.patient_name}`, 15, yPosition, pageWidth - 30, 11);
  yPosition = addText(`Age: ${patientAge} years`, 15, yPosition + 5, pageWidth - 30, 10);
  yPosition = addText(`Date of Birth: ${patient.date_of_birth || 'Not provided'}`, 15, yPosition + 5, pageWidth - 30, 10);
  yPosition = addText(`Phone: ${patient.phone_number}`, 15, yPosition + 5, pageWidth - 30, 10);
  yPosition = addText(`Email: ${patient.email}`, 15, yPosition + 5, pageWidth - 30, 10);
  yPosition = addText(`Address: ${patient.address || 'Not provided'}`, 15, yPosition + 5, pageWidth - 30, 10);
  yPosition = addText(`Blood Type: ${patient.blood_type || 'Not provided'}`, 15, yPosition + 5, pageWidth - 30, 10);
  yPosition = addText(`Emergency Contact: ${patient.emergency_contact || 'Not provided'}`, 15, yPosition + 5, pageWidth - 30, 10);

  yPosition += 10;

  // Consultation Section
  if (consultation) {
    yPosition = addSectionHeader('CONSULTATION RECORD', yPosition);
    
    yPosition = addText(`Doctor: ${consultation.doctor_name}`, 15, yPosition, pageWidth - 30, 11);
    yPosition = addText(`Date: ${new Date(consultation.consultation_date || consultation.created_at).toLocaleDateString()}`, 15, yPosition + 5, pageWidth - 30, 10);
    yPosition = addText(`Symptoms: ${consultation.symptoms || 'None recorded'}`, 15, yPosition + 5, pageWidth - 30, 10);
    yPosition = addText(`Diagnosis: ${consultation.diagnosis || 'Not provided'}`, 15, yPosition + 5, pageWidth - 30, 10);
    yPosition = addText(`Treatment Plan: ${consultation.treatment_plan || 'Not provided'}`, 15, yPosition + 5, pageWidth - 30, 10);
    
    if (consultation.notes) {
      yPosition = addText(`Notes: ${consultation.notes}`, 15, yPosition + 5, pageWidth - 30, 10);
    }

    // Prescribed Medicines from Consultation (check both prescribed_medicines field and notes)
    if (consultation.prescribed_medicines && consultation.prescribed_medicines.length > 0) {
      yPosition += 5;
      pdf.setFont('helvetica', 'bold');
      yPosition = addText('Prescribed Medicines:', 15, yPosition, pageWidth - 30, 10);
      pdf.setFont('helvetica', 'normal');

      consultation.prescribed_medicines.forEach((med: any, index: number) => {
        yPosition = addText(`${index + 1}. ${med.name} - ${med.dosage}`, 20, yPosition + 3, pageWidth - 35, 9);
        yPosition = addText(`   Frequency: ${med.frequency}, Duration: ${med.duration}`, 20, yPosition + 2, pageWidth - 35, 8);
        if (med.instructions) {
          yPosition = addText(`   Instructions: ${med.instructions}`, 20, yPosition + 2, pageWidth - 35, 8);
        }
      });
    } else if (consultation.notes && consultation.notes.includes('--- PRESCRIBED MEDICINES ---')) {
      // Extract prescribed medicines from notes if they're stored there
      yPosition += 5;
      pdf.setFont('helvetica', 'bold');
      yPosition = addText('Prescribed Medicines:', 15, yPosition, pageWidth - 30, 10);
      pdf.setFont('helvetica', 'normal');

      const medicinesSection = consultation.notes.split('--- PRESCRIBED MEDICINES ---')[1];
      if (medicinesSection) {
        const medicineLines = medicinesSection.trim().split('\n');
        medicineLines.forEach((line: string) => {
          if (line.trim()) {
            yPosition = addText(line.trim(), 20, yPosition + 3, pageWidth - 35, 9);
          }
        });
      }
    }

    yPosition += 10;
  }

  // Laboratory Section
  if (laboratory) {
    // Check if we need a new page
    if (yPosition > pageHeight - 60) {
      pdf.addPage();
      yPosition = 20;
    }

    yPosition = addSectionHeader('LABORATORY RECORD', yPosition);
    
    yPosition = addText(`Technician: ${laboratory.technician_name}`, 15, yPosition, pageWidth - 30, 11);
    yPosition = addText(`Test Date: ${new Date(laboratory.test_date || laboratory.created_at).toLocaleDateString()}`, 15, yPosition + 5, pageWidth - 30, 10);
    yPosition = addText(`Test Type: ${laboratory.test_type || 'Not specified'}`, 15, yPosition + 5, pageWidth - 30, 10);
    yPosition = addText(`Status: ${laboratory.status || 'Completed'}`, 15, yPosition + 5, pageWidth - 30, 10);

    // Test Results
    if (laboratory.test_results && Object.keys(laboratory.test_results).length > 0) {
      yPosition += 5;
      pdf.setFont('helvetica', 'bold');
      yPosition = addText('Test Results:', 15, yPosition, pageWidth - 30, 10);
      pdf.setFont('helvetica', 'normal');
      
      Object.entries(laboratory.test_results).forEach(([key, value]: [string, any]) => {
        yPosition = addText(`${key}: ${value}`, 20, yPosition + 3, pageWidth - 35, 9);
      });
    }

    if (laboratory.lab_notes) {
      yPosition += 5;
      yPosition = addText(`Lab Notes: ${laboratory.lab_notes}`, 15, yPosition, pageWidth - 30, 10);
    }

    // Prescribed Medicines from Laboratory
    if (laboratory.prescribed_medicines && laboratory.prescribed_medicines.length > 0) {
      yPosition += 5;
      pdf.setFont('helvetica', 'bold');
      yPosition = addText('Additional Medicines (Based on Lab Results):', 15, yPosition, pageWidth - 30, 10);
      pdf.setFont('helvetica', 'normal');
      
      laboratory.prescribed_medicines.forEach((med: any, index: number) => {
        yPosition = addText(`${index + 1}. ${med.name} - ${med.dosage}`, 20, yPosition + 3, pageWidth - 35, 9);
        yPosition = addText(`   Frequency: ${med.frequency}, Duration: ${med.duration}`, 20, yPosition + 2, pageWidth - 35, 8);
        if (med.instructions) {
          yPosition = addText(`   Instructions: ${med.instructions}`, 20, yPosition + 2, pageWidth - 35, 8);
        }
      });
    }

    yPosition += 10;
  }

  // Pharmacy Section
  if (pharmacy) {
    // Check if we need a new page
    if (yPosition > pageHeight - 40) {
      pdf.addPage();
      yPosition = 20;
    }

    yPosition = addSectionHeader('PHARMACY RECORD', yPosition);
    
    yPosition = addText(`Pharmacist: ${pharmacy.pharmacist_name}`, 15, yPosition, pageWidth - 30, 11);
    yPosition = addText(`Dispensed Date: ${new Date(pharmacy.dispensed_date || pharmacy.created_at).toLocaleDateString()}`, 15, yPosition + 5, pageWidth - 30, 10);
    
    if (pharmacy.total_cost) {
      yPosition = addText(`Total Cost: $${pharmacy.total_cost.toFixed(2)}`, 15, yPosition + 5, pageWidth - 30, 10);
    }

    // Approved Medicines
    if (pharmacy.approved_medicines && pharmacy.approved_medicines.length > 0) {
      yPosition += 5;
      pdf.setFont('helvetica', 'bold');
      yPosition = addText('Approved & Dispensed Medicines:', 15, yPosition, pageWidth - 30, 10);
      pdf.setFont('helvetica', 'normal');
      
      pharmacy.approved_medicines.forEach((med: any, index: number) => {
        yPosition = addText(`${index + 1}. ${med.name} - ${med.dosage}`, 20, yPosition + 3, pageWidth - 35, 9);
        yPosition = addText(`   Quantity Dispensed: ${med.dispensed_quantity || 'Not specified'}`, 20, yPosition + 2, pageWidth - 35, 8);
        if (med.notes) {
          yPosition = addText(`   Pharmacist Notes: ${med.notes}`, 20, yPosition + 2, pageWidth - 35, 8);
        }
      });
    }

    if (pharmacy.notes) {
      yPosition += 5;
      yPosition = addText(`Pharmacy Notes: ${pharmacy.notes}`, 15, yPosition, pageWidth - 30, 10);
    }
  }

  // Footer
  const footerY = pageHeight - 15;
  pdf.setFontSize(8);
  pdf.setTextColor(128, 128, 128);
  pdf.text(`Generated on: ${new Date().toLocaleString()}`, 15, footerY);
  pdf.text(`Patient ID: ${patient.id}`, pageWidth - 60, footerY);

  return pdf;
};

export const downloadPatientPDF = async (patientRecord: PatientRecord) => {
  try {
    const pdf = await generatePatientPDF(patientRecord);
    const fileName = `${patientRecord.patient.patient_name.replace(/\s+/g, '_')}_Medical_Record_${new Date().toISOString().split('T')[0]}.pdf`;
    pdf.save(fileName);
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw new Error('Failed to generate PDF');
  }
};

export const printPatientRecord = async (patientRecord: PatientRecord) => {
  try {
    const pdf = await generatePatientPDF(patientRecord);
    const pdfBlob = pdf.output('blob');
    const pdfUrl = URL.createObjectURL(pdfBlob);
    
    // Open in new window for printing
    const printWindow = window.open(pdfUrl, '_blank');
    if (printWindow) {
      printWindow.onload = () => {
        printWindow.print();
      };
    }
  } catch (error) {
    console.error('Error printing PDF:', error);
    throw new Error('Failed to print record');
  }
};
