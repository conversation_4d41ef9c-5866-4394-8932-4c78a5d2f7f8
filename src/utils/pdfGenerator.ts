import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

interface PatientRecord {
  patient: any;
  consultation?: any;
  laboratory?: any;
  pharmacy?: any;
  hospitalName?: string;
  hospitalLogo?: string;
  settings?: any;
}

export const generatePatientPDF = async (patientRecord: PatientRecord) => {
  const { patient, consultation, laboratory, pharmacy, hospitalName, hospitalLogo, settings } = patientRecord;
  
  // Create new PDF document
  const pdf = new jsPDF('p', 'mm', 'a4');
  const pageWidth = pdf.internal.pageSize.getWidth();
  const pageHeight = pdf.internal.pageSize.getHeight();
  let yPosition = 20;

  // Helper function to add text with word wrap
  const addText = (text: string, x: number, y: number, maxWidth?: number, fontSize = 10) => {
    pdf.setFontSize(fontSize);
    if (maxWidth) {
      const lines = pdf.splitTextToSize(text, maxWidth);
      pdf.text(lines, x, y);
      return y + (lines.length * (fontSize * 0.35));
    } else {
      pdf.text(text, x, y);
      return y + (fontSize * 0.35);
    }
  };

  // Helper function to add section header
  const addSectionHeader = (title: string, y: number) => {
    pdf.setFillColor(59, 130, 246); // Blue background
    pdf.rect(10, y - 5, pageWidth - 20, 8, 'F');
    pdf.setTextColor(255, 255, 255); // White text
    pdf.setFontSize(12);
    pdf.setFont('helvetica', 'bold');
    pdf.text(title, 15, y);
    pdf.setTextColor(0, 0, 0); // Reset to black
    pdf.setFont('helvetica', 'normal');
    return y + 10;
  };

  // Header with Logo
  pdf.setFillColor(34, 197, 94); // Green background
  pdf.rect(0, 0, pageWidth, 30, 'F');

  let logoXPosition = 15;
  let textXPosition = 15;

  // Add hospital logo if available
  if (hospitalLogo || settings?.logo_url) {
    try {
      const logoUrl = hospitalLogo || settings?.logo_url;
      // Create a temporary image element to load the logo
      const img = new Image();
      img.crossOrigin = 'anonymous';

      await new Promise((resolve, reject) => {
        img.onload = () => {
          try {
            // Create a canvas to convert image to base64
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = img.width;
            canvas.height = img.height;
            ctx?.drawImage(img, 0, 0);

            const imgData = canvas.toDataURL('image/jpeg', 0.8);

            // Add logo to PDF (20mm width, maintain aspect ratio)
            const logoWidth = 20;
            const logoHeight = (img.height / img.width) * logoWidth;
            pdf.addImage(imgData, 'JPEG', logoXPosition, 5, logoWidth, Math.min(logoHeight, 20));

            textXPosition = logoXPosition + logoWidth + 5; // Position text after logo
            resolve(true);
          } catch (error) {
            console.warn('Error processing logo:', error);
            resolve(false);
          }
        };
        img.onerror = () => {
          console.warn('Failed to load hospital logo');
          resolve(false);
        };
        img.src = logoUrl;
      });
    } catch (error) {
      console.warn('Error loading hospital logo:', error);
    }
  }

  // Header text
  pdf.setTextColor(255, 255, 255);
  pdf.setFontSize(18);
  pdf.setFont('helvetica', 'bold');
  const hospitalDisplayName = settings?.hospital_name || hospitalName || 'HEALTHCARE MANAGEMENT SYSTEM';
  pdf.text(hospitalDisplayName.toUpperCase(), textXPosition, 12);

  pdf.setFontSize(12);
  pdf.text('Complete Patient Medical Record', textXPosition, 18);

  // Add hospital contact info if available
  if (settings?.phone_number || settings?.email) {
    pdf.setFontSize(8);
    let contactY = 22;
    if (settings?.phone_number) {
      pdf.text(`Tel: ${settings.phone_number}`, textXPosition, contactY);
      contactY += 3;
    }
    if (settings?.email) {
      pdf.text(`Email: ${settings.email}`, textXPosition, contactY);
    }
  }

  pdf.setTextColor(0, 0, 0);
  pdf.setFont('helvetica', 'normal');

  yPosition = 40;

  // Patient Registration Information Section
  yPosition = addSectionHeader('PATIENT REGISTRATION INFORMATION', yPosition);

  const patientAge = patient.date_of_birth ?
    new Date().getFullYear() - new Date(patient.date_of_birth).getFullYear() : 'Unknown';

  // Basic Information
  yPosition = addText(`Full Name: ${patient.patient_name}`, 15, yPosition, pageWidth - 30, 11);
  yPosition = addText(`Patient ID: ${patient.id}`, 15, yPosition + 5, pageWidth - 30, 10);
  yPosition = addText(`Date of Birth: ${patient.date_of_birth ? new Date(patient.date_of_birth).toLocaleDateString() : 'Not provided'}`, 15, yPosition + 5, pageWidth - 30, 10);
  yPosition = addText(`Age: ${patientAge} years`, 15, yPosition + 5, pageWidth - 30, 10);
  yPosition = addText(`Gender: ${patient.gender || 'Not specified'}`, 15, yPosition + 5, pageWidth - 30, 10);

  // Contact Information
  yPosition = addText(`Phone: ${patient.phone_number || 'Not provided'}`, 15, yPosition + 5, pageWidth - 30, 10);
  yPosition = addText(`Email: ${patient.email || 'Not provided'}`, 15, yPosition + 5, pageWidth - 30, 10);
  yPosition = addText(`Address: ${patient.address || 'Not provided'}`, 15, yPosition + 5, pageWidth - 30, 10);
  yPosition = addText(`Emergency Contact: ${patient.emergency_contact || 'Not provided'}`, 15, yPosition + 5, pageWidth - 30, 10);

  // Medical Information
  yPosition = addText(`Blood Type: ${patient.blood_type || 'Not specified'}`, 15, yPosition + 5, pageWidth - 30, 10);
  yPosition = addText(`Insurance: ${patient.insurance || 'Not specified'}`, 15, yPosition + 5, pageWidth - 30, 10);

  // Registration Details
  yPosition = addText(`Registration Date: ${new Date(patient.registration_date || patient.created_at).toLocaleDateString()}`, 15, yPosition + 5, pageWidth - 30, 10);
  yPosition = addText(`Workflow Status: ${patient.workflow_status || 'Registered'}`, 15, yPosition + 5, pageWidth - 30, 10);

  // Registration Fee Information
  if (patient.fee_amount > 0 || patient.fee_paid) {
    const currencySymbol = settings?.currency || 'KES';
    yPosition = addText(`Registration Fee: ${currencySymbol} ${patient.fee_amount?.toFixed(2) || '0.00'}`, 15, yPosition + 5, pageWidth - 30, 10);
    yPosition = addText(`Fee Status: ${patient.fee_paid ? 'PAID' : 'PENDING'}`, 15, yPosition + 5, pageWidth - 30, 10);
    if (patient.fee_notes) {
      yPosition = addText(`Fee Notes: ${patient.fee_notes}`, 15, yPosition + 5, pageWidth - 30, 10);
    }
  }

  // Additional Notes
  if (patient.notes) {
    yPosition = addText(`Notes: ${patient.notes}`, 15, yPosition + 5, pageWidth - 30, 10);
  }

  yPosition += 10;

  // Consultation Section
  if (consultation) {
    yPosition = addSectionHeader('CONSULTATION RECORD', yPosition);

    yPosition = addText(`Doctor: ${consultation.doctor_name}`, 15, yPosition, pageWidth - 30, 11);
    yPosition = addText(`Date: ${new Date(consultation.consultation_date || consultation.created_at).toLocaleDateString()}`, 15, yPosition + 5, pageWidth - 30, 10);
    yPosition = addText(`Type: ${consultation.consultation_type || 'General'}`, 15, yPosition + 5, pageWidth - 30, 10);

    // Consultation Times
    if (consultation.start_time || consultation.end_time) {
      yPosition = addText(`Start Time: ${consultation.start_time || 'Not recorded'}`, 15, yPosition + 5, pageWidth - 30, 10);
      yPosition = addText(`End Time: ${consultation.end_time || 'Not recorded'}`, 15, yPosition + 5, pageWidth - 30, 10);
    }

    // Medical Information
    yPosition = addText(`Symptoms: ${consultation.symptoms || 'None recorded'}`, 15, yPosition + 5, pageWidth - 30, 10);
    yPosition = addText(`Vital Signs: ${consultation.vital_signs || 'Not recorded'}`, 15, yPosition + 5, pageWidth - 30, 10);
    yPosition = addText(`Diagnosis: ${consultation.diagnosis || 'Not provided'}`, 15, yPosition + 5, pageWidth - 30, 10);
    yPosition = addText(`Treatment Plan: ${consultation.treatment_plan || 'Not provided'}`, 15, yPosition + 5, pageWidth - 30, 10);

    // Consultation Fee Information
    if (consultation.fee_amount > 0 || consultation.fee_paid) {
      const currencySymbol = settings?.currency || 'KES';
      yPosition = addText(`Consultation Fee: ${currencySymbol} ${consultation.fee_amount?.toFixed(2) || '0.00'}`, 15, yPosition + 5, pageWidth - 30, 10);
      yPosition = addText(`Fee Status: ${consultation.fee_paid ? 'PAID' : 'PENDING'}`, 15, yPosition + 5, pageWidth - 30, 10);
      if (consultation.fee_notes) {
        yPosition = addText(`Fee Notes: ${consultation.fee_notes}`, 15, yPosition + 5, pageWidth - 30, 10);
      }
    }

    if (consultation.notes) {
      yPosition = addText(`Additional Notes: ${consultation.notes}`, 15, yPosition + 5, pageWidth - 30, 10);
    }

    // Prescribed Medicines from Consultation (check both prescribed_medicines field and notes)
    if (consultation.prescribed_medicines && consultation.prescribed_medicines.length > 0) {
      yPosition += 5;
      pdf.setFont('helvetica', 'bold');
      yPosition = addText('Prescribed Medicines:', 15, yPosition, pageWidth - 30, 10);
      pdf.setFont('helvetica', 'normal');

      consultation.prescribed_medicines.forEach((med: any, index: number) => {
        yPosition = addText(`${index + 1}. ${med.name} - ${med.dosage}`, 20, yPosition + 3, pageWidth - 35, 9);
        yPosition = addText(`   Frequency: ${med.frequency}, Duration: ${med.duration}`, 20, yPosition + 2, pageWidth - 35, 8);
        if (med.instructions) {
          yPosition = addText(`   Instructions: ${med.instructions}`, 20, yPosition + 2, pageWidth - 35, 8);
        }
      });
    } else if (consultation.notes && consultation.notes.includes('--- PRESCRIBED MEDICINES ---')) {
      // Extract prescribed medicines from notes if they're stored there
      yPosition += 5;
      pdf.setFont('helvetica', 'bold');
      yPosition = addText('Prescribed Medicines:', 15, yPosition, pageWidth - 30, 10);
      pdf.setFont('helvetica', 'normal');

      const medicinesSection = consultation.notes.split('--- PRESCRIBED MEDICINES ---')[1];
      if (medicinesSection) {
        const medicineLines = medicinesSection.trim().split('\n');
        medicineLines.forEach((line: string) => {
          if (line.trim()) {
            yPosition = addText(line.trim(), 20, yPosition + 3, pageWidth - 35, 9);
          }
        });
      }
    }

    yPosition += 10;
  }

  // Laboratory Section
  if (laboratory) {
    // Check if we need a new page
    if (yPosition > pageHeight - 60) {
      pdf.addPage();
      yPosition = 20;
    }

    yPosition = addSectionHeader('LABORATORY RECORD', yPosition);

    yPosition = addText(`Technician: ${laboratory.technician_name}`, 15, yPosition, pageWidth - 30, 11);
    yPosition = addText(`Test Date: ${new Date(laboratory.test_date || laboratory.created_at).toLocaleDateString()}`, 15, yPosition + 5, pageWidth - 30, 10);
    yPosition = addText(`Test Type: ${laboratory.test_type || 'Not specified'}`, 15, yPosition + 5, pageWidth - 30, 10);
    yPosition = addText(`Status: ${laboratory.status || 'Completed'}`, 15, yPosition + 5, pageWidth - 30, 10);

    // Laboratory Times
    if (laboratory.start_time || laboratory.end_time) {
      yPosition = addText(`Start Time: ${laboratory.start_time || 'Not recorded'}`, 15, yPosition + 5, pageWidth - 30, 10);
      yPosition = addText(`End Time: ${laboratory.end_time || 'Not recorded'}`, 15, yPosition + 5, pageWidth - 30, 10);
    }

    // Laboratory Fee Information
    if (laboratory.fee_amount > 0 || laboratory.fee_paid) {
      const currencySymbol = settings?.currency || 'KES';
      yPosition = addText(`Laboratory Fee: ${currencySymbol} ${laboratory.fee_amount?.toFixed(2) || '0.00'}`, 15, yPosition + 5, pageWidth - 30, 10);
      yPosition = addText(`Fee Status: ${laboratory.fee_paid ? 'PAID' : 'PENDING'}`, 15, yPosition + 5, pageWidth - 30, 10);
      if (laboratory.fee_notes) {
        yPosition = addText(`Fee Notes: ${laboratory.fee_notes}`, 15, yPosition + 5, pageWidth - 30, 10);
      }
    }

    // Test Results
    if (laboratory.test_results && Object.keys(laboratory.test_results).length > 0) {
      yPosition += 5;
      pdf.setFont('helvetica', 'bold');
      yPosition = addText('Test Results:', 15, yPosition, pageWidth - 30, 10);
      pdf.setFont('helvetica', 'normal');
      
      Object.entries(laboratory.test_results).forEach(([key, value]: [string, any]) => {
        yPosition = addText(`${key}: ${value}`, 20, yPosition + 3, pageWidth - 35, 9);
      });
    }

    if (laboratory.lab_notes) {
      yPosition += 5;
      yPosition = addText(`Lab Notes: ${laboratory.lab_notes}`, 15, yPosition, pageWidth - 30, 10);
    }

    // Prescribed Medicines from Laboratory
    if (laboratory.prescribed_medicines && laboratory.prescribed_medicines.length > 0) {
      yPosition += 5;
      pdf.setFont('helvetica', 'bold');
      yPosition = addText('Additional Medicines (Based on Lab Results):', 15, yPosition, pageWidth - 30, 10);
      pdf.setFont('helvetica', 'normal');
      
      laboratory.prescribed_medicines.forEach((med: any, index: number) => {
        yPosition = addText(`${index + 1}. ${med.name} - ${med.dosage}`, 20, yPosition + 3, pageWidth - 35, 9);
        yPosition = addText(`   Frequency: ${med.frequency}, Duration: ${med.duration}`, 20, yPosition + 2, pageWidth - 35, 8);
        if (med.instructions) {
          yPosition = addText(`   Instructions: ${med.instructions}`, 20, yPosition + 2, pageWidth - 35, 8);
        }
      });
    }

    yPosition += 10;
  }

  // Pharmacy Section
  if (pharmacy) {
    // Check if we need a new page
    if (yPosition > pageHeight - 40) {
      pdf.addPage();
      yPosition = 20;
    }

    yPosition = addSectionHeader('PHARMACY RECORD', yPosition);
    
    yPosition = addText(`Pharmacist: ${pharmacy.pharmacist_name}`, 15, yPosition, pageWidth - 30, 11);
    yPosition = addText(`Dispensed Date: ${new Date(pharmacy.dispensed_date || pharmacy.created_at).toLocaleDateString()}`, 15, yPosition + 5, pageWidth - 30, 10);
    
    if (pharmacy.total_cost) {
      yPosition = addText(`Total Cost: $${pharmacy.total_cost.toFixed(2)}`, 15, yPosition + 5, pageWidth - 30, 10);
    }

    // Approved Medicines
    if (pharmacy.approved_medicines && pharmacy.approved_medicines.length > 0) {
      yPosition += 5;
      pdf.setFont('helvetica', 'bold');
      yPosition = addText('Approved & Dispensed Medicines:', 15, yPosition, pageWidth - 30, 10);
      pdf.setFont('helvetica', 'normal');
      
      pharmacy.approved_medicines.forEach((med: any, index: number) => {
        yPosition = addText(`${index + 1}. ${med.name} - ${med.dosage}`, 20, yPosition + 3, pageWidth - 35, 9);
        yPosition = addText(`   Quantity Dispensed: ${med.dispensed_quantity || 'Not specified'}`, 20, yPosition + 2, pageWidth - 35, 8);
        if (med.notes) {
          yPosition = addText(`   Pharmacist Notes: ${med.notes}`, 20, yPosition + 2, pageWidth - 35, 8);
        }
      });
    }

    if (pharmacy.notes) {
      yPosition += 5;
      yPosition = addText(`Pharmacy Notes: ${pharmacy.notes}`, 15, yPosition, pageWidth - 30, 10);
    }
  }

  // Footer
  const footerY = pageHeight - 15;
  pdf.setFontSize(8);
  pdf.setTextColor(128, 128, 128);
  pdf.text(`Generated on: ${new Date().toLocaleString()}`, 15, footerY);
  pdf.text(`Patient ID: ${patient.id}`, pageWidth - 60, footerY);

  return pdf;
};

export const downloadPatientPDF = async (patientRecord: PatientRecord) => {
  try {
    const pdf = await generatePatientPDF(patientRecord);
    const fileName = `${patientRecord.patient.patient_name.replace(/\s+/g, '_')}_Medical_Record_${new Date().toISOString().split('T')[0]}.pdf`;
    pdf.save(fileName);
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw new Error('Failed to generate PDF');
  }
};

export const printPatientRecord = async (patientRecord: PatientRecord) => {
  try {
    const pdf = await generatePatientPDF(patientRecord);
    const pdfBlob = pdf.output('blob');
    const pdfUrl = URL.createObjectURL(pdfBlob);
    
    // Open in new window for printing
    const printWindow = window.open(pdfUrl, '_blank');
    if (printWindow) {
      printWindow.onload = () => {
        printWindow.print();
      };
    }
  } catch (error) {
    console.error('Error printing PDF:', error);
    throw new Error('Failed to print record');
  }
};
