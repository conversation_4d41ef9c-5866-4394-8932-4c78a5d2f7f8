import { useState, useCallback } from 'react';
import { toast } from '@/hooks/use-toast';
import {
  ValidationResult,
  validatePatientData,
  validateConsultationData,
  validateLaboratoryData,
  validatePharmacyData,
  validateWorkflowTransition,
  validateInventoryAvailability,
  PatientData,
  ConsultationData,
  LaboratoryData,
  PharmacyData
} from '@/utils/workflowValidation';

export const useValidation = () => {
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [validationWarnings, setValidationWarnings] = useState<string[]>([]);
  const [isValidating, setIsValidating] = useState(false);

  const showValidationResults = useCallback((result: ValidationResult) => {
    setValidationErrors(result.errors);
    setValidationWarnings(result.warnings);

    // Show errors as toast notifications
    if (result.errors.length > 0) {
      toast({
        title: "Validation Errors",
        description: result.errors.join(', '),
        variant: "destructive",
      });
    }

    // Show warnings as toast notifications
    if (result.warnings.length > 0) {
      toast({
        title: "Validation Warnings",
        description: result.warnings.join(', '),
        variant: "default",
      });
    }

    return result.isValid;
  }, []);

  const validatePatient = useCallback((data: Partial<PatientData>) => {
    setIsValidating(true);
    const result = validatePatientData(data);
    setIsValidating(false);
    return showValidationResults(result);
  }, [showValidationResults]);

  const validateConsultation = useCallback((data: Partial<ConsultationData>) => {
    setIsValidating(true);
    const result = validateConsultationData(data);
    setIsValidating(false);
    return showValidationResults(result);
  }, [showValidationResults]);

  const validateLaboratory = useCallback((data: Partial<LaboratoryData>) => {
    setIsValidating(true);
    const result = validateLaboratoryData(data);
    setIsValidating(false);
    return showValidationResults(result);
  }, [showValidationResults]);

  const validatePharmacy = useCallback((data: Partial<PharmacyData>) => {
    setIsValidating(true);
    const result = validatePharmacyData(data);
    setIsValidating(false);
    return showValidationResults(result);
  }, [showValidationResults]);

  const validateWorkflow = useCallback((currentDept: string, targetDept: string) => {
    setIsValidating(true);
    const result = validateWorkflowTransition(currentDept, targetDept);
    setIsValidating(false);
    return showValidationResults(result);
  }, [showValidationResults]);

  const validateInventory = useCallback((
    medications: Array<{ name: string; quantity: number }>,
    inventory: Array<{ medication_name: string; stock_quantity: number }>
  ) => {
    setIsValidating(true);
    const result = validateInventoryAvailability(medications, inventory);
    setIsValidating(false);
    return showValidationResults(result);
  }, [showValidationResults]);

  const clearValidation = useCallback(() => {
    setValidationErrors([]);
    setValidationWarnings([]);
  }, []);

  // Real-time validation for forms
  const validateField = useCallback((
    fieldName: string,
    value: any,
    formType: 'patient' | 'consultation' | 'laboratory' | 'pharmacy'
  ) => {
    const errors: string[] = [];

    switch (formType) {
      case 'patient':
        if (fieldName === 'patient_name' && (!value || value.trim().length < 2)) {
          errors.push('Patient name must be at least 2 characters long');
        }
        if (fieldName === 'email' && value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
          errors.push('Please enter a valid email address');
        }
        if (fieldName === 'phone_number' && value && !/^\+?[\d\s\-\(\)]{10,}$/.test(value)) {
          errors.push('Please enter a valid phone number');
        }
        break;

      case 'consultation':
        if (fieldName === 'doctor_name' && !value?.trim()) {
          errors.push('Doctor name is required');
        }
        if (fieldName === 'symptoms' && value && value.length < 10) {
          errors.push('Please provide more detailed symptoms');
        }
        break;

      case 'laboratory':
        if (fieldName === 'test_type' && !value?.trim()) {
          errors.push('Test type is required');
        }
        if (fieldName === 'technician_name' && !value?.trim()) {
          errors.push('Technician name is required');
        }
        break;

      case 'pharmacy':
        if (fieldName === 'pharmacist_name' && !value?.trim()) {
          errors.push('Pharmacist name is required');
        }
        if (fieldName === 'total_cost' && value < 0) {
          errors.push('Total cost cannot be negative');
        }
        break;
    }

    return errors;
  }, []);

  // Batch validation for complex forms
  const validateForm = useCallback(async (
    data: any,
    formType: 'patient' | 'consultation' | 'laboratory' | 'pharmacy'
  ): Promise<boolean> => {
    setIsValidating(true);
    
    let result: ValidationResult;
    
    switch (formType) {
      case 'patient':
        result = validatePatientData(data);
        break;
      case 'consultation':
        result = validateConsultationData(data);
        break;
      case 'laboratory':
        result = validateLaboratoryData(data);
        break;
      case 'pharmacy':
        result = validatePharmacyData(data);
        break;
      default:
        result = { isValid: false, errors: ['Invalid form type'], warnings: [] };
    }
    
    setIsValidating(false);
    return showValidationResults(result);
  }, [showValidationResults]);

  // Validation for workflow progression with business rules
  const validateWorkflowProgression = useCallback(async (
    workflowData: {
      currentDepartment: string;
      targetDepartment: string;
      patientData?: any;
      stageData?: any;
      inventory?: Array<{ medication_name: string; stock_quantity: number }>;
    }
  ): Promise<boolean> => {
    setIsValidating(true);
    
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate workflow transition
    const transitionResult = validateWorkflowTransition(
      workflowData.currentDepartment,
      workflowData.targetDepartment
    );
    errors.push(...transitionResult.errors);
    warnings.push(...transitionResult.warnings);

    // Validate stage-specific data
    if (workflowData.stageData) {
      let stageResult: ValidationResult;
      
      switch (workflowData.currentDepartment) {
        case 'consultation':
          stageResult = validateConsultationData(workflowData.stageData);
          break;
        case 'laboratory':
          stageResult = validateLaboratoryData(workflowData.stageData);
          break;
        case 'pharmacy':
          stageResult = validatePharmacyData(workflowData.stageData);
          
          // Additional inventory validation for pharmacy
          if (workflowData.inventory && workflowData.stageData.medications) {
            const inventoryResult = validateInventoryAvailability(
              workflowData.stageData.medications,
              workflowData.inventory
            );
            errors.push(...inventoryResult.errors);
            warnings.push(...inventoryResult.warnings);
          }
          break;
        default:
          stageResult = { isValid: true, errors: [], warnings: [] };
      }
      
      errors.push(...stageResult.errors);
      warnings.push(...stageResult.warnings);
    }

    // Business rule validations
    if (workflowData.currentDepartment === 'consultation' && 
        workflowData.targetDepartment === 'pharmacy' && 
        !workflowData.stageData?.diagnosis) {
      warnings.push('Proceeding to pharmacy without completing diagnosis - please verify this is intentional');
    }

    if (workflowData.currentDepartment === 'laboratory' && 
        workflowData.stageData?.status !== 'completed') {
      errors.push('Laboratory tests must be completed before proceeding to pharmacy');
    }

    const result: ValidationResult = {
      isValid: errors.length === 0,
      errors,
      warnings
    };

    setIsValidating(false);
    return showValidationResults(result);
  }, [showValidationResults]);

  return {
    // State
    validationErrors,
    validationWarnings,
    isValidating,

    // Individual validators
    validatePatient,
    validateConsultation,
    validateLaboratory,
    validatePharmacy,
    validateWorkflow,
    validateInventory,

    // Form validation
    validateField,
    validateForm,
    validateWorkflowProgression,

    // Utilities
    clearValidation,
    showValidationResults
  };
};
