import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';


export const useSupabaseData = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Patients
  const usePatients = () => {
    return useQuery({
      queryKey: ['patients', user?.id],
      queryFn: async () => {
        if (!user) return [];
        const { data, error } = await supabase
          .from('patients')
          .select(`
            *,
            medical_departments (
              id,
              name,
              description,
              icon,
              color
            )
          `)
          .eq('user_id', user.id)
          .order('created_at', { ascending: false });

        if (error) throw error;
        return data || [];
      },
      enabled: !!user
    });
  };

  // Reception visible patients (excludes those sent to consultation > 24h ago)
  const useReceptionPatients = () => {
    return useQuery({
      queryKey: ['reception-patients', user?.id],
      queryFn: async () => {
        if (!user) return [];
        try {
          const { data, error } = await supabase
            .rpc('get_reception_visible_patients');

          if (error) {
            console.warn('Reception patients query error:', error);
            return [];
          }
          return data || [];
        } catch (error) {
          console.warn('Reception patients query failed:', error);
          return [];
        }
      },
      enabled: !!user,
      retry: 1,
      staleTime: 2 * 60 * 1000, // Cache for 2 minutes (shorter for real-time updates)
    });
  };

  // Medical departments
  const useMedicalDepartments = () => {
    return useQuery({
      queryKey: ['medical-departments'],
      queryFn: async () => {
        try {
          const { data, error } = await supabase
            .rpc('get_active_medical_departments');

          if (error) {
            console.warn('Medical departments query error:', error);
            return [];
          }
          return data || [];
        } catch (error) {
          console.warn('Medical departments query failed:', error);
          return [];
        }
      },
      staleTime: 10 * 60 * 1000, // Cache for 10 minutes (departments don't change often)
    });
  };

  // Appointments
  const useAppointments = () => {
    return useQuery({
      queryKey: ['appointments', user?.id],
      queryFn: async () => {
        if (!user) return [];
        const { data, error } = await supabase
          .from('appointments')
          .select('*')
          .eq('user_id', user.id)
          .order('date_time', { ascending: true });
        
        if (error) throw error;
        return data || [];
      },
      enabled: !!user
    });
  };

  // Doctors
  const useDoctors = () => {
    return useQuery({
      queryKey: ['doctors', user?.id],
      queryFn: async () => {
        if (!user) return [];
        const { data, error } = await supabase
          .from('doctors')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false });
        
        if (error) throw error;
        return data || [];
      },
      enabled: !!user
    });
  };

  // Departments
  const useDepartments = () => {
    return useQuery({
      queryKey: ['departments', user?.id],
      queryFn: async () => {
        if (!user) return [];
        const { data, error } = await supabase
          .from('departments')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false });
        
        if (error) throw error;
        return data || [];
      },
      enabled: !!user
    });
  };

  // Medical Records
  const useMedicalRecords = () => {
    return useQuery({
      queryKey: ['medical_records', user?.id],
      queryFn: async () => {
        if (!user) return [];
        const { data, error } = await supabase
          .from('medical_records')
          .select('*')
          .eq('user_id', user.id)
          .order('date', { ascending: false });
        
        if (error) throw error;
        return data || [];
      },
      enabled: !!user
    });
  };

  // Lab Tests
  const useLabTests = () => {
    return useQuery({
      queryKey: ['lab_tests', user?.id],
      queryFn: async () => {
        if (!user) return [];
        const { data, error } = await supabase
          .from('lab_tests')
          .select('*')
          .eq('user_id', user.id)
          .order('test_date', { ascending: false });
        
        if (error) throw error;
        return data || [];
      },
      enabled: !!user
    });
  };

  // Dashboard Stats
  const useDashboardStats = () => {
    return useQuery({
      queryKey: ['dashboard_stats', user?.id],
      queryFn: async () => {
        if (!user) return null;
        const { data, error } = await supabase.rpc('get_dashboard_stats', {
          user_uuid: user.id
        });
        
        if (error) throw error;
        return data?.[0] || { total_patients: 0, today_appointments: 0, total_doctors: 0, total_departments: 0, total_revenue: 0, total_expenses: 0, occupancy_rate: 0 };
      },
      enabled: !!user
    });
  };

  // Pharmacy Inventory
  const usePharmacyInventory = () => {
    return useQuery({
      queryKey: ['pharmacy_inventory', user?.id],
      queryFn: async () => {
        if (!user) return [];
        const { data, error } = await supabase
          .from('pharmacy_inventory')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false });
        
        if (error) throw error;
        return data || [];
      },
      enabled: !!user
    });
  };

  // Settings
  const useSettings = () => {
    return useQuery({
      queryKey: ['settings', user?.id],
      queryFn: async () => {
        if (!user) return null;
        try {
          // First try to get existing settings
          const { data, error } = await supabase
            .from('settings')
            .select('*')
            .eq('user_id', user.id)
            .maybeSingle();

          if (error) {
            console.warn('Settings query error:', error);
            return null;
          }

          // If no settings exist, create default settings
          if (!data) {
            const { data: newSettings, error: createError } = await supabase
              .from('settings')
              .insert([{ user_id: user.id, hospital_name: 'My Hospital' }])
              .select()
              .single();

            if (createError) {
              console.warn('Failed to create default settings:', createError);
              return null;
            }
            return newSettings;
          }

          return data;
        } catch (error) {
          console.warn('Settings query failed:', error);
          return null;
        }
      },
      enabled: !!user,
      retry: 1, // Retry once if it fails
      staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    });
  };

  // Billing Invoices
  const useBillingInvoices = () => {
    return useQuery({
      queryKey: ['billing_invoices', user?.id],
      queryFn: async () => {
        if (!user) return [];
        const { data, error } = await supabase
          .from('billing_invoices')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false });
        
        if (error) throw error;
        return data || [];
      },
      enabled: !!user
    });
  };

  // Revenue Entries
  const useRevenueEntries = () => {
    return useQuery({
      queryKey: ['revenue_entries', user?.id],
      queryFn: async () => {
        if (!user) return [];
        const { data, error } = await supabase
          .from('revenue_entries')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false });
        
        if (error) throw error;
        return data || [];
      },
      enabled: !!user
    });
  };

  // Expense Entries
  const useExpenseEntries = () => {
    return useQuery({
      queryKey: ['expense_entries', user?.id],
      queryFn: async () => {
        if (!user) return [];
        const { data, error } = await supabase
          .from('expense_entries')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false });
        
        if (error) throw error;
        return data || [];
      },
      enabled: !!user
    });
  };

  // Profile
  const useProfile = () => {
    return useQuery({
      queryKey: ['profile', user?.id],
      queryFn: async () => {
        if (!user) return null;
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();
        
        if (error && error.code !== 'PGRST116') throw error;
        return data;
      },
      enabled: !!user
    });
  };

  // Notifications
  const useNotifications = () => {
    return useQuery({
      queryKey: ['notifications', user?.id],
      queryFn: async () => {
        if (!user) return [];
        const { data, error } = await supabase
          .from('notifications')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false });
        
        if (error) throw error;
        return data || [];
      },
      enabled: !!user
    });
  };

  // Invoices - using billing_invoices table
  const useInvoices = () => {
    return useQuery({
      queryKey: ['invoices', user?.id],
      queryFn: async () => {
        if (!user) return [];
        const { data, error } = await supabase
          .from('billing_invoices')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false });
        
        if (error) throw error;
        return data || [];
      },
      enabled: !!user
    });
  };

  // Mutations
  const createPatient = useMutation({
    mutationFn: async (patientData: any) => {
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('patients')
        .insert([{ ...patientData, user_id: user.id }])
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['patients'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard_stats'] });
      toast({
        title: "Success",
        description: "Patient added successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add patient",
        variant: "destructive",
      });
    }
  });

  // Create Patient with Workflow
  const createPatientWithWorkflow = useMutation({
    mutationFn: async (patientData: any) => {
      if (!user) throw new Error('User not authenticated');

      // Create patient first with workflow status
      const { data: patient, error: patientError } = await supabase
        .from('patients')
        .insert([{
          ...patientData,
          user_id: user.id,
          workflow_status: 'sent_to_consultation' // Automatically send to consultation
        }])
        .select()
        .single();

      if (patientError) throw patientError;

      // Create workflow for the patient
      const { data: workflow, error: workflowError } = await supabase
        .from('patient_workflows')
        .insert([{
          patient_id: patient.id,
          user_id: user.id,
          current_department: 'consultation', // Start at consultation
          previous_department: 'reception',
          notes: 'Patient registered and automatically sent to consultation'
        }])
        .select()
        .single();

      if (workflowError) throw workflowError;

      // Update patient with workflow_id and set sent_to_consultation_at
      const { data: updatedPatient, error: updateError } = await supabase
        .from('patients')
        .update({
          workflow_id: workflow.id,
          sent_to_consultation_at: new Date().toISOString()
        })
        .eq('id', patient.id)
        .select()
        .single();

      if (updateError) throw updateError;

      return { patient: updatedPatient, workflow };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['patients'] });
      queryClient.invalidateQueries({ queryKey: ['reception-patients'] });
      queryClient.invalidateQueries({ queryKey: ['patient_workflows'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard_stats'] });
      toast({
        title: "Success",
        description: "Patient registered and automatically sent to consultation",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to register patient",
        variant: "destructive",
      });
    }
  });

  // Update Patient Workflow Status
  const updatePatientWorkflowStatus = useMutation({
    mutationFn: async ({ patientId, status }: { patientId: string, status: string }) => {
      if (!user) throw new Error('User not authenticated');

      const { error } = await supabase
        .rpc('update_patient_workflow_status', {
          patient_id: patientId,
          new_status: status
        });

      if (error) throw error;
      return { patientId, status };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['patients'] });
      queryClient.invalidateQueries({ queryKey: ['reception-patients'] });
      queryClient.invalidateQueries({ queryKey: ['patient_workflows'] });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update patient status",
        variant: "destructive",
      });
    }
  });

  const createAppointment = useMutation({
    mutationFn: async (appointmentData: any) => {
      if (!user) throw new Error('User not authenticated');
      
      const { data, error } = await supabase
        .from('appointments')
        .insert([{ ...appointmentData, user_id: user.id }])
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['appointments'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard_stats'] });
      toast({
        title: "Success",
        description: "Appointment scheduled successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to schedule appointment",
        variant: "destructive",
      });
    }
  });

  const createDoctor = useMutation({
    mutationFn: async (doctorData: any) => {
      if (!user) throw new Error('User not authenticated');
      
      const { data, error } = await supabase
        .from('doctors')
        .insert([{ ...doctorData, user_id: user.id }])
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['doctors'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard_stats'] });
      toast({
        title: "Success",
        description: "Doctor added successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add doctor",
        variant: "destructive",
      });
    }
  });

  const createDepartment = useMutation({
    mutationFn: async (departmentData: any) => {
      if (!user) throw new Error('User not authenticated');
      
      const { data, error } = await supabase
        .from('departments')
        .insert([{ ...departmentData, user_id: user.id }])
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['departments'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard_stats'] });
      toast({
        title: "Success",
        description: "Department added successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add department",
        variant: "destructive",
      });
    }
  });

  const createMedicalRecord = useMutation({
    mutationFn: async (recordData: any) => {
      if (!user) throw new Error('User not authenticated');
      
      const { data, error } = await supabase
        .from('medical_records')
        .insert([{ ...recordData, user_id: user.id }])
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['medical_records'] });
      toast({
        title: "Success",
        description: "Medical record added successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add medical record",
        variant: "destructive",
      });
    }
  });

  const createLabTest = useMutation({
    mutationFn: async (labTestData: any) => {
      if (!user) throw new Error('User not authenticated');
      
      const { data, error } = await supabase
        .from('lab_tests')
        .insert([{ ...labTestData, user_id: user.id }])
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['lab_tests'] });
      toast({
        title: "Success",
        description: "Lab test result added successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add lab test result",
        variant: "destructive",
      });
    }
  });

  const createPharmacyInventory = useMutation({
    mutationFn: async (inventoryData: any) => {
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('pharmacy_inventory')
        .insert([{ ...inventoryData, user_id: user.id }])
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['pharmacy_inventory'] });
      toast({
        title: "Success",
        description: "Medication added to inventory successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add medication to inventory",
        variant: "destructive",
      });
    }
  });

  const updatePharmacyInventory = useMutation({
    mutationFn: async ({ id, inventoryData }: { id: string; inventoryData: any }) => {
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('pharmacy_inventory')
        .update(inventoryData)
        .eq('id', id)
        .eq('user_id', user.id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['pharmacy_inventory'] });
      toast({
        title: "Success",
        description: "Medication updated successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update medication",
        variant: "destructive",
      });
    }
  });

  const deletePharmacyInventory = useMutation({
    mutationFn: async (id: string) => {
      if (!user) throw new Error('User not authenticated');

      const { error } = await supabase
        .from('pharmacy_inventory')
        .delete()
        .eq('id', id)
        .eq('user_id', user.id);

      if (error) throw error;
      return { id };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['pharmacy_inventory'] });
      toast({
        title: "Success",
        description: "Medication deleted successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to delete medication",
        variant: "destructive",
      });
    }
  });

  const updateSettings = () => {
    return useMutation({
      mutationFn: async (settingsData: any) => {
        if (!user) throw new Error('User not authenticated');

        try {
          // First check if settings exist
          const { data: existingSettings } = await supabase
            .from('settings')
            .select('id')
            .eq('user_id', user.id)
            .maybeSingle();

          let result: any;
          if (existingSettings) {
            // Update existing settings
            const { data, error } = await supabase
              .from('settings')
              .update(settingsData)
              .eq('user_id', user.id)
              .select()
              .single();

            if (error) throw error;
            result = data;
          } else {
            // Create new settings
            const { data, error } = await supabase
              .from('settings')
              .insert([{ ...settingsData, user_id: user.id }])
              .select()
              .single();

            if (error) throw error;
            result = data;
          }

          return result;
        } catch (error) {
          console.error('Settings update failed:', error);
          throw error;
        }
      },
      onSuccess: (data) => {
        queryClient.invalidateQueries({ queryKey: ['settings'] });

        // Trigger currency update event if currency was changed
        if (data?.currency) {
          window.dispatchEvent(new CustomEvent('currencyChanged', { detail: data.currency }));
        }

        toast({
          title: "Success",
          description: "Settings updated successfully",
        });
      },
      onError: (error: any) => {
        console.error('Settings update error:', error);
        toast({
          title: "Error",
          description: "Failed to update settings. Please try again.",
          variant: "destructive",
        });
      }
    });
  };

  const createBillingInvoice = useMutation({
    mutationFn: async (invoiceData: any) => {
      if (!user) throw new Error('User not authenticated');
      
      const { data, error } = await supabase
        .from('billing_invoices')
        .insert([{ ...invoiceData, user_id: user.id }])
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['billing_invoices'] });
      toast({
        title: "Success",
        description: "Invoice created successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create invoice",
        variant: "destructive",
      });
    }
  });

  // Revenue Entry Mutations
  const createRevenueEntry = useMutation({
    mutationFn: async (revenueData: any) => {
      if (!user) throw new Error('User not authenticated');
      
      const { data, error } = await supabase
        .from('revenue_entries')
        .insert([{ ...revenueData, user_id: user.id }])
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['revenue_entries'] });
      toast({
        title: "Success",
        description: "Revenue entry added successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add revenue entry",
        variant: "destructive",
      });
    }
  });

  // Expense Entry Mutations
  const createExpenseEntry = useMutation({
    mutationFn: async (expenseData: any) => {
      if (!user) throw new Error('User not authenticated');
      
      const { data, error } = await supabase
        .from('expense_entries')
        .insert([{ ...expenseData, user_id: user.id }])
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['expense_entries'] });
      toast({
        title: "Success",
        description: "Expense entry added successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add expense entry",
        variant: "destructive",
      });
    }
  });

  // Profile Mutations
  const updateProfile = () => {
    return useMutation({
      mutationFn: async (profileData: any) => {
        if (!user) throw new Error('User not authenticated');
        
        const { data, error } = await supabase
          .from('profiles')
          .upsert([{ ...profileData, id: user.id }])
          .select()
          .single();
        
        if (error) throw error;
        return data;
      },
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['profile'] });
        toast({
          title: "Success",
          description: "Profile updated successfully",
        });
      },
      onError: (error: any) => {
        toast({
          title: "Error",
          description: error.message || "Failed to update profile",
          variant: "destructive",
        });
      }
    });
  };

  // Mark notification as read
  const markNotificationAsRead = useMutation({
    mutationFn: async (notificationId: string) => {
      if (!user) throw new Error('User not authenticated');
      
      const { data, error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('id', notificationId)
        .eq('user_id', user.id);
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
    }
  });

  return {
    // Queries
    usePatients,
    useReceptionPatients,
    useMedicalDepartments,
    useAppointments,
    useDoctors,
    useDepartments,
    useMedicalRecords,
    useLabTests,
    useDashboardStats,
    usePharmacyInventory,
    useSettings,
    useBillingInvoices,
    useRevenueEntries,
    useExpenseEntries,
    useProfile,
    useNotifications,
    useInvoices,

    // Mutations
    createPatient,
    createPatientWithWorkflow,
    updatePatientWorkflowStatus,
    createAppointment,
    createDoctor,
    createDepartment,
    createMedicalRecord,
    createLabTest,
    createPharmacyInventory,
    updatePharmacyInventory,
    deletePharmacyInventory,
    updateSettings,
    createBillingInvoice,
    createRevenueEntry,
    createExpenseEntry,
    updateProfile,
    markNotificationAsRead
  };
};
