
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';

export const useUserAccess = () => {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['user_access', user?.id],
    queryFn: async () => {
      if (!user) return null;
      
      const { data, error } = await supabase
        .from('user_access')
        .select('*')
        .eq('user_id', user.id)
        .single();
      
      if (error) {
        // If no user_access record exists, return default values
        if (error.code === 'PGRST116') {
          return {
            is_enabled: true,
            is_admin: false
          };
        }
        throw error;
      }
      
      return data;
    },
    enabled: !!user
  });
};

export const useIsAdmin = () => {
  const { data: userAccess } = useUserAccess();
  return userAccess?.is_admin || false;
};

export const useIsUserEnabled = () => {
  const { data: userAccess } = useUserAccess();
  return userAccess?.is_enabled !== false; // Default to true if no record
};
