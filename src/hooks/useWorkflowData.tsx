
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';

export const useWorkflowData = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Patient Workflows
  const usePatientWorkflows = () => {
    return useQuery({
      queryKey: ['patient_workflows', user?.id],
      queryFn: async () => {
        if (!user) return [];
        const { data, error } = await supabase
          .from('patient_workflows')
          .select(`
            *,
            patients!inner(
              id,
              patient_name,
              email,
              phone_number,
              date_of_birth
            )
          `)
          .eq('user_id', user.id)
          .order('updated_at', { ascending: false });
        
        if (error) throw error;
        return data || [];
      },
      enabled: !!user
    });
  };

  // Consultation Records
  const useConsultationRecords = () => {
    return useQuery({
      queryKey: ['consultation_records', user?.id],
      queryFn: async () => {
        if (!user) return [];

        try {
          // Use explicit column selection including fee fields
          const { data, error } = await supabase
            .from('consultation_records')
            .select(`
              id,
              patient_id,
              workflow_id,
              user_id,
              doctor_name,
              diagnosis,
              treatment_plan,
              vital_signs,
              symptoms,
              consultation_date,
              consultation_type,
              notes,
              fee_amount,
              fee_paid,
              fee_notes,
              start_time,
              end_time,
              lab_tests_required,
              department_id,
              prescribed_medicines,
              created_at,
              updated_at
            `)
            .eq('user_id', user.id)
            .order('consultation_date', { ascending: false });

          if (error) {
            console.error('Consultation records query error:', error);
            throw error;
          }

          console.log('🔍 CONSULTATION RECORDS FETCHED:', data?.length || 0, 'records');
          if (data && data.length > 0) {
            console.log('🔍 FIRST CONSULTATION RECORD:', data[0]);
          }

          return data || [];
        } catch (error) {
          console.error('Failed to fetch consultation records:', error);
          throw error;
        }
      },
      enabled: !!user,
      retry: 2, // Retry up to 2 times
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
      staleTime: 0 // No cache to ensure fresh data
    });
  };

  // Laboratory Records
  const useLaboratoryRecords = () => {
    return useQuery({
      queryKey: ['laboratory_records', user?.id],
      queryFn: async () => {
        if (!user) return [];

        try {
          // Use explicit column selection including all fee fields
          const { data, error } = await supabase
            .from('laboratory_records')
            .select(`
              id,
              patient_id,
              workflow_id,
              user_id,
              technician_name,
              test_type,
              status,
              lab_notes,
              test_results,
              reference_ranges,
              test_date,
              fee_amount,
              fee_paid,
              fee_notes,
              start_time,
              end_time,
              prescribed_medicines,
              created_at,
              updated_at
            `)
            .eq('user_id', user.id)
            .order('test_date', { ascending: false });

          if (error) {
            console.error('Laboratory records query error:', error);
            throw error;
          }

          console.log('🔍 LABORATORY RECORDS FETCHED:', data?.length || 0, 'records');
          if (data && data.length > 0) {
            console.log('🔍 FIRST LABORATORY RECORD:', data[0]);
          }

          return data || [];
        } catch (error) {
          console.error('Failed to fetch laboratory records:', error);
          throw error;
        }
      },
      enabled: !!user,
      retry: 3,
      retryDelay: 1000,
      staleTime: 0 // No cache to ensure fresh data
    });
  };

  // Pharmacy Records
  const usePharmacyRecords = () => {
    return useQuery({
      queryKey: ['pharmacy_records', user?.id],
      queryFn: async () => {
        if (!user) return [];

        try {
          // Use explicit column selection including fee fields
          const { data, error } = await supabase
            .from('pharmacy_records')
            .select(`
              id,
              patient_id,
              workflow_id,
              user_id,
              pharmacist_name,
              medications,
              notes,
              dispensed_date,
              total_cost,
              fee_amount,
              fee_paid,
              fee_notes,
              created_at,
              updated_at
            `)
            .eq('user_id', user.id)
            .order('dispensed_date', { ascending: false });

          if (error) {
            console.error('Pharmacy records query error:', error);
            throw error;
          }

          console.log('🔍 PHARMACY RECORDS FETCHED:', data?.length || 0, 'records');
          if (data && data.length > 0) {
            console.log('🔍 FIRST PHARMACY RECORD:', data[0]);
          }

          return data || [];
        } catch (error) {
          console.error('Failed to fetch pharmacy records:', error);
          throw error;
        }
      },
      enabled: !!user,
      staleTime: 0 // No cache to ensure fresh data
    });
  };

  // Create Patient Workflow
  const createPatientWorkflow = useMutation({
    mutationFn: async ({ patientId, assignedTo, notes }: { patientId: string; assignedTo?: string; notes?: string }) => {
      if (!user) throw new Error('User not authenticated');
      
      const { data, error } = await supabase
        .from('patient_workflows')
        .insert([{
          patient_id: patientId,
          user_id: user.id,
          current_department: 'reception',
          assigned_to: assignedTo,
          notes: notes
        }])
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['patient_workflows'] });
      toast({
        title: "Success",
        description: "Patient workflow created successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create patient workflow",
        variant: "destructive",
      });
    }
  });

  // Advance Workflow
  const advanceWorkflow = useMutation({
    mutationFn: async ({ workflowId, assignedTo, notes }: { workflowId: string; assignedTo?: string; notes?: string }) => {
      if (!user) throw new Error('User not authenticated');
      
      const { data, error } = await supabase.rpc('advance_patient_workflow', {
        p_workflow_id: workflowId,
        p_user_id: user.id,
        p_assigned_to: assignedTo,
        p_notes: notes
      });
      
      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['patient_workflows'] });
      if (data && data.length > 0) {
        toast({
          title: "Success",
          description: data[0].message,
        });
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to advance workflow",
        variant: "destructive",
      });
    }
  });

  // Create Consultation Record
  const createConsultationRecord = useMutation({
    mutationFn: async (consultationData: any) => {
      if (!user) throw new Error('User not authenticated');

      try {
        const { data, error } = await supabase
          .from('consultation_records')
          .insert([{ ...consultationData, user_id: user.id }])
          .select()
          .single();

        if (error) {
          throw error;
        }

        return data;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['consultation_records'] });
      queryClient.invalidateQueries({ queryKey: ['patients'] });
      toast({
        title: "Success",
        description: "Consultation record added successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add consultation record",
        variant: "destructive",
      });
    }
  });

  // Create Laboratory Record
  const createLaboratoryRecord = useMutation({
    mutationFn: async (labData: any) => {
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('laboratory_records')
        .insert([{ ...labData, user_id: user.id }])
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['laboratory_records'] });
      toast({
        title: "Success",
        description: "Laboratory record added successfully",
      });
    },
    onError: (error: any) => {
      // Log detailed error information for debugging
      console.error('Create laboratory record error:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code,
        status: error.status,
        fullError: error
      });

      // Don't show toast here - let the calling component handle error display
      // This prevents duplicate error messages when the operation actually succeeds
    }
  });

  // Update Laboratory Record
  const updateLaboratoryRecord = useMutation({
    mutationFn: async ({ recordId, labData, showToast = true }: { recordId: string; labData: any; showToast?: boolean }) => {
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('laboratory_records')
        .update({ ...labData, updated_at: new Date().toISOString() })
        .eq('id', recordId)
        .eq('user_id', user.id)
        .select()
        .single();

      if (error) throw error;
      return { data, showToast };
    },
    onSuccess: (result) => {
      // Invalidate all related queries to ensure data consistency
      queryClient.invalidateQueries({ queryKey: ['laboratory_records'] });
      queryClient.invalidateQueries({ queryKey: ['consultation_records'] });
      queryClient.invalidateQueries({ queryKey: ['patients'] });
      queryClient.invalidateQueries({ queryKey: ['patient_workflows'] });

      if (result.showToast) {
        toast({
          title: "Success",
          description: "Laboratory record updated successfully",
        });
      }
    },
    onError: (error: any) => {
      // Log error but don't show toast - let the calling component handle error display
      console.error('Update laboratory record error:', error);

      // Don't show toast here to prevent confusing users when operations actually succeed
      // The calling component will handle showing appropriate messages
    }
  });

  // Create Pharmacy Record with Inventory Management
  const createPharmacyRecord = useMutation({
    mutationFn: async (pharmacyData: any) => {
      if (!user) throw new Error('User not authenticated');

      // Start a transaction to handle both pharmacy record and inventory updates
      const { data: pharmacyRecord, error: pharmacyError } = await supabase
        .from('pharmacy_records')
        .insert([{ ...pharmacyData, user_id: user.id }])
        .select()
        .single();

      if (pharmacyError) throw pharmacyError;

      // Update inventory for each medication
      if (pharmacyData.medications && Array.isArray(pharmacyData.medications)) {
        for (const medication of pharmacyData.medications) {
          if (medication.name && medication.quantity > 0) {
            // Find the medication in inventory
            const { data: inventoryItem, error: findError } = await supabase
              .from('pharmacy_inventory')
              .select('*')
              .eq('medication_name', medication.name)
              .eq('user_id', user.id)
              .single();

            if (!findError && inventoryItem) {
              // Update stock quantity
              const newQuantity = Math.max(0, inventoryItem.stock_quantity - medication.quantity);
              const { error: updateError } = await supabase
                .from('pharmacy_inventory')
                .update({
                  stock_quantity: newQuantity,
                  updated_at: new Date().toISOString()
                })
                .eq('id', inventoryItem.id);

              if (updateError) {
                console.warn(`Failed to update inventory for ${medication.name}:`, updateError);
              }
            }
          }
        }
      }

      return pharmacyRecord;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['pharmacy_records'] });
      queryClient.invalidateQueries({ queryKey: ['pharmacy_inventory'] });
      toast({
        title: "Success",
        description: "Medications dispensed and inventory updated successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to dispense medications",
        variant: "destructive",
      });
    }
  });

  // Complete Department Stage and Auto-Advance Workflow
  const completeStageAndAdvance = useMutation({
    mutationFn: async ({ workflowId, stageData, autoAdvance = true }: {
      workflowId: string;
      stageData: any;
      autoAdvance?: boolean;
    }) => {
      if (!user) throw new Error('User not authenticated');

      // Get current workflow to determine stage
      const { data: workflow, error: workflowError } = await supabase
        .from('patient_workflows')
        .select('*')
        .eq('id', workflowId)
        .eq('user_id', user.id)
        .single();

      if (workflowError) throw workflowError;

      let stageResult: any;

      // Create appropriate record based on current department
      switch (workflow.current_department) {
        case 'consultation':
          const { data: consultationData, error: consultationError } = await supabase
            .from('consultation_records')
            .insert([{ ...stageData, user_id: user.id, workflow_id: workflowId }])
            .select()
            .single();
          if (consultationError) throw consultationError;
          stageResult = consultationData;
          break;

        case 'laboratory':
          const { data: labData, error: labError } = await supabase
            .from('laboratory_records')
            .insert([{ ...stageData, user_id: user.id, workflow_id: workflowId }])
            .select()
            .single();
          if (labError) throw labError;
          stageResult = labData;
          break;

        case 'pharmacy':
          // Handle pharmacy with inventory management
          const { data: pharmacyData, error: pharmacyError } = await supabase
            .from('pharmacy_records')
            .insert([{ ...stageData, user_id: user.id, workflow_id: workflowId }])
            .select()
            .single();
          if (pharmacyError) throw pharmacyError;

          // Update inventory
          if (stageData.medications && Array.isArray(stageData.medications)) {
            for (const medication of stageData.medications) {
              if (medication.name && medication.quantity > 0) {
                const { data: inventoryItem } = await supabase
                  .from('pharmacy_inventory')
                  .select('*')
                  .eq('medication_name', medication.name)
                  .eq('user_id', user.id)
                  .single();

                if (inventoryItem) {
                  const newQuantity = Math.max(0, inventoryItem.stock_quantity - medication.quantity);
                  await supabase
                    .from('pharmacy_inventory')
                    .update({
                      stock_quantity: newQuantity,
                      updated_at: new Date().toISOString()
                    })
                    .eq('id', inventoryItem.id);
                }
              }
            }
          }
          stageResult = pharmacyData;
          break;

        default:
          throw new Error('Invalid department for stage completion');
      }

      // Auto-advance workflow if requested
      if (autoAdvance) {
        const { data: advanceResult, error: advanceError } = await supabase.rpc('advance_patient_workflow', {
          p_workflow_id: workflowId,
          p_user_id: user.id,
          p_notes: `Completed ${workflow.current_department} stage`
        });

        if (advanceError) throw advanceError;
        return { stageResult, advanceResult };
      }

      return { stageResult };
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['patient_workflows'] });
      queryClient.invalidateQueries({ queryKey: ['consultation_records'] });
      queryClient.invalidateQueries({ queryKey: ['laboratory_records'] });
      queryClient.invalidateQueries({ queryKey: ['pharmacy_records'] });
      queryClient.invalidateQueries({ queryKey: ['pharmacy_inventory'] });

      toast({
        title: "Success",
        description: data.advanceResult ?
          "Stage completed and patient advanced to next department" :
          "Stage completed successfully",
      });
    },
    onError: (error: any) => {
      // Log error but don't show toast - workflow completion errors are often non-critical
      console.error('Complete stage and advance error:', error);
    }
  });

  // Get Workflow Summary for Patient
  const useWorkflowSummary = (patientId: string) => {
    return useQuery({
      queryKey: ['workflow_summary', patientId, user?.id],
      queryFn: async () => {
        if (!user || !patientId) return null;
        const { data, error } = await supabase.rpc('get_patient_workflow_summary', {
          p_patient_id: patientId,
          p_user_id: user.id
        });

        if (error) throw error;
        return data?.[0] || null;
      },
      enabled: !!user && !!patientId
    });
  };

  // Get Department Queue
  const useDepartmentQueue = (department: string) => {
    return useQuery({
      queryKey: ['department_queue', department, user?.id],
      queryFn: async () => {
        if (!user) return [];
        const { data, error } = await supabase
          .from('patient_workflows')
          .select(`
            *,
            patients!inner(
              id,
              patient_name,
              email,
              phone_number,
              date_of_birth,
              blood_type,
              insurance
            )
          `)
          .eq('user_id', user.id)
          .eq('current_department', department as any) // Type assertion to handle string parameter
          .order('created_at', { ascending: true });

        if (error) throw error;
        return data || [];
      },
      enabled: !!user && !!department
    });
  };

  // Update Workflow Assignment
  const updateWorkflowAssignment = useMutation({
    mutationFn: async ({ workflowId, assignedTo, notes }: {
      workflowId: string;
      assignedTo: string;
      notes?: string;
    }) => {
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('patient_workflows')
        .update({
          assigned_to: assignedTo,
          notes: notes,
          updated_at: new Date().toISOString()
        })
        .eq('id', workflowId)
        .eq('user_id', user.id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['patient_workflows'] });
      toast({
        title: "Success",
        description: "Workflow assignment updated successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update assignment",
        variant: "destructive",
      });
    }
  });

  // Get Workflow Statistics
  const useWorkflowStats = () => {
    return useQuery({
      queryKey: ['workflow_stats', user?.id],
      queryFn: async () => {
        if (!user) return null;

        const { data: workflows, error } = await supabase
          .from('patient_workflows')
          .select('current_department, created_at, updated_at')
          .eq('user_id', user.id);

        if (error) throw error;

        const stats = {
          total: workflows?.length || 0,
          reception: workflows?.filter(w => w.current_department === 'reception').length || 0,
          consultation: workflows?.filter(w => w.current_department === 'consultation').length || 0,
          laboratory: workflows?.filter(w => w.current_department === 'laboratory').length || 0,
          pharmacy: workflows?.filter(w => w.current_department === 'pharmacy').length || 0,
          completed: workflows?.filter(w => w.current_department === 'completed').length || 0,
          averageProcessingTime: 0, // Calculate based on created_at and updated_at
          todayAdmissions: workflows?.filter(w => {
            const today = new Date().toDateString();
            return new Date(w.created_at).toDateString() === today;
          }).length || 0
        };

        return stats;
      },
      enabled: !!user
    });
  };

  return {
    // Queries
    usePatientWorkflows,
    useConsultationRecords,
    useLaboratoryRecords,
    usePharmacyRecords,
    useWorkflowSummary,
    useDepartmentQueue,
    useWorkflowStats,

    // Mutations
    createPatientWorkflow,
    advanceWorkflow,
    createConsultationRecord,
    createLaboratoryRecord,
    updateLaboratoryRecord,
    createPharmacyRecord,
    completeStageAndAdvance,
    updateWorkflowAssignment
  };
};
