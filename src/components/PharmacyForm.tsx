import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { format } from 'date-fns';
import { Calendar as CalendarIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useSupabaseData } from '@/hooks/useSupabaseData';

interface PharmacyFormProps {
  onClose: () => void;
  onSuccess?: () => void;
  existingEntry?: any; // Optional: for editing existing entries
}

export const PharmacyForm = ({ onClose, onSuccess, existingEntry }: PharmacyFormProps) => {
  const { createPharmacyInventory, updatePharmacyInventory } = useSupabaseData();
  const [expiryDate, setExpiryDate] = useState<Date | undefined>(existingEntry?.expiry_date ? new Date(existingEntry.expiry_date) : undefined);
  const [formData, setFormData] = useState({
    medication_name: existingEntry?.medication_name || '',
    stock_quantity: existingEntry?.stock_quantity || '',
    price: existingEntry?.price || '',
    status: existingEntry?.status || 'In Stock',
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (!formData.medication_name) {
      alert('Please enter medication name');
      return;
    }

    if (!formData.stock_quantity || Number(formData.stock_quantity) <= 0) {
      alert('Please enter a valid stock quantity');
      return;
    }

    if (!formData.price || Number(formData.price) <= 0) {
      alert('Please enter a valid price');
      return;
    }

    if (!expiryDate) {
      alert('Please select an expiry date');
      return;
    }

    try {
      const medicationData = {
        medication_name: formData.medication_name,
        stock_quantity: Number(formData.stock_quantity),
        price: Number(formData.price),
        expiry_date: format(expiryDate, 'yyyy-MM-dd'),
        status: formData.status,
      };

      if (existingEntry) {
        // Update existing medication
        await updatePharmacyInventory.mutateAsync({
          id: existingEntry.id,
          inventoryData: medicationData
        });
      } else {
        // Create new medication
        await createPharmacyInventory.mutateAsync(medicationData);
      }

      onSuccess?.();
      onClose();
    } catch (error) {
      console.error('Error saving medication:', error);
      alert(`Error ${existingEntry ? 'updating' : 'adding'} medication. Please try again.`);
    }
  };

  const statuses = ['In Stock', 'Low Stock', 'Out of Stock'];

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          {existingEntry ? 'Edit Pharmacy Entry' : 'Add New Pharmacy Entry'}
        </h2>
      </div>

      {/* Medication Details */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Medication Details</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="medication_name">Medication Name</Label>
            <Input
              id="medication_name"
              name="medication_name"
              value={formData.medication_name}
              onChange={handleChange}
              placeholder="Enter medication name"
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="stock_quantity">Stock Quantity</Label>
            <Input
              id="stock_quantity"
              name="stock_quantity"
              type="number"
              value={formData.stock_quantity}
              onChange={handleChange}
              placeholder="Enter stock quantity"
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="price">Price</Label>
            <Input
              id="price"
              name="price"
              type="number"
              step="0.01"
              value={formData.price}
              onChange={handleChange}
              placeholder="Enter price"
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="expiryDate">Expiry Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !expiryDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {expiryDate ? format(expiryDate, "PPP") : <span>Pick expiry date</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={expiryDate}
                  onSelect={setExpiryDate}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>
      </div>

      {/* Status */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Status</h3>
        <div className="grid grid-cols-1">
          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select value={formData.status} onValueChange={(value) => handleSelectChange('status', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                {statuses.map(status => (
                  <SelectItem key={status} value={status}>{status}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onClose}>Cancel</Button>
        <Button
          type="submit"
          disabled={createPharmacyInventory.isPending || updatePharmacyInventory.isPending}
          className="bg-green-600 hover:bg-green-700"
        >
          {(createPharmacyInventory.isPending || updatePharmacyInventory.isPending)
            ? (existingEntry ? 'Updating...' : 'Adding...')
            : existingEntry ? 'Save Changes' : 'Add Medication'
          }
        </Button>
      </div>
    </form>
  );
}; 