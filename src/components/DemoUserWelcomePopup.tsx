
import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { Star, Gift, MessageCircle, Mail, Phone, Sparkles } from 'lucide-react';

export const DemoUserWelcomePopup: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    let intervalId: NodeJS.Timeout;

    // Only show <NAME_EMAIL> user
    if (user?.email === '<EMAIL>') {
      // Set up interval to show popup every 2 minutes
      intervalId = setInterval(() => {
        setIsOpen(true);
      }, 120000); // 2 minutes = 120000 milliseconds

      // Show popup immediately on first load
      const initialTimeout = setTimeout(() => {
        setIsOpen(true);
      }, 120000); // First popup after 2 minutes

      // Cleanup function will clear both interval and initial timeout
      return () => {
        clearInterval(intervalId);
        clearTimeout(initialTimeout);
      };
    }

    // Cleanup if user <NAME_EMAIL>
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [user]);

  const handleDismiss = () => {
    setIsOpen(false);
    // Popup will appear again after 2 minutes due to the interval
  };

  // Don't render if user <NAME_EMAIL>
  if (user?.email !== '<EMAIL>') {
    return null;
  }

  return (
    <>
      {/* Popup Dialog */}
      <Dialog open={isOpen} onOpenChange={(open) => !open && handleDismiss()}>
        <DialogContent className="w-[95vw] max-w-[600px] max-h-[90vh] overflow-y-auto mx-4 sm:mx-auto [&>button]:w-8 [&>button]:h-8 [&>button>svg]:w-6 [&>button>svg]:h-6">
          <DialogHeader className="text-center sm:text-left">
            <div className="flex flex-col sm:flex-row items-center gap-4 mb-4">
              <div className="p-3 bg-gradient-to-r from-purple-500 to-blue-500 rounded-2xl shadow-lg">
                <Star className="h-8 w-8 text-white" />
              </div>
              <div className="flex-1">
                <DialogTitle className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                  Welcome to VertiQ Hospital Management System!
                </DialogTitle>
                <Badge variant="secondary" className="bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 mt-2 px-3 py-1">
                  <Gift className="h-3 w-3 mr-1" />
                  Professional Solutions Available
                </Badge>
              </div>
            </div>
            <DialogDescription className="text-base text-gray-600 text-center sm:text-left">
              Ready to transform your healthcare management? Get your own customized system today!
            </DialogDescription>
          </DialogHeader>
          
          <div className="py-4 space-y-6">
            {/* Main Content Section */}
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-xl border border-blue-200">
              <div className="text-center mb-4">
                <div className="flex items-center justify-center mb-3">
                  <div className="p-3 bg-purple-100 rounded-full mr-3">
                    <Sparkles className="h-6 w-6 text-purple-600" />
                  </div>
                  <h4 className="text-xl font-bold text-gray-900">Get Your Own System</h4>
                </div>
                <p className="text-gray-700 leading-relaxed mb-4">
                  Experience the full power of VertiQ Hospital Management System with your own dedicated instance. 
                  Whether you need our standard solution or custom development tailored to your specific requirements, 
                  we're here to help transform your healthcare operations.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                  <div className="flex items-center justify-center md:justify-start">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                    Complete patient management
                  </div>
                  <div className="flex items-center justify-center md:justify-start">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                    Custom development available
                  </div>
                  <div className="flex items-center justify-center md:justify-start">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                    Professional support included
                  </div>
                  <div className="flex items-center justify-center md:justify-start">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                    Flexible pricing options
                  </div>
                </div>
              </div>
            </div>

            {/* Contact Section */}
            <div className="bg-gradient-to-r from-orange-50 to-red-50 p-5 rounded-xl border border-orange-200">
              <h4 className="font-semibold text-gray-900 mb-3 flex items-center justify-center sm:justify-start">
                <div className="p-2 bg-orange-100 rounded-full mr-2">
                  <MessageCircle className="h-4 w-4 text-orange-600" />
                </div>
                Ready to Get Started?
              </h4>
              <p className="text-sm text-gray-700 mb-4 text-center sm:text-left">
                Contact us today to discuss your requirements and get a personalized quote for your hospital management system.
              </p>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                <Button
                  onClick={() => window.open('https://wa.me/254110860589?text=I%20want%20a%20hospital%20management%20system', '_blank')}
                  className="w-full bg-green-600 hover:bg-green-700 text-white py-3 rounded-xl shadow-md hover:shadow-lg transition-all"
                  size="lg"
                >
                  <Phone className="h-5 w-5 mr-2" />
                  WhatsApp Us
                </Button>
                <Button
                  onClick={() => {
                    const email = '<EMAIL>';

                    navigator.clipboard.writeText(email).then(() => {
                      toast({
                        title: "Email copied!",
                        description: `${email} has been copied to your clipboard`,
                        duration: 3000,
                      });
                    }).catch(() => {
                      toast({
                        title: "Copy failed",
                        description: `Please manually copy: ${email}`,
                        variant: "destructive",
                        duration: 5000,
                      });
                    });
                  }}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-xl shadow-md hover:shadow-lg transition-all"
                  size="lg"
                >
                  <Mail className="h-5 w-5 mr-2" />
                  <EMAIL>
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};
