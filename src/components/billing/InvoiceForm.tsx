import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  Card,
  CardContent
} from '@/components/ui/card';
import { CalendarIcon, Plus, Trash2 } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  amount: number;
}

interface InvoiceFormProps {
  existingInvoice?: any;
  onClose: () => void;
  onSubmit?: (data: any) => void;
}

const InvoiceForm = ({ existingInvoice, onClose, onSubmit }: InvoiceFormProps) => {
  const [patientId, setPatientId] = useState(existingInvoice?.patientId || '');
  const [patientName, setPatientName] = useState(existingInvoice?.patientName || '');
  const [invoiceDate, setInvoiceDate] = useState<Date | undefined>(
    existingInvoice?.date ? new Date(existingInvoice.date) : new Date()
  );
  const [dueDate, setDueDate] = useState<Date | undefined>(
    existingInvoice?.dueDate ? new Date(existingInvoice.dueDate) : undefined
  );
  const [status, setStatus] = useState(existingInvoice?.status || 'pending');
  const [items, setItems] = useState<InvoiceItem[]>(
    existingInvoice?.items?.map((item, index) => ({
      id: `item-${index}`,
      description: item.description,
      quantity: item.quantity || 1,
      unitPrice: item.unitPrice || item.amount,
      amount: item.amount
    })) || []
  );
  const [notes, setNotes] = useState(existingInvoice?.notes || '');

  // Sample data for patients
  const patients = [
    { id: 'P-001', name: 'John Smith' },
    { id: 'P-002', name: 'Emily Johnson' },
    { id: 'P-003', name: 'Michael Brown' },
    { id: 'P-004', name: 'Sarah Davis' },
    { id: 'P-005', name: 'Robert Wilson' }
  ];

  // Sample data for services
  const services = [
    { id: 'SRV-001', name: 'Consultation', price: 200.00 },
    { id: 'SRV-002', name: 'Blood Test - Complete Blood Count', price: 150.00 },
    { id: 'SRV-003', name: 'X-Ray - Chest', price: 300.00 },
    { id: 'SRV-004', name: 'Ultrasound - Abdominal', price: 250.00 },
    { id: 'SRV-005', name: 'EKG', price: 150.00 },
    { id: 'SRV-006', name: 'Physical Therapy (per session)', price: 150.00 },
    { id: 'SRV-007', name: 'Medication - Antibiotics', price: 100.00 },
    { id: 'SRV-008', name: 'Room Charges (per day)', price: 250.00 }
  ];

  // Calculate total amount
  const totalAmount = items.reduce((sum, item) => sum + item.amount, 0);

  // Handle patient selection
  const handlePatientSelect = (patientId: string) => {
    setPatientId(patientId);
    const selectedPatient = patients.find(p => p.id === patientId);
    if (selectedPatient) {
      setPatientName(selectedPatient.name);
    }
  };

  // Add new item
  const addItem = () => {
    const newItem: InvoiceItem = {
      id: `item-${Date.now()}`,
      description: '',
      quantity: 1,
      unitPrice: 0,
      amount: 0
    };
    setItems([...items, newItem]);
  };

  // Remove item
  const removeItem = (id: string) => {
    setItems(items.filter(item => item.id !== id));
  };

  // Update item
  const updateItem = (id: string, field: string, value: any) => {
    setItems(items.map(item => {
      if (item.id === id) {
        const updatedItem = { ...item, [field]: value };
        
        // Recalculate amount if quantity or unitPrice changes
        if (field === 'quantity' || field === 'unitPrice') {
          updatedItem.amount = updatedItem.quantity * updatedItem.unitPrice;
        }
        
        return updatedItem;
      }
      return item;
    }));
  };

  // Handle service selection
  const handleServiceSelect = (itemId: string, serviceId: string) => {
    const selectedService = services.find(s => s.id === serviceId);
    if (selectedService) {
      updateItem(itemId, 'description', selectedService.name);
      updateItem(itemId, 'unitPrice', selectedService.price);
      updateItem(itemId, 'amount', selectedService.price * (items.find(i => i.id === itemId)?.quantity || 1));
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const invoiceData = {
      patientId,
      patientName,
      date: invoiceDate ? format(invoiceDate, 'yyyy-MM-dd') : '',
      dueDate: dueDate ? format(dueDate, 'yyyy-MM-dd') : '',
      status,
      items,
      notes,
      amount: totalAmount
    };
    
    if (onSubmit) {
      onSubmit(invoiceData);
    }
    
    onClose();
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6 w-full max-w-3xl mx-auto overflow-hidden">
      <div>
        <h2 className="text-2xl font-bold">{existingInvoice ? 'Edit Invoice' : 'Create New Invoice'}</h2>
        <p className="text-gray-500">Enter the invoice details below</p>
      </div>
      
      {/* Patient Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Patient Information</h3>
        <div>
          <Label htmlFor="patient">Patient</Label>
          <Select value={patientId} onValueChange={handlePatientSelect}>
            <SelectTrigger>
              <SelectValue placeholder="Select a patient" />
            </SelectTrigger>
            <SelectContent>
              {patients.map(patient => (
                <SelectItem key={patient.id} value={patient.id}>
                  {patient.name} ({patient.id})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
      
      {/* Invoice Details */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Invoice Details</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="invoiceDate">Invoice Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !invoiceDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {invoiceDate ? format(invoiceDate, "PPP") : "Select date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={invoiceDate}
                  onSelect={setInvoiceDate}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
          <div>
            <Label htmlFor="dueDate">Due Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !dueDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {dueDate ? format(dueDate, "PPP") : "Select due date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={dueDate}
                  onSelect={setDueDate}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>
        <div>
          <Label htmlFor="status">Status</Label>
          <Select value={status} onValueChange={setStatus}>
            <SelectTrigger>
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="paid">Paid</SelectItem>
              <SelectItem value="overdue">Overdue</SelectItem>
              <SelectItem value="partial">Partial Payment</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      {/* Invoice Items */}
      <div className="space-y-4 overflow-x-auto">
          <h3 className="text-lg font-semibold">Invoice Items</h3>
        <Button type="button" variant="outline" size="sm" onClick={addItem} className="flex items-center space-x-1">
          <Plus className="h-4 w-4" />
          <span>Add Item</span>
          </Button>
        
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[200px]">Description</TableHead>
                <TableHead className="w-[80px]">Qty</TableHead>
                <TableHead className="w-[120px]">Unit Price</TableHead>
                <TableHead className="text-right">Amount</TableHead>
                <TableHead className="w-[60px]">Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {items.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center text-gray-500">
                    No items added. Click "Add Item" to add services to this invoice.
                  </TableCell>
                </TableRow>
              ) : (
                items.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>
                      <Select 
                        value={services.find(s => s.name === item.description)?.id || ''}
                         onValueChange={(serviceId) => handleServiceSelect(item.id, serviceId as string)}
                      >
                        <SelectTrigger>
                           <SelectValue placeholder="Select service" />
                        </SelectTrigger>
                        <SelectContent>
                          {services.map(service => (
                            <SelectItem key={service.id} value={service.id}>
                               {service.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                     </TableCell>
                    <TableCell>
                      <Input
                        type="number"
                        value={item.quantity}
                        onChange={(e) => updateItem(item.id, 'quantity', parseInt(e.target.value) || 0)}
                        min="1"
                        className="w-16 text-center"
                      />
                    </TableCell>
                    <TableCell>
                      <Input
                        type="number"
                        value={item.unitPrice}
                        onChange={(e) => updateItem(item.id, 'unitPrice', parseFloat(e.target.value) || 0)}
                        step="0.01"
                        min="0"
                        className="w-24"
                      />
                    </TableCell>
                    <TableCell className="text-right">
                      {totalAmount.toFixed(2)}
                    </TableCell>
                    <TableCell className="text-center">
                      <Button variant="destructive" size="icon" onClick={() => removeItem(item.id)}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        <div className="flex justify-end items-center space-x-4 text-lg font-semibold">
          <span>Total Amount:</span>
          <span>${totalAmount.toFixed(2)}</span>
        </div>
      </div>
      
      {/* Notes */}
      <div className="space-y-2">
        <Label htmlFor="notes">Notes</Label>
        <Textarea
          id="notes"
          placeholder="Add any additional notes here..."
          value={notes}
          onChange={(e) => setNotes(e.target.value)}
        />
      </div>
      
      {/* Form Actions */}
      <div className="flex justify-end space-x-4">
        <Button type="button" variant="outline" onClick={onClose}>
          Cancel
        </Button>
        <Button type="submit">
          {existingInvoice ? 'Save Changes' : 'Create Invoice'}
        </Button>
      </div>
    </form>
  );
};

export default InvoiceForm;
