
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { format } from 'date-fns';
import { Calendar as CalendarIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useSupabaseData } from '@/hooks/useSupabaseData';
import { useCurrency } from '@/contexts/CurrencyContext';
import { toast } from '@/hooks/use-toast';

interface MedicalRecordFormProps {
  onClose: () => void;
  existingRecord?: any;
}

export const MedicalRecordForm = ({ onClose, existingRecord }: MedicalRecordFormProps) => {
  const [recordDate, setRecordDate] = useState<Date | undefined>(
    existingRecord?.date ? new Date(existingRecord.date) : new Date()
  );
  const [formData, setFormData] = useState({
    patient_name: existingRecord?.patient_name || '',
    doctor: existingRecord?.doctor || '',
    record_type: existingRecord?.record_type || '',
    summary: existingRecord?.summary || '',
    fee_amount: existingRecord?.fee_amount || '',
    fee_paid: existingRecord?.fee_paid || false,
    fee_notes: existingRecord?.fee_notes || ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { createMedicalRecord } = useSupabaseData();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!recordDate) {
      alert('Please select a date');
      return;
    }

    setIsSubmitting(true);
    try {
      await createMedicalRecord.mutateAsync({
        ...formData,
        date: format(recordDate, 'yyyy-MM-dd'),
        fee_amount: parseFloat(formData.fee_amount) || 0,
        fee_paid: formData.fee_paid,
        fee_notes: formData.fee_notes
      });
      onClose();
    } catch (error) {
      console.error('Error creating medical record:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const recordTypes = [
    'Consultation Note',
    'Lab Result',
    'Imaging Report',
    'Procedure Report',
    'Discharge Summary',
    'Follow-up Note',
    'Prescription',
    'Diagnosis',
  ];

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          {existingRecord ? 'Edit Medical Record' : 'Add New Medical Record'}
        </h2>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Record Details</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="patient_name">Patient Name</Label>
            <Input
              id="patient_name"
              name="patient_name"
              value={formData.patient_name}
              onChange={handleChange}
              placeholder="Enter patient name"
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="doctor">Doctor</Label>
            <Input
              id="doctor"
              name="doctor"
              value={formData.doctor}
              onChange={handleChange}
              placeholder="Enter doctor name"
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="record_type">Record Type</Label>
            <Select value={formData.record_type} onValueChange={(value) => handleSelectChange('record_type', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select record type" />
              </SelectTrigger>
              <SelectContent>
                {recordTypes.map(type => (
                  <SelectItem key={type} value={type}>{type}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label>Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !recordDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {recordDate ? format(recordDate, "PPP") : <span>Pick a date</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={recordDate}
                  onSelect={setRecordDate}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="summary">Summary/Details</Label>
        <Textarea
          id="summary"
          name="summary"
          value={formData.summary}
          onChange={handleChange}
          placeholder="Enter record summary or details"
          className="min-h-[150px]"
          required
        />
      </div>

      {/* Fees Section */}
      <div className="space-y-4 border-t pt-4">
        <h3 className="text-lg font-medium text-gray-900">Medical Record Fees</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="fee_amount">Fee Amount</Label>
            <Input
              id="fee_amount"
              type="number"
              step="0.01"
              min="0"
              value={formData.fee_amount}
              onChange={(e) => {
                const amount = e.target.value;
                setFormData(prev => ({
                  ...prev,
                  fee_amount: amount,
                  // Automatically set as paid when amount is added
                  fee_paid: amount && parseFloat(amount) > 0 ? true : false
                }));
              }}
              placeholder="0.00"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="fee_paid">Payment Status</Label>
            <div className="flex items-center space-x-2">
              <input
                id="fee_paid"
                type="checkbox"
                checked={formData.fee_paid} // Direct logic: checked means PAID
                onChange={(e) => setFormData(prev => ({ ...prev, fee_paid: e.target.checked }))} // Direct logic
                className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
              />
              <Label htmlFor="fee_paid" className="text-sm font-normal">
                {formData.fee_paid ? '✅ Payment Received' : '❌ Mark as Paid'}
              </Label>
            </div>
            <p className="text-xs text-gray-500">
              {formData.fee_paid ? 'Payment has been received' : 'Check this box when payment is received'}
            </p>
          </div>
        </div>
        <div className="space-y-2">
          <Label htmlFor="fee_notes">Fee Notes (Optional)</Label>
          <Input
            id="fee_notes"
            type="text"
            value={formData.fee_notes}
            onChange={(e) => setFormData(prev => ({ ...prev, fee_notes: e.target.value }))}
            placeholder="Payment method, receipt number, etc."
          />
        </div>
      </div>

      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>
          Cancel
        </Button>
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? 'Saving...' : existingRecord ? 'Save Changes' : 'Add Record'}
        </Button>
      </div>
    </form>
  );
};
