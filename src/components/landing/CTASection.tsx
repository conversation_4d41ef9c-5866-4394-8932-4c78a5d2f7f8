import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowRight, Play, MessageCircle, Sparkles, CheckCircle } from 'lucide-react';

export const CTASection = () => {
  return (
    <section className="py-16 md:py-24 bg-gradient-to-br from-purple-900 via-purple-800 to-blue-900 text-white relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 right-0 w-64 h-64 md:w-96 md:h-96 bg-gradient-to-br from-purple-400/20 to-blue-400/20 rounded-full mix-blend-multiply filter blur-3xl"></div>
        <div className="absolute bottom-20 left-0 w-64 h-64 md:w-96 md:h-96 bg-gradient-to-br from-blue-400/20 to-indigo-400/20 rounded-full mix-blend-multiply filter blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-5xl mx-auto">
          {/* Main CTA Content */}
          <div className="text-center mb-12 md:mb-16">
            <div className="inline-flex items-center px-3 py-1.5 md:px-4 md:py-2 bg-white/10 backdrop-blur-sm rounded-full text-xs md:text-sm font-medium mb-6 md:mb-8">
              <Sparkles className="w-3 h-3 md:w-4 md:h-4 mr-1.5 md:mr-2" />
              <span className="hidden sm:inline">Ready to Transform Healthcare?</span>
              <span className="sm:hidden">Transform Healthcare</span>
            </div>

            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-6 md:mb-8 leading-tight px-2">
              Start Your Healthcare
              <span className="bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent"> Revolution</span>
              <br />Today
            </h2>

            <p className="text-base sm:text-lg md:text-xl text-purple-100 mb-8 md:mb-12 max-w-3xl mx-auto leading-relaxed px-4">
              Transform your healthcare facility with our comprehensive management platform.
              Streamline operations, improve patient care, and boost efficiency.
            </p>

            {/* Benefits List */}
            <div className="flex flex-col sm:grid sm:grid-cols-3 gap-3 sm:gap-4 md:gap-6 mb-8 md:mb-12 max-w-4xl mx-auto">
              <div className="flex items-center justify-center md:justify-start">
                <CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-green-400 mr-2 sm:mr-3" />
                <span className="text-purple-100 text-sm sm:text-base">Setup in under 24 hours</span>
              </div>
              <div className="flex items-center justify-center md:justify-start">
                <CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-green-400 mr-2 sm:mr-3" />
                <span className="text-purple-100 text-sm sm:text-base">No long-term contracts</span>
              </div>
              <div className="flex items-center justify-center md:justify-start">
                <CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-green-400 mr-2 sm:mr-3" />
                <span className="text-purple-100 text-sm sm:text-base">24/7 dedicated support</span>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row justify-center items-center gap-4 sm:gap-6 px-2">
              <Link to="/demo-login" className="w-full sm:w-auto">
                <Button size="lg" className="bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-gray-900 font-bold px-6 sm:px-8 py-3 sm:py-4 rounded-xl text-base sm:text-lg shadow-lg hover:shadow-xl transition-all duration-300 w-full">
                  <Play className="mr-1.5 sm:mr-2 h-4 w-4 sm:h-5 sm:w-5" />
                  Try Live Demo
                  <ArrowRight className="ml-1.5 sm:ml-2 h-4 w-4 sm:h-5 sm:w-5" />
                </Button>
              </Link>

              <Link to="/contact-us" className="w-full sm:w-auto">
                <Button size="lg" variant="outline" className="border-2 border-white/30 text-white hover:bg-white/10 backdrop-blur-sm px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-semibold text-base sm:text-lg transition-all duration-300 w-full">
                  <MessageCircle className="mr-1.5 sm:mr-2 h-4 w-4 sm:h-5 sm:w-5" />
                  Schedule Consultation
                </Button>
              </Link>
            </div>
          </div>

          {/* Simple Trust Indicators */}
          <div className="bg-white/10 backdrop-blur-sm rounded-xl lg:rounded-2xl p-4 sm:p-5 lg:p-6 border border-white/20">
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-6 lg:gap-8 text-center">
              <div className="flex items-center">
                <div className="text-lg sm:text-xl lg:text-2xl font-bold text-green-400 mr-1.5 sm:mr-2">✓</div>
                <span className="text-purple-100 text-sm sm:text-base">HIPAA Compliant</span>
              </div>
              <div className="flex items-center">
                <div className="text-lg sm:text-xl lg:text-2xl font-bold text-blue-400 mr-1.5 sm:mr-2">✓</div>
                <span className="text-purple-100 text-sm sm:text-base">Enterprise Security</span>
              </div>
              <div className="flex items-center">
                <div className="text-lg sm:text-xl lg:text-2xl font-bold text-yellow-400 mr-1.5 sm:mr-2">✓</div>
                <span className="text-purple-100 text-sm sm:text-base">Cloud-Based</span>
              </div>
            </div>
          </div>

          {/* Bottom Text */}
          <div className="text-center mt-8 md:mt-12 px-4">
            <p className="text-purple-200 text-xs sm:text-sm leading-relaxed">
              No setup fees • Cancel anytime • HIPAA compliant • SOC 2 certified
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};
