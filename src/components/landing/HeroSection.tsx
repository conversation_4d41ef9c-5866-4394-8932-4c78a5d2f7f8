
import React from 'react';
import { Link } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, <PERSON>R<PERSON>, <PERSON>geC<PERSON><PERSON>, Shield<PERSON>heck, Star, BarChart3, Video } from 'lucide-react';

export const HeroSection = () => {
  return (
    <section className="relative w-full flex justify-center items-center bg-[#f5f7fd] py-8 md:py-16 min-h-[80vh]">
      <div className="w-full max-w-6xl mx-auto flex flex-col md:flex-row items-center rounded-3xl shadow-xl bg-white px-4 md:px-8 py-8 md:py-12 gap-8 md:gap-10">
        {/* Left Side: Text */}
        <div className="flex-1 flex flex-col gap-4 md:gap-6">
          {/* Badge/Chip */}
          <span className="inline-flex items-center mb-1.5 w-fit px-3 py-1.5 text-sm font-medium rounded-full bg-blue-50 text-blue-600 border border-blue-100 shadow-sm">
            <BarChart3 className="w-4 h-4 mr-1.5 text-blue-400" />
            All-in-One Business Solution
          </span>
          {/* Heading */}
          <h1 className="font-bold text-3xl sm:text-4xl lg:text-5xl leading-tight text-gray-900 mb-2">
            Streamline Your{' '}
            <span className="text-blue-600">Business</span> Operations
          </h1>
          {/* Subtitle */}
          <p className="text-[1.06rem] leading-relaxed text-gray-600 max-w-lg mb-2">
            vertiQ helps you manage your business efficiently with powerful tools for financials, inventory, customers, and more - all in one platform.
          </p>
          {/* Button Group */}
          <div className="flex gap-3 mt-2 mb-3">
            <Link to="/contact-us">
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-7 shadow-md rounded-lg font-semibold transition-all text-base">
                Contact Us
              </Button>
            </Link>
            <Link to="/demo-login">
              <Button 
                size="lg" 
                variant="outline"
                className="border-gray-200 text-gray-800 bg-white hover:bg-gray-50 px-7 rounded-lg font-semibold text-base flex items-center gap-2 shadow-sm"
              >
                <Video className="w-4 h-4 mr-1 text-blue-500" />
                Watch Demo
              </Button>
            </Link>
          </div>
          {/* Features Line (like reference) */}
          <div className="flex flex-wrap items-center gap-4 mt-2 mb-0">
            <div className="flex items-center text-sm gap-1">
              <ShieldCheck className="text-green-500 w-4 h-4 mr-1" />
              <span className="mr-1 text-gray-600">SOC 2</span>
            </div>
            <div className="flex items-center text-sm gap-1">
              <BadgeCheck className="text-blue-500 w-4 h-4 mr-1" />
              <span className="mr-1 text-gray-600">99.9% Uptime</span>
            </div>
            <div className="flex items-center text-sm gap-1">
              <Star className="text-yellow-400 fill-yellow-400 w-4 h-4 mr-1" />
              <span className="text-gray-600">4.8/5 Rating</span>
            </div>
          </div>
        </div>
        {/* Right Side: Main Image */}
        <div className="flex-1 flex items-center justify-center w-full">
          <div className="relative rounded-2xl overflow-hidden shadow-lg bg-gray-100 flex items-center justify-center max-w-[480px] w-full h-[290px] md:h-[325px]">
            <img
              src="/slider-small-1.jpg"
              alt="Modern Hospital with Digital Management"
              className="w-full h-full object-cover rounded-2xl"
              draggable="false"
            />
            {/* Floating "Live Demo" badge */}
            <span className="absolute bottom-4 left-4 flex items-center gap-2 bg-white px-3 py-2 text-sm rounded-xl shadow border border-gray-100 font-medium">
              <span className="w-2 h-2 rounded-full bg-green-500 animate-pulse"></span>
              Live Demo
            </span>
          </div>
        </div>
      </div>
    </section>
  );
};
