
import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Menu, X, Target } from 'lucide-react';

export const LandingHeader = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  return (
    <header className="w-full py-4 px-4 md:px-8 bg-white/80 backdrop-blur-md sticky top-0 z-50 border-b border-gray-100">
      <div className="container mx-auto flex items-center justify-between">
        {/* New Logo and Brand Name */}
        <div className="flex items-center">
          <Link to="/" className="flex items-center space-x-2.5">
            <Target className="h-8 w-8 text-purple-600" />
            <span className="text-2xl font-bold text-gray-800 tracking-tight">VertiQ</span>
          </Link>
        </div>
        
        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center space-x-8">
          <Link to="/about-us" className="text-gray-600 hover:text-purple-600 font-medium transition-colors">About Us</Link>
          <Link to="/features" className="text-gray-600 hover:text-purple-600 font-medium transition-colors">Features</Link>
          <Link to="/contact-us" className="text-gray-600 hover:text-purple-600 font-medium transition-colors">Contact Us</Link>
        </nav>
        
        {/* CTA Button */}
        <div className="hidden md:flex items-center">
          <Link to="/demo-login">
            <Button className="bg-gradient-to-r from-purple-600 to-blue-500 hover:from-purple-700 hover:to-blue-600 text-white">
              Demo
            </Button>
          </Link>
        </div>

        {/* Mobile menu button */}
        <button 
          className="md:hidden p-2 rounded-md text-gray-500 hover:text-purple-600 hover:bg-purple-50"
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
        >
          {mobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
        </button>
      </div>

      {/* Mobile menu */}
      {mobileMenuOpen && (
        <div className="md:hidden absolute top-full left-0 right-0 bg-white border-b border-gray-100 shadow-lg">
          <div className="container mx-auto py-4 px-4">
            <nav className="flex flex-col space-y-4">
              <Link to="/about-us" className="text-gray-600 hover:text-purple-600 font-medium py-2" onClick={() => setMobileMenuOpen(false)}>About Us</Link>
              <Link to="/features" className="text-gray-600 hover:text-purple-600 font-medium py-2" onClick={() => setMobileMenuOpen(false)}>Features</Link>
              <Link to="/contact-us" className="text-gray-600 hover:text-purple-600 font-medium py-2" onClick={() => setMobileMenuOpen(false)}>Contact Us</Link>
              <div className="flex flex-col space-y-2 pt-2 border-t border-gray-100">
                <Link to="/demo-login" onClick={() => setMobileMenuOpen(false)}>
                  <Button className="w-full bg-gradient-to-r from-purple-600 to-blue-500 text-white">Demo</Button>
                </Link>
              </div>
            </nav>
          </div>
        </div>
      )}
    </header>
  );
};
