import React from 'react';
import { TrendingUp, Users, Clock, Shield, Award, Zap } from 'lucide-react';

export const StatsSection = () => {
  const stats = [
    {
      icon: TrendingUp,
      number: "40%",
      label: "Efficiency Boost",
      description: "Average improvement in operations",
      color: "green"
    },
    {
      icon: Shield,
      number: "100%",
      label: "HIPAA Compliant",
      description: "Secure patient data protection",
      color: "indigo"
    },
    {
      icon: Clock,
      number: "99.9%",
      label: "System Uptime",
      description: "Reliable, always-on service",
      color: "blue"
    },
    {
      icon: Users,
      number: "Enterprise",
      label: "Grade Security",
      description: "Bank-level encryption",
      color: "purple"
    }
  ];

  const benefits = [
    {
      icon: Zap,
      title: "Lightning Fast",
      description: "Cloud-based infrastructure ensures rapid response times and seamless performance."
    },
    {
      icon: Shield,
      title: "Enterprise Security",
      description: "Bank-level encryption and HIPAA compliance protect your sensitive healthcare data."
    },
    {
      icon: Award,
      title: "Award Winning",
      description: "Recognized by healthcare industry leaders for innovation and excellence."
    }
  ];

  const getColorClasses = (color: string) => {
    const colorMap = {
      purple: {
        bg: "from-purple-500 to-purple-600",
        icon: "bg-purple-100 text-purple-600",
        text: "text-purple-600"
      },
      blue: {
        bg: "from-blue-500 to-blue-600", 
        icon: "bg-blue-100 text-blue-600",
        text: "text-blue-600"
      },
      green: {
        bg: "from-green-500 to-green-600",
        icon: "bg-green-100 text-green-600", 
        text: "text-green-600"
      },
      indigo: {
        bg: "from-indigo-500 to-indigo-600",
        icon: "bg-indigo-100 text-indigo-600",
        text: "text-indigo-600"
      }
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.purple;
  };

  return (
    <section className="py-16 md:py-24 bg-gradient-to-br from-gray-50 to-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12 md:mb-20">
          <div className="inline-flex items-center px-3 py-1.5 md:px-4 md:py-2 bg-purple-100 text-purple-700 rounded-full text-xs md:text-sm font-medium mb-4 md:mb-6">
            <TrendingUp className="w-3 h-3 md:w-4 md:h-4 mr-1.5 md:mr-2" />
            Proven Results
          </div>
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-4 md:mb-6 text-gray-900 px-2">
            Trusted by Healthcare
            <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent"> Leaders</span>
          </h2>
          <p className="text-base md:text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed px-4">
            Experience the difference with our comprehensive healthcare management platform designed for modern facilities.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8 mb-12 md:mb-20">
          {stats.map((stat, index) => {
            const colors = getColorClasses(stat.color);
            return (
              <div
                key={index}
                className="group relative bg-white rounded-xl lg:rounded-2xl p-4 sm:p-6 lg:p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-gray-200"
              >
                <div className="text-center">
                  <div className={`inline-flex items-center justify-center w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 rounded-xl lg:rounded-2xl ${colors.icon} mb-4 sm:mb-5 lg:mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <stat.icon className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8" />
                  </div>
                  <div className={`text-2xl sm:text-3xl lg:text-4xl font-bold ${colors.text} mb-1 sm:mb-2`}>
                    {stat.number}
                  </div>
                  <h3 className="text-sm sm:text-base lg:text-lg font-semibold text-gray-900 mb-1 sm:mb-2">
                    {stat.label}
                  </h3>
                  <p className="text-gray-600 text-xs sm:text-sm leading-relaxed">
                    {stat.description}
                  </p>
                </div>

                {/* Hover Effect */}
                <div className={`absolute inset-0 bg-gradient-to-br ${colors.bg} opacity-0 group-hover:opacity-5 rounded-xl lg:rounded-2xl transition-opacity duration-300`}></div>
              </div>
            );
          })}
        </div>

        {/* Benefits Section */}
        <div className="bg-gradient-to-br from-purple-50 to-blue-50 rounded-2xl lg:rounded-3xl p-6 sm:p-8 lg:p-12 border border-purple-100">
          <div className="text-center mb-8 lg:mb-12">
            <h3 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 mb-3 lg:mb-4 px-2">
              Why Healthcare Providers Choose Us
            </h3>
            <p className="text-sm sm:text-base lg:text-lg text-gray-600 max-w-2xl mx-auto px-4">
              Experience the difference with our cutting-edge technology and dedicated support.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8">
            {benefits.map((benefit, index) => (
              <div key={index} className="text-center group">
                <div className="inline-flex items-center justify-center w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 bg-white rounded-xl lg:rounded-2xl shadow-lg mb-4 sm:mb-5 lg:mb-6 group-hover:shadow-xl transition-shadow duration-300">
                  <benefit.icon className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-purple-600" />
                </div>
                <h4 className="text-base sm:text-lg lg:text-xl font-semibold text-gray-900 mb-2 lg:mb-3">
                  {benefit.title}
                </h4>
                <p className="text-sm sm:text-base text-gray-600 leading-relaxed px-2">
                  {benefit.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};
