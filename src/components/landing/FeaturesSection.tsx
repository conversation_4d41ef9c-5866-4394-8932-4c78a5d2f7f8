import React from 'react';
import {
  Calendar,
  Users,
  BarChart2,
  FileText,
  Shield,
  Zap,
  Clock,
  Smartphone,
  ArrowRight,
  Sparkles
} from 'lucide-react';

const mainFeatures = [
  {
    icon: Calendar,
    title: "Smart Scheduling",
    description: "AI-powered appointment scheduling that optimizes doctor availability and reduces wait times by up to 40%.",
    color: "purple",
    benefits: ["Automated scheduling", "Conflict resolution", "Wait time optimization"]
  },
  {
    icon: Users,
    title: "Patient Management",
    description: "Comprehensive patient profiles with medical history, treatment plans, and real-time visit tracking.",
    color: "blue",
    benefits: ["Complete patient records", "Treatment tracking", "Family history"]
  },
  {
    icon: BarChart2,
    title: "Analytics Dashboard",
    description: "Real-time insights and analytics to track performance metrics and make data-driven decisions.",
    color: "indigo",
    benefits: ["Real-time reporting", "Performance metrics", "Predictive analytics"]
  }
];

const additionalFeatures = [
  {
    icon: FileText,
    title: "Electronic Records",
    description: "Secure electronic medical records with easy access and sharing capabilities.",
    color: "green"
  },
  {
    icon: Shield,
    title: "HIPAA Compliant",
    description: "Built with security and compliance in mind to protect sensitive patient information.",
    color: "red"
  },
  {
    icon: Zap,
    title: "Fast Performance",
    description: "Lightning-fast performance ensures smooth operation even during peak hours.",
    color: "yellow"
  },
  {
    icon: Clock,
    title: "24/7 Availability",
    description: "Cloud-based system accessible anytime, anywhere with 99.9% uptime guarantee.",
    color: "teal"
  },
  {
    icon: Smartphone,
    title: "Mobile Friendly",
    description: "Responsive design works seamlessly across desktop, tablet, and mobile devices.",
    color: "pink"
  }
];

const getColorClasses = (color: string) => {
  const colorMap = {
    purple: {
      bg: "from-purple-500 to-purple-600",
      icon: "bg-purple-100 text-purple-600",
      border: "border-purple-200",
      text: "text-purple-600"
    },
    blue: {
      bg: "from-blue-500 to-blue-600",
      icon: "bg-blue-100 text-blue-600",
      border: "border-blue-200",
      text: "text-blue-600"
    },
    indigo: {
      bg: "from-indigo-500 to-indigo-600",
      icon: "bg-indigo-100 text-indigo-600",
      border: "border-indigo-200",
      text: "text-indigo-600"
    },
    green: {
      bg: "from-green-500 to-green-600",
      icon: "bg-green-100 text-green-600",
      border: "border-green-200",
      text: "text-green-600"
    },
    red: {
      bg: "from-red-500 to-red-600",
      icon: "bg-red-100 text-red-600",
      border: "border-red-200",
      text: "text-red-600"
    },
    yellow: {
      bg: "from-yellow-500 to-yellow-600",
      icon: "bg-yellow-100 text-yellow-600",
      border: "border-yellow-200",
      text: "text-yellow-600"
    },
    teal: {
      bg: "from-teal-500 to-teal-600",
      icon: "bg-teal-100 text-teal-600",
      border: "border-teal-200",
      text: "text-teal-600"
    },
    pink: {
      bg: "from-pink-500 to-pink-600",
      icon: "bg-pink-100 text-pink-600",
      border: "border-pink-200",
      text: "text-pink-600"
    }
  };
  return colorMap[color as keyof typeof colorMap] || colorMap.purple;
};

export const FeaturesSection = () => {
  return (
    <section id="features" className="py-16 md:py-24 bg-gradient-to-br from-gray-50 to-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12 md:mb-20">
          <div className="inline-flex items-center px-3 py-1.5 md:px-4 md:py-2 bg-purple-100 text-purple-700 rounded-full text-xs md:text-sm font-medium mb-4 md:mb-6">
            <Sparkles className="w-3 h-3 md:w-4 md:h-4 mr-1.5 md:mr-2" />
            Powerful Features
          </div>
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-4 md:mb-6 text-gray-900 px-2">
            Everything You Need for
            <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent"> Modern Healthcare</span>
          </h2>
          <p className="text-base md:text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed px-4">
            Our comprehensive platform combines cutting-edge technology with intuitive design to transform your healthcare operations.
          </p>
        </div>

        {/* Main Features */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 md:gap-8 mb-12 md:mb-20">
          {mainFeatures.map((feature, index) => {
            const colors = getColorClasses(feature.color);
            return (
              <div
                key={index}
                className="group relative bg-white rounded-2xl lg:rounded-3xl p-4 sm:p-6 lg:p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-gray-200"
              >
                <div className={`inline-flex items-center justify-center w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 rounded-xl lg:rounded-2xl ${colors.icon} mb-4 sm:mb-5 lg:mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  <feature.icon className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8" />
                </div>

                <h3 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 mb-3 lg:mb-4">
                  {feature.title}
                </h3>

                <p className="text-sm sm:text-base text-gray-600 leading-relaxed mb-4 lg:mb-6">
                  {feature.description}
                </p>

                <ul className="space-y-1.5 lg:space-y-2">
                  {feature.benefits.map((benefit, i) => (
                    <li key={i} className="flex items-center text-xs sm:text-sm text-gray-600">
                      <div className={`w-1 h-1 sm:w-1.5 sm:h-1.5 rounded-full ${colors.icon.split(' ')[0]} mr-2 sm:mr-3`}></div>
                      {benefit}
                    </li>
                  ))}
                </ul>

                <div className="absolute top-4 right-4 sm:top-5 sm:right-5 lg:top-6 lg:right-6 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <ArrowRight className={`w-4 h-4 sm:w-5 sm:h-5 ${colors.text}`} />
                </div>
              </div>
            );
          })}
        </div>

        {/* Additional Features Grid */}
        <div className="bg-white rounded-2xl lg:rounded-3xl p-6 sm:p-8 lg:p-12 shadow-lg border border-gray-100">
          <div className="text-center mb-8 lg:mb-12">
            <h3 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 mb-3 lg:mb-4 px-2">
              Plus Many More Advanced Features
            </h3>
            <p className="text-sm sm:text-base lg:text-lg text-gray-600 max-w-2xl mx-auto px-4">
              Discover additional capabilities that make our platform the complete solution for healthcare management.
            </p>
          </div>

          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 sm:gap-6">
            {additionalFeatures.map((feature, index) => {
              const colors = getColorClasses(feature.color);
              return (
                <div
                  key={index}
                  className="group text-center p-3 sm:p-4 lg:p-6 rounded-xl lg:rounded-2xl hover:bg-gray-50 transition-colors duration-300"
                >
                  <div className={`inline-flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 rounded-lg lg:rounded-xl ${colors.icon} mb-2 sm:mb-3 lg:mb-4 group-hover:scale-110 transition-transform duration-300`}>
                    <feature.icon className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6" />
                  </div>
                  <h4 className="font-semibold text-gray-900 mb-1 sm:mb-2 text-xs sm:text-sm lg:text-base">
                    {feature.title}
                  </h4>
                  <p className="text-[10px] sm:text-xs lg:text-sm text-gray-600 leading-relaxed">
                    {feature.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
};
