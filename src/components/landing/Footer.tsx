
import React from 'react';
import { Link } from 'react-router-dom';
import { Facebook, Twitter, Linkedin, Instagram, Mail, Phone, ShieldCheck, Target } from 'lucide-react';

export const Footer = () => {
  return (
    <footer className="bg-gray-100 pt-16 pb-8 border-t border-gray-200">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-12 mb-12">
          {/* Company Info - Updated to use icon and text logo */}
          <div>
            <Link to="/" className="flex items-center space-x-2.5 mb-6 text-gray-800 hover:text-purple-600 transition-colors">
              <Target className="h-8 w-8 text-purple-600" />
              <span className="text-2xl font-bold tracking-tight">VertiQ</span>
            </Link>
            <p className="text-gray-700 leading-relaxed mb-6">
              Modern hospital management system designed to streamline healthcare operations and improve patient care.
            </p>
            {/* Social Media Icons - Styled */}
            <div className="flex space-x-4">
              <a href="#" className="w-9 h-9 rounded-full bg-gray-200 flex items-center justify-center text-gray-700 hover:bg-purple-600 hover:text-white transition-colors duration-200">
                <Facebook size={18} />
              </a>
              <a href="#" className="w-9 h-9 rounded-full bg-gray-200 flex items-center justify-center text-gray-700 hover:bg-purple-600 hover:text-white transition-colors duration-200">
                <Twitter size={18} />
              </a>
              <a href="#" className="w-9 h-9 rounded-full bg-gray-200 flex items-center justify-center text-gray-700 hover:bg-purple-600 hover:text-white transition-colors duration-200">
                <Linkedin size={18} />
              </a>
              <a href="#" className="w-9 h-9 rounded-full bg-gray-200 flex items-center justify-center text-gray-700 hover:bg-purple-600 hover:text-white transition-colors duration-200">
                <Instagram size={18} />
              </a>
            </div>
          </div>
          
          {/* Quick Links - Refined typography and spacing */}
          <div>
            <h3 className="font-bold text-lg text-gray-800 mb-5">Quick Links</h3>
            <ul className="space-y-3">
              <li><Link to="/about-us" className="text-gray-700 hover:text-purple-600 transition-colors duration-200">About Us</Link></li>
              <li><Link to="/features" className="text-gray-700 hover:text-purple-600 transition-colors duration-200">Features</Link></li>
              <li><Link to="/blog" className="text-gray-700 hover:text-purple-600 transition-colors duration-200">Blog</Link></li>
              <li><Link to="/contact-us" className="text-gray-700 hover:text-purple-600 transition-colors duration-200">Contact Us</Link></li>
            </ul>
          </div>
          
          {/* Contact Info - Refined typography and spacing */}
          <div>
            <h3 className="font-bold text-lg text-gray-800 mb-5">Contact Info</h3>
            <ul className="space-y-3">
              <li className="flex items-start">
                <Mail className="h-5 w-5 text-purple-600 mr-3 shrink-0 mt-0.5" />
                <button
                  onClick={() => {
                    const email = '<EMAIL>';
                    navigator.clipboard.writeText(email).then(() => {
                      alert(`Email address copied to clipboard: ${email}`);
                    }).catch(() => {
                      alert(`Please manually copy: ${email}`);
                    });
                  }}
                  className="text-gray-700 hover:text-purple-600 transition-colors duration-200 cursor-pointer"
                >
                  <EMAIL>
                </button>
              </li>
              <li className="flex items-start">
                <Phone className="h-5 w-5 text-purple-600 mr-3 shrink-0 mt-0.5" />
                <a href="tel:+254110860589" className="text-gray-700 hover:text-purple-600 transition-colors duration-200">+254 110 860 589</a>
              </li>
            </ul>
          </div>
        </div>
        
        <div className="pt-8 border-t border-gray-200">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-600 text-sm mb-4 md:mb-0">
              &copy; {new Date().getFullYear()} VertiQ. All rights reserved.
            </p>
            {/* Policy links - Refined text color and hover */}
            <div className="flex space-x-4 text-sm">
              <a href="#" className="text-gray-600 hover:text-purple-600 transition-colors duration-200">Privacy Policy</a>
              <a href="#" className="text-gray-600 hover:text-purple-600 transition-colors duration-200">Terms of Service</a>
              <a href="#" className="text-gray-600 hover:text-purple-600 transition-colors duration-200">Cookie Policy</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};
