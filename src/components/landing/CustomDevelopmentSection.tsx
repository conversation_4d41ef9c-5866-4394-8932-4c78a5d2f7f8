import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Code, Smartphone, Zap, Settings, ArrowRight, CheckCircle } from 'lucide-react';

export const CustomDevelopmentSection = () => {
  const services = [
    {
      icon: Code,
      title: "Custom Development",
      description: "Tailored solutions built specifically for your healthcare facility's unique requirements.",
      features: ["Custom integrations", "Specialized workflows", "API development"]
    },
    {
      icon: Smartphone,
      title: "Mobile Applications",
      description: "Native mobile apps for patients and staff to access your system on the go.",
      features: ["iOS & Android apps", "Offline capabilities", "Push notifications"]
    },
    {
      icon: Settings,
      title: "System Integration",
      description: "Seamlessly connect with existing healthcare systems and third-party services.",
      features: ["EHR integration", "Lab systems", "Billing platforms"]
    }
  ];

  return (
    <section id="custom-development" className="py-16 md:py-24 bg-gradient-to-br from-gray-50 to-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12 md:mb-20">
          <div className="inline-flex items-center px-3 py-1.5 md:px-4 md:py-2 bg-indigo-100 text-indigo-700 rounded-full text-xs md:text-sm font-medium mb-4 md:mb-6">
            <Code className="w-3 h-3 md:w-4 md:h-4 mr-1.5 md:mr-2" />
            Custom Solutions
          </div>
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-4 md:mb-6 text-gray-900 px-2">
            Need Something
            <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent"> Unique?</span>
          </h2>
          <p className="text-base md:text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed px-4">
            Beyond our comprehensive VHMS platform, we offer custom development services to create
            tailored solutions that perfectly fit your healthcare facility's specific needs.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 mb-12 md:mb-16">
          {services.map((service, index) => (
            <div
              key={index}
              className="group bg-white rounded-2xl lg:rounded-3xl p-4 sm:p-6 lg:p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-gray-200"
            >
              <div className="inline-flex items-center justify-center w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 bg-gradient-to-br from-purple-100 to-blue-100 rounded-xl lg:rounded-2xl mb-4 sm:mb-5 lg:mb-6 group-hover:scale-110 transition-transform duration-300">
                <service.icon className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-purple-600" />
              </div>

              <h3 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 mb-3 lg:mb-4">
                {service.title}
              </h3>

              <p className="text-sm sm:text-base text-gray-600 leading-relaxed mb-4 lg:mb-6">
                {service.description}
              </p>

              <ul className="space-y-1.5 lg:space-y-2">
                {service.features.map((feature, i) => (
                  <li key={i} className="flex items-center text-xs sm:text-sm text-gray-600">
                    <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-green-600 mr-2 sm:mr-3" />
                    {feature}
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-br from-purple-50 to-blue-50 rounded-2xl lg:rounded-3xl p-6 sm:p-8 lg:p-12 text-center border border-purple-100">
          <div className="max-w-3xl mx-auto">
            <h3 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 mb-4 lg:mb-6 px-2">
              Ready to Build Something Amazing?
            </h3>
            <p className="text-sm sm:text-base lg:text-lg text-gray-600 mb-6 lg:mb-8 leading-relaxed px-4">
              Our experienced development team specializes in healthcare technology.
              Let's discuss how we can create the perfect solution for your facility.
            </p>

            <div className="flex flex-col sm:flex-row justify-center items-center gap-3 sm:gap-4">
              <Link to="/contact-us" className="w-full sm:w-auto">
                <Button size="lg" className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-semibold text-base sm:text-lg shadow-lg hover:shadow-xl transition-all duration-300 w-full">
                  <Code className="mr-1.5 sm:mr-2 h-4 w-4 sm:h-5 sm:w-5" />
                  Discuss Your Project
                  <ArrowRight className="ml-1.5 sm:ml-2 h-4 w-4 sm:h-5 sm:w-5" />
                </Button>
              </Link>

              <div className="text-xs sm:text-sm text-gray-600 text-center">
                Free consultation • No obligation • Expert advice
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};