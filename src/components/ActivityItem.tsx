
import React from 'react';
import { LucideIcon } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface ActivityItemProps {
  time: string;
  title: string;
  status?: string;
  icon?: LucideIcon;
  color: 'blue' | 'green' | 'purple' | 'yellow' | 'red' | 'orange' | 'cyan';
}

const colorClasses = {
  blue: 'border-blue-500',
  green: 'border-green-500',
  purple: 'border-purple-500',
  yellow: 'border-yellow-500',
  red: 'border-red-500',
  orange: 'border-orange-500',
  cyan: 'border-cyan-500',
};

const statusColorMap: Record<string, string> = {
  completed: 'bg-green-100 text-green-800 border-green-200',
  pending: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  cancelled: 'bg-red-100 text-red-800 border-red-200',
  new: 'bg-blue-100 text-blue-800 border-blue-200',
  scheduled: 'bg-purple-100 text-purple-800 border-purple-200',
  processing: 'bg-cyan-100 text-cyan-800 border-cyan-200',
  critical: 'bg-red-100 text-red-800 border-red-200',
  low: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  normal: 'bg-green-100 text-green-800 border-green-200',
};

export const ActivityItem = ({ time, title, status, icon: Icon, color }: ActivityItemProps) => {
  return (
    <div className="flex space-x-3 py-2 group hover:bg-gray-50 rounded-lg px-1 transition-colors duration-150">
      {Icon ? (
        <div className={`p-2 rounded-lg bg-${color}-100 h-fit`}>
          <Icon className={`h-4 w-4 text-${color}-600`} />
        </div>
      ) : (
        <div className={`w-1 ${colorClasses[color]} border-l-4 flex-shrink-0 h-full`}></div>
      )}
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between mb-1">
          <p className="text-xs text-gray-500">{time}</p>
          {status && (
            <Badge variant="outline" className={`${statusColorMap[status.toLowerCase()] || 'bg-gray-100 text-gray-800'} text-xs`}>
              {status}
            </Badge>
          )}
        </div>
        <p className="text-sm font-medium text-gray-900 line-clamp-2">{title}</p>
      </div>
    </div>
  );
};
