import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Bell, 
  BellRing, 
  X, 
  Clock, 
  Users,
  Stethoscope,
  FlaskConical,
  Pill,
  AlertCircle,
  CheckCircle,
  Info
} from 'lucide-react';
import { useWorkflowData } from '@/hooks/useWorkflowData';
import { useSupabaseData } from '@/hooks/useSupabaseData';

interface Notification {
  id: string;
  type: 'info' | 'warning' | 'success' | 'error';
  title: string;
  message: string;
  department: string;
  patientName?: string;
  timestamp: Date;
  read: boolean;
  actionUrl?: string;
}

export const WorkflowNotifications = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);

  const { useWorkflowStats, useDepartmentQueue } = useWorkflowData();
  const { data: stats } = useWorkflowStats();
  const { data: receptionQueue = [] } = useDepartmentQueue('reception');
  const { data: consultationQueue = [] } = useDepartmentQueue('consultation');
  const { data: laboratoryQueue = [] } = useDepartmentQueue('laboratory');
  const { data: pharmacyQueue = [] } = useDepartmentQueue('pharmacy');

  // Generate notifications based on workflow data
  useEffect(() => {
    // Prevent infinite loops by checking if data is actually available
    if (!stats && receptionQueue.length === 0 && consultationQueue.length === 0 &&
        laboratoryQueue.length === 0 && pharmacyQueue.length === 0) {
      return;
    }

    const newNotifications: Notification[] = [];

    // Long waiting times
    const checkWaitingTimes = (queue: any[], department: string, threshold: number) => {
      queue.forEach(workflow => {
        const waitTime = new Date().getTime() - new Date(workflow.updated_at).getTime();
        const waitMinutes = Math.floor(waitTime / (1000 * 60));
        
        if (waitMinutes > threshold) {
          newNotifications.push({
            id: `wait-${workflow.id}`,
            type: 'warning',
            title: `Long Wait Time - ${department}`,
            message: `${workflow.patients?.patient_name} has been waiting for ${waitMinutes} minutes`,
            department,
            patientName: workflow.patients?.patient_name,
            timestamp: new Date(),
            read: false,
            actionUrl: `/${department.toLowerCase()}-department`
          });
        }
      });
    };

    // Check each department for long wait times
    checkWaitingTimes(receptionQueue, 'Reception', 30);
    checkWaitingTimes(consultationQueue, 'Consultation', 45);
    checkWaitingTimes(laboratoryQueue, 'Laboratory', 60);
    checkWaitingTimes(pharmacyQueue, 'Pharmacy', 30);

    // Queue size alerts
    if (receptionQueue.length > 10) {
      newNotifications.push({
        id: 'reception-queue',
        type: 'warning',
        title: 'High Reception Queue',
        message: `${receptionQueue.length} patients waiting in reception`,
        department: 'Reception',
        timestamp: new Date(),
        read: false,
        actionUrl: '/reception'
      });
    }

    if (consultationQueue.length > 8) {
      newNotifications.push({
        id: 'consultation-queue',
        type: 'warning',
        title: 'High Consultation Queue',
        message: `${consultationQueue.length} patients waiting for consultation`,
        department: 'Consultation',
        timestamp: new Date(),
        read: false,
        actionUrl: '/consultation-department'
      });
    }

    if (laboratoryQueue.length > 5) {
      newNotifications.push({
        id: 'laboratory-queue',
        type: 'warning',
        title: 'High Laboratory Queue',
        message: `${laboratoryQueue.length} patients waiting for lab tests`,
        department: 'Laboratory',
        timestamp: new Date(),
        read: false,
        actionUrl: '/laboratory-department'
      });
    }

    if (pharmacyQueue.length > 6) {
      newNotifications.push({
        id: 'pharmacy-queue',
        type: 'warning',
        title: 'High Pharmacy Queue',
        message: `${pharmacyQueue.length} patients waiting for medication`,
        department: 'Pharmacy',
        timestamp: new Date(),
        read: false,
        actionUrl: '/pharmacy-department'
      });
    }

    // Update notifications state
    setNotifications(prev => {
      const existingIds = prev.map(n => n.id);
      const filteredNew = newNotifications.filter(n => !existingIds.includes(n.id));
      return [...prev, ...filteredNew].slice(-20); // Keep only last 20 notifications
    });

  }, [receptionQueue, consultationQueue, laboratoryQueue, pharmacyQueue, stats]);

  // Update unread count
  useEffect(() => {
    setUnreadCount(notifications.filter(n => !n.read).length);
  }, [notifications]);

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success': return CheckCircle;
      case 'warning': return AlertCircle;
      case 'error': return AlertCircle;
      default: return Info;
    }
  };

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'success': return 'text-green-600 bg-green-100';
      case 'warning': return 'text-orange-600 bg-orange-100';
      case 'error': return 'text-red-600 bg-red-100';
      default: return 'text-blue-600 bg-blue-100';
    }
  };

  const getDepartmentIcon = (department: string) => {
    switch (department.toLowerCase()) {
      case 'reception': return Users;
      case 'consultation': return Stethoscope;
      case 'laboratory': return FlaskConical;
      case 'pharmacy': return Pill;
      default: return Bell;
    }
  };

  const markAsRead = (notificationId: string) => {
    setNotifications(prev =>
      prev.map(n => n.id === notificationId ? { ...n, read: true } : n)
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev => prev.map(n => ({ ...n, read: true })));
  };

  const dismissNotification = (notificationId: string) => {
    setNotifications(prev => prev.filter(n => n.id !== notificationId));
  };

  const formatTimeAgo = (timestamp: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - timestamp.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays}d ago`;
  };

  return (
    <div className="relative">
      {/* Notification Bell */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsOpen(!isOpen)}
        className="relative"
      >
        {unreadCount > 0 ? (
          <BellRing className="h-5 w-5" />
        ) : (
          <Bell className="h-5 w-5" />
        )}
        {unreadCount > 0 && (
          <Badge className="absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs bg-red-500">
            {unreadCount > 99 ? '99+' : unreadCount}
          </Badge>
        )}
      </Button>

      {/* Notifications Panel */}
      {isOpen && (
        <div className="absolute right-0 top-full mt-2 w-96 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold text-gray-900">Notifications</h3>
              <div className="flex items-center space-x-2">
                {unreadCount > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={markAllAsRead}
                    className="text-xs"
                  >
                    Mark all read
                  </Button>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsOpen(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          <div className="max-h-96 overflow-y-auto">
            {notifications.length === 0 ? (
              <div className="p-8 text-center text-gray-500">
                <Bell className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                <p>No notifications</p>
              </div>
            ) : (
              <div className="divide-y divide-gray-100">
                {notifications.map((notification) => {
                  const Icon = getNotificationIcon(notification.type);
                  const DeptIcon = getDepartmentIcon(notification.department);
                  
                  return (
                    <div
                      key={notification.id}
                      className={`p-4 hover:bg-gray-50 cursor-pointer ${
                        !notification.read ? 'bg-blue-50' : ''
                      }`}
                      onClick={() => markAsRead(notification.id)}
                    >
                      <div className="flex items-start space-x-3">
                        <div className={`p-1 rounded-full ${getNotificationColor(notification.type)}`}>
                          <Icon className="h-4 w-4" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2 mb-1">
                            <DeptIcon className="h-3 w-3 text-gray-400" />
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {notification.title}
                            </p>
                            {!notification.read && (
                              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                            )}
                          </div>
                          <p className="text-sm text-gray-600 mb-2">
                            {notification.message}
                          </p>
                          <div className="flex items-center justify-between">
                            <span className="text-xs text-gray-400">
                              {formatTimeAgo(notification.timestamp)}
                            </span>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                dismissNotification(notification.id);
                              }}
                              className="h-6 w-6 p-0"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
