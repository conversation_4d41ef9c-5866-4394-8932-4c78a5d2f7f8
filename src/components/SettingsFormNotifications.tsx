import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Bell, Mail, MessageSquare, AlertTriangle, Shield } from 'lucide-react';
import { useSupabaseData } from '@/hooks/useSupabaseData';
import { Settings, SettingsFormData, defaultSettings } from '@/types/settings';

interface SettingsFormNotificationsProps {}

export const SettingsFormNotifications = ({}: SettingsFormNotificationsProps) => {
  const { useSettings, updateSettings } = useSupabaseData();
  const { data: settings, isLoading } = useSettings();
  const updateSettingsMutation = updateSettings();

  const [formData, setFormData] = useState<Partial<SettingsFormData>>({
    ...defaultSettings,
    // Notifications
    email_notifications: true,
    sms_notifications: false,
    appointment_reminders: true,
    reminder_hours_before: 24,
    low_stock_alerts: true,
    low_stock_threshold: 10,
    // Security
    session_timeout_minutes: 480,
    password_expiry_days: 90,
    max_login_attempts: 5,
    two_factor_auth: false,
  });

  // Load settings data when available
  useEffect(() => {
    if (settings) {
      setFormData({
        // Notifications
        email_notifications: settings.email_notifications ?? true,
        sms_notifications: settings.sms_notifications ?? false,
        appointment_reminders: settings.appointment_reminders ?? true,
        reminder_hours_before: settings.reminder_hours_before || 24,
        low_stock_alerts: settings.low_stock_alerts ?? true,
        low_stock_threshold: settings.low_stock_threshold || 10,
        // Security
        session_timeout_minutes: settings.session_timeout_minutes || 480,
        password_expiry_days: settings.password_expiry_days || 90,
        max_login_attempts: settings.max_login_attempts || 5,
        two_factor_auth: settings.two_factor_auth ?? false,
      });
    }
  }, [settings]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type } = e.target;
    const checked = e.target.checked;
    setFormData(prev => ({ 
      ...prev, 
      [name]: type === 'checkbox' ? checked : (type === 'number' ? Number(value) : value)
    }));
  };

  const handleCheckboxChange = (name: string, checked: boolean) => {
    setFormData(prev => ({ ...prev, [name]: checked }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const settingsData = {
      // Notifications
      email_notifications: formData.email_notifications,
      sms_notifications: formData.sms_notifications,
      appointment_reminders: formData.appointment_reminders,
      reminder_hours_before: formData.reminder_hours_before,
      low_stock_alerts: formData.low_stock_alerts,
      low_stock_threshold: formData.low_stock_threshold,
      // Security
      session_timeout_minutes: formData.session_timeout_minutes,
      password_expiry_days: formData.password_expiry_days,
      max_login_attempts: formData.max_login_attempts,
      two_factor_auth: formData.two_factor_auth,
    };

    updateSettingsMutation.mutate(settingsData);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="text-lg text-gray-600">Loading notification settings...</div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto">
      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Email Notifications */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Mail className="mr-2 h-5 w-5" />
              Email Notifications
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="email_notifications"
                checked={formData.email_notifications || false}
                onCheckedChange={(checked) => handleCheckboxChange('email_notifications', checked as boolean)}
              />
              <Label htmlFor="email_notifications" className="font-medium">Enable email notifications</Label>
            </div>
            
            {formData.email_notifications && (
              <div className="space-y-4 ml-6 border-l-2 border-gray-200 pl-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="appointment_reminders"
                    checked={formData.appointment_reminders || false}
                    onCheckedChange={(checked) => handleCheckboxChange('appointment_reminders', checked as boolean)}
                  />
                  <Label htmlFor="appointment_reminders">Send appointment reminders</Label>
                </div>
                
                {formData.appointment_reminders && (
                  <div className="space-y-2 ml-6">
                    <Label htmlFor="reminder_hours_before">Send reminder (hours before appointment)</Label>
                    <Input
                      id="reminder_hours_before"
                      name="reminder_hours_before"
                      type="number"
                      min="1"
                      max="168"
                      value={formData.reminder_hours_before || ''}
                      onChange={handleChange}
                      placeholder="24"
                    />
                    <p className="text-sm text-gray-500">
                      How many hours before the appointment to send reminder emails
                    </p>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* SMS Notifications */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <MessageSquare className="mr-2 h-5 w-5" />
              SMS Notifications
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="sms_notifications"
                checked={formData.sms_notifications || false}
                onCheckedChange={(checked) => handleCheckboxChange('sms_notifications', checked as boolean)}
              />
              <Label htmlFor="sms_notifications" className="font-medium">Enable SMS notifications</Label>
            </div>
            
            {formData.sms_notifications && (
              <div className="ml-6 border-l-2 border-gray-200 pl-4">
                <p className="text-sm text-gray-600">
                  SMS notifications require SMS API configuration in Integration Settings.
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Inventory Alerts */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertTriangle className="mr-2 h-5 w-5" />
              Inventory Alerts
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="low_stock_alerts"
                checked={formData.low_stock_alerts || false}
                onCheckedChange={(checked) => handleCheckboxChange('low_stock_alerts', checked as boolean)}
              />
              <Label htmlFor="low_stock_alerts" className="font-medium">Enable low stock alerts</Label>
            </div>
            
            {formData.low_stock_alerts && (
              <div className="space-y-2 ml-6 border-l-2 border-gray-200 pl-4">
                <Label htmlFor="low_stock_threshold">Low stock threshold</Label>
                <Input
                  id="low_stock_threshold"
                  name="low_stock_threshold"
                  type="number"
                  min="1"
                  max="1000"
                  value={formData.low_stock_threshold || ''}
                  onChange={handleChange}
                  placeholder="10"
                />
                <p className="text-sm text-gray-500">
                  Alert when inventory quantity falls below this number
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Security Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Shield className="mr-2 h-5 w-5" />
              Security Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="session_timeout_minutes">Session Timeout (minutes)</Label>
                <Input
                  id="session_timeout_minutes"
                  name="session_timeout_minutes"
                  type="number"
                  min="15"
                  max="1440"
                  value={formData.session_timeout_minutes || ''}
                  onChange={handleChange}
                  placeholder="480"
                />
                <p className="text-sm text-gray-500">
                  Auto-logout after inactivity (15 min - 24 hours)
                </p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="password_expiry_days">Password Expiry (days)</Label>
                <Input
                  id="password_expiry_days"
                  name="password_expiry_days"
                  type="number"
                  min="30"
                  max="365"
                  value={formData.password_expiry_days || ''}
                  onChange={handleChange}
                  placeholder="90"
                />
                <p className="text-sm text-gray-500">
                  Force password change after this many days
                </p>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="max_login_attempts">Max Login Attempts</Label>
              <Input
                id="max_login_attempts"
                name="max_login_attempts"
                type="number"
                min="3"
                max="10"
                value={formData.max_login_attempts || ''}
                onChange={handleChange}
                placeholder="5"
              />
              <p className="text-sm text-gray-500">
                Lock account after this many failed login attempts
              </p>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="two_factor_auth"
                checked={formData.two_factor_auth || false}
                onCheckedChange={(checked) => handleCheckboxChange('two_factor_auth', checked as boolean)}
              />
              <Label htmlFor="two_factor_auth">Enable two-factor authentication</Label>
            </div>
          </CardContent>
        </Card>

        {/* Save Button */}
        <div className="flex justify-end space-x-4 pt-6 border-t">
          <Button
            type="submit"
            disabled={updateSettingsMutation.isPending}
            className="px-8 py-2"
          >
            {updateSettingsMutation.isPending ? 'Saving...' : 'Save Notification Settings'}
          </Button>
        </div>
      </form>
    </div>
  );
};
