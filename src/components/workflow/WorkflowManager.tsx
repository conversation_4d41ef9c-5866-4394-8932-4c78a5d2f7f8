import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { 
  ArrowRight, 
  CheckCircle, 
  Clock, 
  User,
  Stethoscope,
  FlaskConical,
  Pill,
  Users
} from 'lucide-react';
import { useWorkflowData } from '@/hooks/useWorkflowData';
import { ConsultationForm } from './ConsultationForm';
import { LaboratoryForm } from './LaboratoryForm';
import { PharmacyForm } from './PharmacyForm';

interface WorkflowManagerProps {
  workflow: any;
  onWorkflowUpdate?: () => void;
}

export const WorkflowManager = ({ workflow, onWorkflowUpdate }: WorkflowManagerProps) => {
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [currentAction, setCurrentAction] = useState<string>('');
  
  const { advanceWorkflow, updateWorkflowAssignment } = useWorkflowData();

  const getDepartmentIcon = (department: string) => {
    switch (department) {
      case 'reception': return Users;
      case 'consultation': return Stethoscope;
      case 'laboratory': return FlaskConical;
      case 'pharmacy': return Pill;
      case 'completed': return CheckCircle;
      default: return Clock;
    }
  };

  const getDepartmentColor = (department: string) => {
    switch (department) {
      case 'reception': return 'bg-blue-100 text-blue-800';
      case 'consultation': return 'bg-green-100 text-green-800';
      case 'laboratory': return 'bg-orange-100 text-orange-800';
      case 'pharmacy': return 'bg-purple-100 text-purple-800';
      case 'completed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getNextDepartment = (currentDepartment: string) => {
    const sequence = ['reception', 'consultation', 'laboratory', 'completed'];
    const currentIndex = sequence.indexOf(currentDepartment);
    return currentIndex < sequence.length - 1 ? sequence[currentIndex + 1] : null;
  };

  const handleStartProcess = (action: string) => {
    setCurrentAction(action);
    setIsFormOpen(true);
  };

  const handleProcessComplete = async (data?: any) => {
    setIsFormOpen(false);
    setCurrentAction('');
    if (onWorkflowUpdate) {
      onWorkflowUpdate();
    }
  };

  const handleManualAdvance = async () => {
    try {
      await advanceWorkflow.mutateAsync({
        workflowId: workflow.id,
        notes: `Manually advanced from ${workflow.current_department}`
      });
      if (onWorkflowUpdate) {
        onWorkflowUpdate();
      }
    } catch (error) {
      console.error('Error advancing workflow:', error);
    }
  };

  const canProcessInCurrentDepartment = () => {
    return ['consultation', 'laboratory'].includes(workflow.current_department);
  };

  const getProcessButtonText = () => {
    switch (workflow.current_department) {
      case 'consultation': return 'Start Consultation';
      case 'laboratory': return 'Complete Lab Test & Finish Workflow';
      default: return 'Process';
    }
  };

  const renderCurrentForm = () => {
    if (!workflow || !isFormOpen) return null;

    const commonProps = {
      patientId: workflow.patient_id,
      workflowId: workflow.id,
      onSuccess: handleProcessComplete,
      autoAdvance: true
    };

    switch (workflow.current_department) {
      case 'consultation':
        return <ConsultationForm {...commonProps} />;
      case 'laboratory':
        return <LaboratoryForm {...commonProps} />;
      default:
        return null;
    }
  };

  const Icon = getDepartmentIcon(workflow.current_department);
  const nextDepartment = getNextDepartment(workflow.current_department);

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Icon className="h-5 w-5" />
            <span>Workflow Management</span>
          </div>
          <Badge className={getDepartmentColor(workflow.current_department)}>
            {workflow.current_department}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Patient Info */}
        <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
          <div className="p-2 bg-blue-100 rounded">
            <User className="h-4 w-4 text-blue-600" />
          </div>
          <div>
            <h3 className="font-medium">{workflow.patients?.patient_name}</h3>
            <p className="text-sm text-gray-600">
              Current Stage: {workflow.current_department}
            </p>
          </div>
        </div>

        {/* Workflow Progress */}
        <div className="space-y-2">
          <h4 className="font-medium text-sm">Workflow Progress</h4>
          <div className="flex items-center space-x-2">
            {['reception', 'consultation', 'laboratory', 'completed'].map((stage, index) => {
              const StageIcon = getDepartmentIcon(stage);
              const isActive = workflow.current_department === stage;
              const isCompleted = ['reception', 'consultation', 'laboratory', 'completed']
                .indexOf(workflow.current_department) > index;
              
              return (
                <React.Fragment key={stage}>
                  <div className={`flex flex-col items-center ${isActive ? 'scale-110' : ''}`}>
                    <div className={`p-2 rounded-full ${
                      isCompleted ? 'bg-green-500 text-white' :
                      isActive ? getDepartmentColor(stage).replace('100', '500').replace('800', 'white') :
                      'bg-gray-200 text-gray-400'
                    }`}>
                      <StageIcon className="h-3 w-3" />
                    </div>
                    <span className="text-xs mt-1 capitalize">{stage}</span>
                  </div>
                  {index < 4 && (
                    <ArrowRight className={`h-3 w-3 ${
                      isCompleted ? 'text-green-500' : 'text-gray-300'
                    }`} />
                  )}
                </React.Fragment>
              );
            })}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-2">
          {canProcessInCurrentDepartment() && (
            <Button 
              onClick={() => handleStartProcess(workflow.current_department)}
              className="flex-1"
            >
              <Icon className="h-4 w-4 mr-2" />
              {getProcessButtonText()}
            </Button>
          )}
          
          {nextDepartment && workflow.current_department !== 'completed' && (
            <Button 
              variant="outline" 
              onClick={handleManualAdvance}
              disabled={advanceWorkflow.isPending}
            >
              <ArrowRight className="h-4 w-4 mr-2" />
              {advanceWorkflow.isPending ? 'Advancing...' : `Skip to ${nextDepartment}`}
            </Button>
          )}
        </div>

        {/* Workflow Info */}
        <div className="text-xs text-gray-500 space-y-1">
          <p>Created: {new Date(workflow.created_at).toLocaleString()}</p>
          <p>Last Updated: {new Date(workflow.updated_at).toLocaleString()}</p>
          {workflow.assigned_to && (
            <p>Assigned to: {workflow.assigned_to}</p>
          )}
          {workflow.notes && (
            <p>Notes: {workflow.notes}</p>
          )}
        </div>
      </CardContent>

      {/* Form Dialog */}
      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <div className="space-y-4">
            <div className="border-b pb-4">
              <h2 className="text-lg font-semibold">
                {getProcessButtonText()} for {workflow.patients?.patient_name}
              </h2>
              <p className="text-sm text-gray-600">
                Current Department: {workflow.current_department}
              </p>
            </div>
            {renderCurrentForm()}
          </div>
        </DialogContent>
      </Dialog>
    </Card>
  );
};
