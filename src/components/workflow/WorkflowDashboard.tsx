
import React, { useState, useMemo } from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { useNavigate } from 'react-router-dom';
import {
  Users,
  Stethoscope,
  FlaskConical,
  Pill,
  CheckCircle,
  Clock,
  ArrowRight,
  User,
  Search,
  Activity,
  TrendingUp,
  AlertCircle,
  Calendar,
  BarChart3,
  Filter,
  FileText,
  Eye,
  RefreshCw,
  Timer,
  UserCheck,
  Building2
} from 'lucide-react';
import { useWorkflowData } from '@/hooks/useWorkflowData';
import { useSupabaseData } from '@/hooks/useSupabaseData';

export const WorkflowDashboard = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('overview');

  // Get real data from workflow hooks
  const {
    useConsultationRecords,
    useLaboratoryRecords,
    usePharmacyRecords
  } = useWorkflowData();

  const { usePatients } = useSupabaseData();

  const { data: allPatients = [], isLoading: patientsLoading } = usePatients();
  const { data: consultationRecords = [], isLoading: consultationLoading } = useConsultationRecords();
  const { data: laboratoryRecords = [], isLoading: laboratoryLoading } = useLaboratoryRecords();
  const { data: pharmacyRecords = [], isLoading: pharmacyLoading } = usePharmacyRecords();

  // Calculate real workflow statistics
  const workflowStats = useMemo(() => {
    const totalPatients = allPatients.length;
    const patientsWithConsultation = consultationRecords.length;
    const patientsWithLaboratory = laboratoryRecords.length;
    const patientsWithPharmacy = pharmacyRecords.length;

    // Patients at each stage
    const atReception = allPatients.filter(patient => {
      const hasConsultation = consultationRecords.some(record => record.patient_id === patient.id);
      return !hasConsultation;
    }).length;

    const atConsultation = allPatients.filter(patient => {
      const hasConsultation = consultationRecords.some(record => record.patient_id === patient.id);
      const hasLaboratory = laboratoryRecords.some(record => record.patient_id === patient.id);
      return hasConsultation && !hasLaboratory;
    }).length;

    const atLaboratory = allPatients.filter(patient => {
      const hasConsultation = consultationRecords.some(record => record.patient_id === patient.id);
      const hasLaboratory = laboratoryRecords.some(record => record.patient_id === patient.id);
      const hasPharmacy = pharmacyRecords.some(record => record.patient_id === patient.id);
      return hasConsultation && hasLaboratory && !hasPharmacy;
    }).length;

    const completed = allPatients.filter(patient => {
      const hasConsultation = consultationRecords.some(record => record.patient_id === patient.id);
      const hasLaboratory = laboratoryRecords.some(record => record.patient_id === patient.id);
      return hasConsultation && hasLaboratory; // Workflow ends at laboratory
    }).length;

    return {
      totalPatients,
      atReception,
      atConsultation,
      atLaboratory,
      completed,
      patientsWithConsultation,
      patientsWithLaboratory,
      patientsWithPharmacy
    };
  }, [allPatients, consultationRecords, laboratoryRecords, pharmacyRecords]);

  // Get patients by workflow stage for detailed views
  const patientsByStage = useMemo(() => {
    const reception = allPatients.filter(patient => {
      const hasConsultation = consultationRecords.some(record => record.patient_id === patient.id);
      return !hasConsultation;
    });

    const consultation = allPatients.filter(patient => {
      const hasConsultation = consultationRecords.some(record => record.patient_id === patient.id);
      const hasLaboratory = laboratoryRecords.some(record => record.patient_id === patient.id);
      return hasConsultation && !hasLaboratory;
    });

    const laboratory = allPatients.filter(patient => {
      const hasConsultation = consultationRecords.some(record => record.patient_id === patient.id);
      const hasLaboratory = laboratoryRecords.some(record => record.patient_id === patient.id);
      const hasPharmacy = pharmacyRecords.some(record => record.patient_id === patient.id);
      return hasConsultation && hasLaboratory && !hasPharmacy;
    });

    const completed = allPatients.filter(patient => {
      const hasConsultation = consultationRecords.some(record => record.patient_id === patient.id);
      const hasLaboratory = laboratoryRecords.some(record => record.patient_id === patient.id);
      return hasConsultation && hasLaboratory;
    });

    return { reception, consultation, laboratory, completed };
  }, [allPatients, consultationRecords, laboratoryRecords, pharmacyRecords]);

  const isLoading = patientsLoading || consultationLoading || laboratoryLoading || pharmacyLoading;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex flex-col items-center space-y-4">
          <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />
          <div className="text-lg text-gray-600">Loading workflow dashboard...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            <Activity className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Workflow Dashboard</h1>
            <p className="text-gray-600 text-sm sm:text-base">Real-time patient flow monitoring and analytics</p>
          </div>
        </div>
        <div className="flex items-center space-x-4">
          <div className="text-sm text-gray-500">
            Total Patients: {workflowStats.totalPatients}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Overview Statistics Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
        <Card className="bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200">
          <CardContent className="p-4 sm:p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-600 text-sm font-medium">At Reception</p>
                <p className="text-2xl sm:text-3xl font-bold text-blue-900">{workflowStats.atReception}</p>
                <p className="text-xs text-blue-700 mt-1">Awaiting consultation</p>
              </div>
              <div className="p-3 bg-blue-200 rounded-full">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-green-50 to-green-100 border-green-200">
          <CardContent className="p-4 sm:p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-600 text-sm font-medium">At Consultation</p>
                <p className="text-2xl sm:text-3xl font-bold text-green-900">{workflowStats.atConsultation}</p>
                <p className="text-xs text-green-700 mt-1">Awaiting laboratory</p>
              </div>
              <div className="p-3 bg-green-200 rounded-full">
                <Stethoscope className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-orange-50 to-orange-100 border-orange-200">
          <CardContent className="p-4 sm:p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-600 text-sm font-medium">At Laboratory</p>
                <p className="text-2xl sm:text-3xl font-bold text-orange-900">{workflowStats.atLaboratory}</p>
                <p className="text-xs text-orange-700 mt-1">Pending pharmacy</p>
              </div>
              <div className="p-3 bg-orange-200 rounded-full">
                <FlaskConical className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-purple-50 to-purple-100 border-purple-200">
          <CardContent className="p-4 sm:p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-600 text-sm font-medium">Completed</p>
                <p className="text-2xl sm:text-3xl font-bold text-purple-900">{workflowStats.completed}</p>
                <p className="text-xs text-purple-700 mt-1">Full workflow done</p>
              </div>
              <div className="p-3 bg-purple-200 rounded-full">
                <CheckCircle className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Workflow Flow Visualization */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <ArrowRight className="h-5 w-5 mr-2 text-blue-600" />
            Patient Flow Pipeline
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="p-3 sm:p-4 bg-gray-50 rounded-lg">
            {/* Mobile: Vertical Layout */}
            <div className="flex flex-col sm:hidden space-y-3">
              <div className="flex items-center space-x-2">
                <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                  <Users className="h-3 w-3 text-white" />
                </div>
                <div className="text-xs">
                  <div className="font-medium">Reception</div>
                  <div className="text-gray-500">{workflowStats.atReception} patients</div>
                </div>
              </div>
              <div className="flex justify-center">
                <ArrowRight className="h-3 w-3 text-gray-400 rotate-90" />
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                  <Stethoscope className="h-3 w-3 text-white" />
                </div>
                <div className="text-xs">
                  <div className="font-medium">Consultation</div>
                  <div className="text-gray-500">{workflowStats.atConsultation} patients</div>
                </div>
              </div>
              <div className="flex justify-center">
                <ArrowRight className="h-3 w-3 text-gray-400 rotate-90" />
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center">
                  <FlaskConical className="h-3 w-3 text-white" />
                </div>
                <div className="text-xs">
                  <div className="font-medium">Laboratory</div>
                  <div className="text-gray-500">{workflowStats.atLaboratory} patients</div>
                </div>
              </div>
              <div className="flex justify-center">
                <ArrowRight className="h-3 w-3 text-gray-400 rotate-90" />
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center">
                  <CheckCircle className="h-3 w-3 text-white" />
                </div>
                <div className="text-xs">
                  <div className="font-medium">Completed</div>
                  <div className="text-gray-500">{workflowStats.completed} patients</div>
                </div>
              </div>
            </div>

            {/* Desktop: Horizontal Layout */}
            <div className="hidden sm:flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                    <Users className="h-4 w-4 text-white" />
                  </div>
                  <div className="text-sm">
                    <div className="font-medium">Reception</div>
                    <div className="text-gray-500">{workflowStats.atReception} patients</div>
                  </div>
                </div>
                <ArrowRight className="h-4 w-4 text-gray-400" />
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                    <Stethoscope className="h-4 w-4 text-white" />
                  </div>
                  <div className="text-sm">
                    <div className="font-medium">Consultation</div>
                    <div className="text-gray-500">{workflowStats.atConsultation} patients</div>
                  </div>
                </div>
                <ArrowRight className="h-4 w-4 text-gray-400" />
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                    <FlaskConical className="h-4 w-4 text-white" />
                  </div>
                  <div className="text-sm">
                    <div className="font-medium">Laboratory</div>
                    <div className="text-gray-500">{workflowStats.atLaboratory} patients</div>
                  </div>
                </div>
                <ArrowRight className="h-4 w-4 text-gray-400" />
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                    <CheckCircle className="h-4 w-4 text-white" />
                  </div>
                  <div className="text-sm">
                    <div className="font-medium">Completed</div>
                    <div className="text-gray-500">{workflowStats.completed} patients</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Patient Lists by Department */}
      <Tabs defaultValue="reception" className="w-full">
        <TabsList className="grid w-full grid-cols-2 sm:grid-cols-4 gap-1">
          <TabsTrigger value="reception" className="flex items-center space-x-1 sm:space-x-2 text-xs sm:text-sm">
            <Users className="h-3 w-3 sm:h-4 sm:w-4" />
            <span className="hidden sm:inline">Reception ({workflowStats.atReception})</span>
            <span className="sm:hidden">Rec ({workflowStats.atReception})</span>
          </TabsTrigger>
          <TabsTrigger value="consultation" className="flex items-center space-x-1 sm:space-x-2 text-xs sm:text-sm">
            <Stethoscope className="h-3 w-3 sm:h-4 sm:w-4" />
            <span className="hidden sm:inline">Consultation ({workflowStats.atConsultation})</span>
            <span className="sm:hidden">Con ({workflowStats.atConsultation})</span>
          </TabsTrigger>
          <TabsTrigger value="laboratory" className="flex items-center space-x-1 sm:space-x-2 text-xs sm:text-sm">
            <FlaskConical className="h-3 w-3 sm:h-4 sm:w-4" />
            <span className="hidden sm:inline">Laboratory ({workflowStats.atLaboratory})</span>
            <span className="sm:hidden">Lab ({workflowStats.atLaboratory})</span>
          </TabsTrigger>
          <TabsTrigger value="completed" className="flex items-center space-x-1 sm:space-x-2 text-xs sm:text-sm">
            <CheckCircle className="h-3 w-3 sm:h-4 sm:w-4" />
            <span className="hidden sm:inline">Completed ({workflowStats.completed})</span>
            <span className="sm:hidden">Done ({workflowStats.completed})</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="reception" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Patients at Reception</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigate('/reception')}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  Go to Reception
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {patientsByStage.reception.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Users className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>No patients at reception</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {patientsByStage.reception.slice(0, 5).map((patient) => (
                    <div key={patient.id} className="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                          <User className="h-4 w-4 text-white" />
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">{patient.patient_name}</p>
                          <p className="text-sm text-gray-500">{patient.phone_number}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-gray-500">Registered</p>
                        <p className="text-xs text-gray-400">
                          {new Date(patient.registration_date).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  ))}
                  {patientsByStage.reception.length > 5 && (
                    <p className="text-center text-sm text-gray-500 pt-2">
                      And {patientsByStage.reception.length - 5} more patients...
                    </p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="consultation" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Patients at Consultation</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigate('/consultation-department')}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  Go to Consultation
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {patientsByStage.consultation.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Stethoscope className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>No patients at consultation</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {patientsByStage.consultation.slice(0, 5).map((patient) => (
                    <div key={patient.id} className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                          <User className="h-4 w-4 text-white" />
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">{patient.patient_name}</p>
                          <p className="text-sm text-gray-500">{patient.phone_number}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-gray-500">Consultation Done</p>
                        <p className="text-xs text-gray-400">Awaiting Lab</p>
                      </div>
                    </div>
                  ))}
                  {patientsByStage.consultation.length > 5 && (
                    <p className="text-center text-sm text-gray-500 pt-2">
                      And {patientsByStage.consultation.length - 5} more patients...
                    </p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="laboratory" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Patients at Laboratory</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigate('/laboratory-department')}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  Go to Laboratory
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {patientsByStage.laboratory.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <FlaskConical className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>No patients at laboratory</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {patientsByStage.laboratory.slice(0, 5).map((patient) => (
                    <div key={patient.id} className="flex items-center justify-between p-3 bg-orange-50 rounded-lg border border-orange-200">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                          <User className="h-4 w-4 text-white" />
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">{patient.patient_name}</p>
                          <p className="text-sm text-gray-500">{patient.phone_number}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-gray-500">Lab Tests Done</p>
                        <p className="text-xs text-gray-400">Pending Pharmacy</p>
                      </div>
                    </div>
                  ))}
                  {patientsByStage.laboratory.length > 5 && (
                    <p className="text-center text-sm text-gray-500 pt-2">
                      And {patientsByStage.laboratory.length - 5} more patients...
                    </p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="completed" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Completed Patients</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigate('/medical-records')}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  View Medical Records
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {patientsByStage.completed.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <CheckCircle className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>No completed patients</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {patientsByStage.completed.slice(0, 5).map((patient) => (
                    <div key={patient.id} className="flex items-center justify-between p-3 bg-purple-50 rounded-lg border border-purple-200">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                          <User className="h-4 w-4 text-white" />
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">{patient.patient_name}</p>
                          <p className="text-sm text-gray-500">{patient.phone_number}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-gray-500">Workflow Complete</p>
                        <p className="text-xs text-gray-400">Ready for Records</p>
                      </div>
                    </div>
                  ))}
                  {patientsByStage.completed.length > 5 && (
                    <p className="text-center text-sm text-gray-500 pt-2">
                      And {patientsByStage.completed.length - 5} more patients...
                    </p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Building2 className="h-5 w-5 mr-2 text-blue-600" />
            Quick Actions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <Button
              variant="outline"
              className="h-20 flex flex-col items-center justify-center space-y-2"
              onClick={() => navigate('/reception')}
            >
              <Users className="h-6 w-6 text-blue-600" />
              <span className="text-sm">Add Patient</span>
            </Button>
            <Button
              variant="outline"
              className="h-20 flex flex-col items-center justify-center space-y-2"
              onClick={() => navigate('/consultation-department')}
            >
              <Stethoscope className="h-6 w-6 text-green-600" />
              <span className="text-sm">Consultation</span>
            </Button>
            <Button
              variant="outline"
              className="h-20 flex flex-col items-center justify-center space-y-2"
              onClick={() => navigate('/laboratory-department')}
            >
              <FlaskConical className="h-6 w-6 text-orange-600" />
              <span className="text-sm">Laboratory</span>
            </Button>
            <Button
              variant="outline"
              className="h-20 flex flex-col items-center justify-center space-y-2"
              onClick={() => navigate('/medical-records')}
            >
              <FileText className="h-6 w-6 text-purple-600" />
              <span className="text-sm">Medical Records</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
