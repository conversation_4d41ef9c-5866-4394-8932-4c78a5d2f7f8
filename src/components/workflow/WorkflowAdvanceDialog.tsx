
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, <PERSON>alogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useWorkflowData } from '@/hooks/useWorkflowData';

interface WorkflowAdvanceDialogProps {
  workflow: any;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export const WorkflowAdvanceDialog = ({ workflow, isOpen, onClose, onSuccess }: WorkflowAdvanceDialogProps) => {
  const [assignedTo, setAssignedTo] = useState('');
  const [notes, setNotes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const { advanceWorkflow } = useWorkflowData();

  const getNextDepartment = (current: string) => {
    switch (current) {
      case 'reception': return 'Consultation';
      case 'consultation': return 'Laboratory';
      case 'laboratory': return 'Follow-up Consultation';
      case 'follow_up_consultation': return 'Medical Records (Completed)';
      case 'pharmacy': return 'Completed';
      default: return 'Unknown';
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      await advanceWorkflow.mutateAsync({
        workflowId: workflow.id,
        assignedTo: assignedTo || undefined,
        notes: notes || undefined
      });
      onSuccess();
    } catch (error) {
      console.error('Error advancing workflow:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setAssignedTo('');
    setNotes('');
    onClose();
  };

  if (!workflow) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Advance Patient Workflow</DialogTitle>
          <DialogDescription>
            Move the patient to the next department in their workflow process
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <p className="text-sm text-gray-600">
              Moving <strong>{workflow.patients?.patient_name}</strong> from{' '}
              <span className="capitalize font-medium">{workflow.current_department}</span> to{' '}
              <span className="capitalize font-medium">{getNextDepartment(workflow.current_department)}</span>
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="assignedTo">Assign To (Optional)</Label>
            <Input
              id="assignedTo"
              type="text"
              placeholder="Staff member name"
              value={assignedTo}
              onChange={(e) => setAssignedTo(e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Notes (Optional)</Label>
            <Textarea
              id="notes"
              placeholder="Add any notes about this transition..."
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={3}
            />
          </div>

          <div className="flex justify-end space-x-3">
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Advancing...' : 'Advance Workflow'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
