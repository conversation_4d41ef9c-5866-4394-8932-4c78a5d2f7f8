
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { useWorkflowData } from '@/hooks/useWorkflowData';
import { useSupabaseData } from '@/hooks/useSupabaseData';
import { useValidation } from '@/hooks/useValidation';
import { useNavigate } from 'react-router-dom';
import { toast } from '@/hooks/use-toast';
import { useCurrency } from '@/contexts/CurrencyContext';
import { Eye, User, Phone, Mail, Heart, Calendar, Plus, X, Pill } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface ConsultationFormProps {
  patientId: string;
  workflowId: string;
  onSuccess: (data?: any) => void;
  autoAdvance?: boolean;
  existingData?: any; // Add existing consultation data
  patientData?: any; // Add patient data from reception
  consultationType?: 'initial' | 'follow_up'; // Add consultation type
}

export const ConsultationForm = ({
  patientId,
  workflowId,
  onSuccess,
  autoAdvance = true,
  existingData,
  patientData,
  consultationType = 'initial'
}: ConsultationFormProps) => {
  const navigate = useNavigate();
  const { formatCurrency } = useCurrency();
  const [showPatientDetails, setShowPatientDetails] = useState(false);
  const [formData, setFormData] = useState(() => {
    const initialLabTestsRequired = existingData?.lab_tests_required ? 'yes' : (consultationType === 'initial' ? 'yes' : 'no');

    console.log('🔍 FORM INITIALIZATION:', {
      consultationType,
      existingData: existingData ? 'exists' : 'none',
      existingLabTestsRequired: existingData?.lab_tests_required,
      initialLabTestsRequired
    });

    return {
      doctor_name: existingData?.doctor_name || '',
      diagnosis: existingData?.diagnosis || '',
      treatment_plan: existingData?.treatment_plan || '',
      symptoms: existingData?.symptoms || '',
      notes: existingData?.notes || '',
      labTestsRequired: initialLabTestsRequired,
      department_id: existingData?.department_id || patientData?.department_id || '',
      start_time: existingData?.start_time || new Date().toTimeString().slice(0, 5), // HH:MM format
      end_time: existingData?.end_time || '',
      fee_amount: existingData?.fee_amount || '',
      fee_paid: existingData?.fee_paid || false,
      fee_notes: existingData?.fee_notes || '',
    vital_signs: {
      blood_pressure: existingData?.vital_signs?.blood_pressure || '',
      temperature: existingData?.vital_signs?.temperature || '',
      pulse: existingData?.vital_signs?.pulse || '',
      respiratory_rate: existingData?.vital_signs?.respiratory_rate || '',
      weight: existingData?.vital_signs?.weight || '',
      height: existingData?.vital_signs?.height || ''
    }
    };
  });

  // Medicine prescription state - parse from notes if available
  const [prescribedMedicines, setPrescribedMedicines] = useState(() => {
    // Try to parse prescribed medicines from notes if they exist
    if (existingData?.prescribed_medicines) {
      return existingData.prescribed_medicines;
    }

    // If no prescribed_medicines field, try to parse from notes
    if (existingData?.notes && existingData.notes.includes('--- PRESCRIBED MEDICINES ---')) {
      // This is a fallback for when medicines are stored in notes
      // For now, just return empty array and let user re-enter if needed
      return [{ name: '', dosage: '', frequency: '', duration: '', instructions: '' }];
    }

    return [{ name: '', dosage: '', frequency: '', duration: '', instructions: '' }];
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { createConsultationRecord, completeStageAndAdvance } = useWorkflowData();
  const { usePatients, useMedicalDepartments } = useSupabaseData();
  const { data: departments = [] } = useMedicalDepartments();
  const { validateConsultation, validationErrors, clearValidation } = useValidation();

  const isEditMode = !!existingData; // Check if we're editing existing data
  const { data: allPatients = [] } = usePatients();

  // Get patient details from reception
  const currentPatient = patientData || allPatients.find(p => p.id === patientId);

  const getPatientAge = (dateOfBirth: string) => {
    if (!dateOfBirth) return 'Unknown';
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  const handleVitalSignsChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      vital_signs: {
        ...prev.vital_signs,
        [field]: value
      }
    }));
  };

  // Medicine prescription functions
  const addMedicine = () => {
    setPrescribedMedicines([
      ...prescribedMedicines,
      { name: '', dosage: '', frequency: '', duration: '', instructions: '' }
    ]);
  };

  const removeMedicine = (index: number) => {
    setPrescribedMedicines(prescribedMedicines.filter((_, i) => i !== index));
  };

  const updateMedicine = (index: number, field: string, value: string) => {
    setPrescribedMedicines(prescribedMedicines.map((med, i) =>
      i === index ? { ...med, [field]: value } : med
    ));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    clearValidation();

    // Validate consultation data
    const isValid = validateConsultation({
      doctor_name: formData.doctor_name,
      consultation_date: new Date().toISOString().split('T')[0], // Use current date
      symptoms: formData.symptoms,
      diagnosis: formData.diagnosis,
      treatment_plan: formData.treatment_plan,
      notes: formData.notes
    });

    if (!isValid) {
      return;
    }

    // Department is now optional for all consultations
    // No additional validation needed for department_id

    // Validate start time
    if (!formData.start_time.trim()) {
      toast({
        title: "Validation Error",
        description: "Consultation start time is required.",
        variant: "destructive"
      });
      setIsSubmitting(false);
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare prescribed medicines as text for notes field
      const prescribedMedsText = prescribedMedicines
        .filter(med => med.name.trim() !== '')
        .map((med, index) =>
          `${index + 1}. ${med.name} - ${med.dosage}\n   Frequency: ${med.frequency}, Duration: ${med.duration}${med.instructions ? `\n   Instructions: ${med.instructions}` : ''}`
        ).join('\n');

      const notesWithPrescriptions = prescribedMedsText
        ? `${formData.notes}\n\n--- PRESCRIBED MEDICINES ---\n${prescribedMedsText}`
        : formData.notes;

      // Always use direct consultation record creation (simplified approach)
      const result = await createConsultationRecord.mutateAsync({
        patient_id: patientId,
        doctor_name: formData.doctor_name,
        consultation_date: new Date().toISOString().split('T')[0],
        symptoms: formData.symptoms,
        diagnosis: formData.diagnosis,
        treatment_plan: formData.treatment_plan,
        notes: notesWithPrescriptions,
        vital_signs: formData.vital_signs,
        consultation_type: consultationType,
        lab_tests_required: formData.labTestsRequired === 'yes',
        department_id: formData.department_id || null,
        start_time: formData.start_time,
        end_time: formData.end_time || new Date().toTimeString().slice(0, 5), // Auto-fill end time if empty
        fee_amount: parseFloat(formData.fee_amount) || 0,
        fee_paid: formData.fee_paid,
        fee_notes: formData.fee_notes
      });

      // Show success message
      const isFollowUp = consultationType === 'follow_up';
      const needsLabTests = formData.labTestsRequired === 'yes';

      // DEBUG: Let's see what's happening
      console.log('🔍 CONSULTATION FORM DEBUG:', {
        consultationType,
        isFollowUp,
        formDataLabTestsRequired: formData.labTestsRequired,
        needsLabTests,
        autoAdvance,
        existingData: existingData ? 'exists' : 'none',
        existingLabTestsRequired: existingData?.lab_tests_required
      });





      toast({
        title: isFollowUp ? "Follow-up Consultation Completed" : "Consultation Completed",
        description: isFollowUp
          ? "Follow-up consultation completed. Patient workflow finished."
          : needsLabTests
            ? "Consultation completed. Patient will be sent to laboratory."
            : "Consultation completed. Patient workflow finished.",
      });

      onSuccess(result);

      // Auto-advance based on consultation type and lab test requirements
      if (autoAdvance) {
        setTimeout(() => {
          if (isFollowUp) {
            // Follow-up consultation completes the workflow
            console.log('🔄 NAVIGATING: Follow-up complete → Medical Records');
            navigate('/medical-records');
          } else {
            // Initial consultation - check if lab tests are needed
            if (needsLabTests) {
              console.log('🔄 NAVIGATING: Initial consultation with lab tests → Laboratory Department');
              navigate('/laboratory-department');
            } else {
              console.log('🔄 NAVIGATING: Initial consultation without lab tests → Medical Records');
              navigate('/medical-records'); // Skip lab, workflow completed
            }
          }
        }, 1500); // Small delay to show success message
      }
    } catch (error) {
      console.error('Error creating consultation record:', error);
      toast({
        title: "Error",
        description: "Failed to save consultation record. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Mode Indicator */}
      {isEditMode && (
        <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-sm text-blue-800">
            <strong>Editing existing consultation record</strong> - Form is pre-filled with current data
          </p>
        </div>
      )}

      {/* Follow-up Consultation Indicator */}
      {consultationType === 'follow_up' && (
        <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
          <p className="text-sm text-green-800">
            <strong>Follow-up Consultation</strong> - This is a post-laboratory follow-up consultation
          </p>
        </div>
      )}

      {validationErrors.length > 0 && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <h4 className="font-medium text-red-800 mb-2">Please fix the following errors:</h4>
          <ul className="list-disc list-inside text-sm text-red-700">
            {validationErrors.map((error, index) => (
              <li key={index}>{error}</li>
            ))}
          </ul>
        </div>
      )}

      {/* View Reception Details Button */}
      {currentPatient && (
        <div className="flex justify-end">
          <Button
            type="button"
            variant="outline"
            onClick={() => setShowPatientDetails(true)}
            className="mb-4"
          >
            <Eye className="h-4 w-4 mr-2" />
            View Reception Details
          </Button>
        </div>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Consultation Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="doctor_name">Doctor Name *</Label>
              <Input
                id="doctor_name"
                type="text"
                required
                value={formData.doctor_name}
                onChange={(e) => setFormData(prev => ({ ...prev, doctor_name: e.target.value }))}
              />
            </div>
          </div>

          {/* Department Selection - Only show for initial consultations */}
          {consultationType === 'initial' && (
            <div className="space-y-2">
              <Label htmlFor="department">Medical Department (Optional)</Label>
              <Select
                value={formData.department_id}
                onValueChange={(value) => setFormData(prev => ({ ...prev, department_id: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select medical department (optional)" />
                </SelectTrigger>
                <SelectContent>
                  {departments.map(dept => (
                    <SelectItem key={dept.id} value={dept.id}>
                      <div className="flex items-center">
                        <div
                          className="w-3 h-3 rounded-full mr-2"
                          style={{ backgroundColor: dept.color }}
                        />
                        {dept.name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-500">
                Select the medical department for this consultation (optional)
              </p>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="symptoms">Symptoms</Label>
            <Textarea
              id="symptoms"
              rows={3}
              value={formData.symptoms}
              onChange={(e) => setFormData(prev => ({ ...prev, symptoms: e.target.value }))}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="diagnosis">Diagnosis</Label>
            <Textarea
              id="diagnosis"
              rows={3}
              value={formData.diagnosis}
              onChange={(e) => setFormData(prev => ({ ...prev, diagnosis: e.target.value }))}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="treatment_plan">Treatment Plan</Label>
            <Textarea
              id="treatment_plan"
              rows={4}
              value={formData.treatment_plan}
              onChange={(e) => setFormData(prev => ({ ...prev, treatment_plan: e.target.value }))}
            />
          </div>

          {/* Time Recording Section */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="start_time">Consultation Start Time *</Label>
              <Input
                id="start_time"
                type="time"
                required
                value={formData.start_time}
                onChange={(e) => setFormData(prev => ({ ...prev, start_time: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="end_time">Consultation End Time</Label>
              <Input
                id="end_time"
                type="time"
                value={formData.end_time}
                onChange={(e) => setFormData(prev => ({ ...prev, end_time: e.target.value }))}
                placeholder="Will auto-fill on save if empty"
              />
              <p className="text-xs text-gray-500">
                End time will be automatically set to current time if left empty
              </p>
            </div>
          </div>

          {/* Fees Section */}
          <div className="space-y-4 border-t pt-4">
            <h3 className="text-lg font-medium text-gray-900">Consultation Fees</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="fee_amount">Fee Amount</Label>
                <Input
                  id="fee_amount"
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.fee_amount}
                  onChange={(e) => {
                    const amount = e.target.value;
                    setFormData(prev => ({
                      ...prev,
                      fee_amount: amount,
                      // Automatically set as paid when amount is added
                      fee_paid: amount && parseFloat(amount) > 0 ? true : false
                    }));
                  }}
                  placeholder="0.00"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="fee_paid">Payment Status</Label>
                <div className="flex items-center space-x-2">
                  <input
                    id="fee_paid"
                    type="checkbox"
                    checked={formData.fee_paid} // Direct logic: checked means PAID
                    onChange={(e) => setFormData(prev => ({ ...prev, fee_paid: e.target.checked }))} // Direct logic
                    className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                  />
                  <Label htmlFor="fee_paid" className="text-sm font-normal">
                    {formData.fee_paid ? '✅ Payment Received' : '❌ Mark as Paid'}
                  </Label>
                </div>
                <p className="text-xs text-gray-500">
                  {formData.fee_paid ? 'Payment has been received' : 'Check this box when payment is received'}
                </p>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="fee_notes">Fee Notes (Optional)</Label>
              <Input
                id="fee_notes"
                type="text"
                value={formData.fee_notes}
                onChange={(e) => setFormData(prev => ({ ...prev, fee_notes: e.target.value }))}
                placeholder="Payment method, receipt number, etc."
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Vital Signs</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="blood_pressure">Blood Pressure</Label>
              <Input
                id="blood_pressure"
                type="text"
                placeholder="120/80"
                value={formData.vital_signs.blood_pressure}
                onChange={(e) => handleVitalSignsChange('blood_pressure', e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="temperature">Temperature (°C)</Label>
              <Input
                id="temperature"
                type="text"
                placeholder="37.5"
                value={formData.vital_signs.temperature}
                onChange={(e) => handleVitalSignsChange('temperature', e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="pulse">Pulse (bpm)</Label>
              <Input
                id="pulse"
                type="text"
                placeholder="72"
                value={formData.vital_signs.pulse}
                onChange={(e) => handleVitalSignsChange('pulse', e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="respiratory_rate">Respiratory Rate</Label>
              <Input
                id="respiratory_rate"
                type="text"
                placeholder="16"
                value={formData.vital_signs.respiratory_rate}
                onChange={(e) => handleVitalSignsChange('respiratory_rate', e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="weight">Weight (kg)</Label>
              <Input
                id="weight"
                type="text"
                placeholder="70"
                value={formData.vital_signs.weight}
                onChange={(e) => handleVitalSignsChange('weight', e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="height">Height (cm)</Label>
              <Input
                id="height"
                type="text"
                placeholder="175"
                value={formData.vital_signs.height}
                onChange={(e) => handleVitalSignsChange('height', e.target.value)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Workflow Decision</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Label htmlFor="labTestsRequired">Laboratory Tests Required?</Label>
            <select
              id="labTestsRequired"
              value={formData.labTestsRequired}
              onChange={(e) => setFormData(prev => ({ ...prev, labTestsRequired: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="no">No - Send directly to Pharmacy</option>
              <option value="yes">Yes - Send to Laboratory first</option>
            </select>
          </div>
        </CardContent>
      </Card>

      <div className="space-y-2">
        <Label htmlFor="notes">Additional Notes</Label>
        <Textarea
          id="notes"
          rows={3}
          value={formData.notes}
          onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
        />
      </div>

      {/* Medicine Prescription Section (Optional) */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Medicine Prescription (Optional)
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addMedicine}
            >
              <Plus className="h-4 w-4 mr-1" />
              Add Medicine
            </Button>
          </CardTitle>
          <p className="text-sm text-gray-600 mt-2">
            Add prescribed medicines if needed. This section is optional.
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          {prescribedMedicines.map((medicine, index) => (
            <Card key={index} className="p-4 border-2 border-dashed border-gray-200">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium text-gray-900">Medicine {index + 1}</h4>
                {prescribedMedicines.length > 1 && (
                  <Button
                    type="button"
                    variant="destructive"
                    size="sm"
                    onClick={() => removeMedicine(index)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor={`medicine-name-${index}`}>Medicine Name</Label>
                  <Input
                    id={`medicine-name-${index}`}
                    value={medicine.name}
                    onChange={(e) => updateMedicine(index, 'name', e.target.value)}
                    placeholder="e.g., Paracetamol"
                  />
                </div>

                <div>
                  <Label htmlFor={`medicine-dosage-${index}`}>Dosage</Label>
                  <Input
                    id={`medicine-dosage-${index}`}
                    value={medicine.dosage}
                    onChange={(e) => updateMedicine(index, 'dosage', e.target.value)}
                    placeholder="e.g., 500mg"
                  />
                </div>

                <div>
                  <Label htmlFor={`medicine-frequency-${index}`}>Frequency</Label>
                  <Input
                    id={`medicine-frequency-${index}`}
                    value={medicine.frequency}
                    onChange={(e) => updateMedicine(index, 'frequency', e.target.value)}
                    placeholder="e.g., 3 times daily"
                  />
                </div>

                <div>
                  <Label htmlFor={`medicine-duration-${index}`}>Duration</Label>
                  <Input
                    id={`medicine-duration-${index}`}
                    value={medicine.duration}
                    onChange={(e) => updateMedicine(index, 'duration', e.target.value)}
                    placeholder="e.g., 7 days"
                  />
                </div>
              </div>

              <div className="mt-4">
                <Label htmlFor={`medicine-instructions-${index}`}>Special Instructions</Label>
                <Textarea
                  id={`medicine-instructions-${index}`}
                  value={medicine.instructions}
                  onChange={(e) => updateMedicine(index, 'instructions', e.target.value)}
                  placeholder="e.g., Take after meals, avoid alcohol"
                  rows={2}
                />
              </div>
            </Card>
          ))}

          {prescribedMedicines.length === 0 && (
            <div className="text-center py-8 border-2 border-dashed border-gray-200 rounded-lg">
              <Pill className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <p className="text-gray-500">No medicines prescribed (optional)</p>
              <p className="text-xs text-gray-400 mb-4">You can skip this section if no medicines are needed</p>
              <Button
                type="button"
                variant="outline"
                onClick={addMedicine}
                className="mt-2"
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Medicine (Optional)
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      <Button type="submit" disabled={isSubmitting} className="w-full">
        {isSubmitting ? 'Saving...' : (isEditMode ? 'Update Consultation Record' : 'Save Consultation Record')}
      </Button>

      {/* Patient Details Dialog */}
      <Dialog open={showPatientDetails} onOpenChange={setShowPatientDetails}>
        <DialogContent className="w-[95vw] max-w-4xl max-h-[90vh] overflow-y-auto mx-auto">
          <DialogHeader>
            <DialogTitle>Reception Details</DialogTitle>
            <DialogDescription>
              Patient information registered at reception
            </DialogDescription>
          </DialogHeader>
          {currentPatient && (
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center">
                    <User className="h-5 w-5 mr-2" />
                    Patient Information
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium text-gray-500">Full Name</p>
                      <p className="text-gray-900">{currentPatient.patient_name}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Age</p>
                      <p className="text-gray-900">{getPatientAge(currentPatient.date_of_birth)} years</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Date of Birth</p>
                      <p className="text-gray-900">{new Date(currentPatient.date_of_birth).toLocaleDateString()}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Gender</p>
                      <p className="text-gray-900 capitalize">{currentPatient.gender}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Phone Number</p>
                      <div className="flex items-center">
                        <Phone className="h-4 w-4 mr-2 text-gray-400" />
                        <p className="text-gray-900">{currentPatient.phone_number}</p>
                      </div>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Email</p>
                      <div className="flex items-center">
                        <Mail className="h-4 w-4 mr-2 text-gray-400" />
                        <p className="text-gray-900">{currentPatient.email}</p>
                      </div>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Blood Type</p>
                      <div className="flex items-center">
                        <Heart className="h-4 w-4 mr-2 text-gray-400" />
                        <p className="text-gray-900">{currentPatient.blood_type || 'Not specified'}</p>
                      </div>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Registration Date</p>
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                        <p className="text-gray-900">{new Date(currentPatient.created_at).toLocaleDateString()}</p>
                      </div>
                    </div>
                  </div>

                  {/* Registration Time Information */}
                  {currentPatient.registration_time && (
                    <div className="mt-4 p-3 bg-green-50 rounded-lg">
                      <p className="text-sm font-medium text-green-800 mb-2">⏰ Registration Time</p>
                      <div className="text-sm">
                        <span className="font-medium text-green-700">Time:</span>
                        <span className="ml-2 text-green-900">{currentPatient.registration_time}</span>
                      </div>
                    </div>
                  )}

                  {currentPatient.emergency_contact && (
                    <div className="mt-4 pt-4 border-t">
                      <h4 className="font-medium text-gray-900 mb-2">Emergency Contact</h4>
                      <p className="text-gray-700">{currentPatient.emergency_contact}</p>
                    </div>
                  )}

                  {currentPatient.medical_history && (
                    <div className="mt-4 pt-4 border-t">
                      <h4 className="font-medium text-gray-900 mb-2">Medical History</h4>
                      <p className="text-gray-700">{currentPatient.medical_history}</p>
                    </div>
                  )}

                  {/* Fee Information from Previous Step (if any) */}
                  <div className="mt-4 p-4 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200">
                    <p className="text-sm font-bold text-green-800 mb-3 flex items-center">
                      💰 Registration Fee Details
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <div className="text-sm">
                          <span className="font-medium text-green-700">Fee Amount:</span>
                          <span className="ml-2 text-green-900 font-semibold">
                            {formatCurrency(currentPatient.fee_amount || 0)}
                          </span>
                        </div>
                        <div className="text-sm">
                          <span className="font-medium text-green-700">Payment Status:</span>
                          <span className={`ml-2 font-bold ${currentPatient.fee_paid ? 'text-green-700' : 'text-red-700'}`}>
                            {currentPatient.fee_paid ? '✅ PAID' : '❌ NOT PAID'}
                          </span>
                        </div>
                      </div>
                      {currentPatient.fee_notes && (
                        <div className="text-sm">
                          <span className="font-medium text-green-700">Payment Notes:</span>
                          <p className="ml-2 text-green-900 bg-white p-2 rounded border">
                            {currentPatient.fee_notes}
                          </p>
                        </div>
                      )}
                    </div>
                    {!currentPatient.fee_paid && currentPatient.fee_amount > 0 && (
                      <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded">
                        <p className="text-xs text-yellow-700 font-medium">
                          ⚠️ Outstanding registration fee
                        </p>
                      </div>
                    )}
                  </div>

                  <div className="mt-4 pt-4 border-t">
                    <Badge className="bg-green-100 text-green-800">
                      ✅ Registered at Reception
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </form>
  );
};
