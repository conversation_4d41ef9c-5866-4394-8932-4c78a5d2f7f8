
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Plus, X, Eye, TestTube, User, Calendar, FileText, Pill, AlertTriangle, DollarSign } from 'lucide-react';
import { useWorkflowData } from '@/hooks/useWorkflowData';
import { useNavigate } from 'react-router-dom';
import { toast } from '@/hooks/use-toast';
import { useCurrency } from '@/contexts/CurrencyContext';

interface PharmacyFormProps {
  patientId: string;
  workflowId: string;
  onSuccess: (data?: any) => void;
  autoAdvance?: boolean;
  existingData?: any; // Add existing pharmacy data
  laboratoryData?: any; // Add laboratory data from previous step
  consultationData?: any; // Add consultation data for prescribed medicines
}

interface Medication {
  name: string;
  dosage: string;
  quantity: number;
  instructions: string;
  price: number;
}

export const PharmacyForm = ({
  patientId,
  workflowId,
  onSuccess,
  autoAdvance = true,
  existingData,
  laboratoryData,
  consultationData
}: PharmacyFormProps) => {
  const navigate = useNavigate();
  const { formatCurrency } = useCurrency();
  const [showLaboratoryDetails, setShowLaboratoryDetails] = useState(false);
  const [formData, setFormData] = useState({
    pharmacist_name: existingData?.pharmacist_name || '',
    notes: existingData?.notes || '',
    fee_amount: existingData?.fee_amount || '',
    fee_paid: existingData?.fee_paid || false,
    fee_notes: existingData?.fee_notes || ''
  });

  // Medicine approval functions
  const updateMedicineApproval = (index: number, field: string, value: any) => {
    setMedicineApprovals(prev => prev.map((med, i) =>
      i === index ? { ...med, [field]: value } : med
    ));
  };

  const checkInventoryStock = (medicineName: string) => {
    // Mock inventory check - in real app, this would check against actual inventory
    const mockInventory = [
      { name: 'Paracetamol', stock: 100 },
      { name: 'Amoxicillin', stock: 50 },
      { name: 'Ibuprofen', stock: 75 },
      { name: 'Antibiotics', stock: 30 }
    ];

    const inventoryItem = mockInventory.find(item =>
      item.name.toLowerCase().includes(medicineName.toLowerCase()) ||
      medicineName.toLowerCase().includes(item.name.toLowerCase())
    );

    return inventoryItem ? inventoryItem.stock : 0;
  };
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { createPharmacyRecord, completeStageAndAdvance, useLaboratoryRecords, useConsultationRecords } = useWorkflowData();

  // Get laboratory and consultation details from previous steps
  const { data: laboratoryRecords = [] } = useLaboratoryRecords();
  const { data: consultationRecords = [] } = useConsultationRecords();
  const currentLaboratory = laboratoryData || laboratoryRecords.find(l => l.patient_id === patientId);
  const currentConsultation = consultationData || consultationRecords.find(c => c.patient_id === patientId);

  const isEditMode = !!existingData; // Check if we're editing existing data

  // Get all prescribed medicines from consultation and laboratory
  const getAllPrescribedMedicines = () => {
    const consultationMeds = currentConsultation?.prescribed_medicines || [];
    const laboratoryMeds = currentLaboratory?.prescribed_medicines || [];

    // If no prescribed_medicines field, try to parse from notes
    let notesBasedMeds: any[] = [];
    if (currentConsultation?.notes && currentConsultation.notes.includes('--- PRESCRIBED MEDICINES ---')) {
      // For now, return empty array - in future we could parse the notes
      // This is a temporary fallback until the database schema is updated
      notesBasedMeds = [];
    }

    return [...consultationMeds, ...laboratoryMeds, ...notesBasedMeds];
  };

  // Medicine approval state
  const [medicineApprovals, setMedicineApprovals] = useState(() => {
    const allMeds = getAllPrescribedMedicines();
    return allMeds.map(med => ({
      ...med,
      approved: false,
      inStock: true, // This would be checked against actual inventory
      notes: '',
      dispensed_quantity: 0
    }));
  });

  const getTotalApprovedCost = () => {
    return medicineApprovals
      .filter(med => med.approved)
      .reduce((total, med) => total + (med.dispensed_quantity * 5), 0); // Mock price calculation
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Check if this is edit mode - if so, just show success without creating duplicate
      if (isEditMode) {
        // Patient already has pharmacy record - just show success message
        console.log('Editing existing pharmacy record for patient:', patientId);
        toast({
          title: "Information",
          description: "This patient already has a pharmacy record. Update functionality will be available soon.",
          variant: "default"
        });

        onSuccess(existingData);
        return; // Exit early to prevent duplicate creation
      }

      // Validate that at least one medicine is approved
      const approvedMedicines = medicineApprovals.filter(med => med.approved);

      if (approvedMedicines.length === 0) {
        toast({
          title: "No Medicines Approved",
          description: "Please approve at least one medicine before completing pharmacy record.",
          variant: "destructive"
        });
        setIsSubmitting(false);
        return;
      }

      // Create pharmacy record with approved medicines only
      const pharmacyData = {
        patient_id: patientId,
        pharmacist_name: formData.pharmacist_name,
        notes: formData.notes,
        prescribed_medicines: getAllPrescribedMedicines(),
        approved_medicines: approvedMedicines,
        total_cost: getTotalApprovedCost(),
        dispensed_date: new Date().toISOString().split('T')[0],
        status: 'completed',
        fee_amount: parseFloat(formData.fee_amount) || 0,
        fee_paid: formData.fee_paid,
        fee_notes: formData.fee_notes
      };

      const result = await createPharmacyRecord.mutateAsync(pharmacyData);

      // Show success message
      toast({
        title: "Medicines Approved Successfully",
        description: `${approvedMedicines.length} medicines approved. Patient record updated.`,
      });

      onSuccess(result);

      // Auto-navigate to medical records page to show updated record
      if (autoAdvance) {
        setTimeout(() => {
          navigate('/medical-records');
        }, 1500);
      }
    } catch (error) {
      console.error('Error creating pharmacy record:', error);
      toast({
        title: "Error",
        description: "Failed to save pharmacy record. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Mode Indicator */}
      {isEditMode && (
        <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-sm text-blue-800">
            <strong>Editing existing pharmacy record</strong> - Form is pre-filled with current data
          </p>
        </div>
      )}

      {/* View Laboratory Details Button */}
      {currentLaboratory && (
        <div className="flex justify-end">
          <Button
            type="button"
            variant="outline"
            onClick={() => setShowLaboratoryDetails(true)}
            className="mb-4"
          >
            <Eye className="h-4 w-4 mr-2" />
            View Laboratory Details
          </Button>
        </div>
      )}

      {/* Prescribed Medicine Approval Section (Optional) */}
      {getAllPrescribedMedicines().length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Pill className="mr-2 h-5 w-5" />
              Prescribed Medicine Approval (Optional)
            </CardTitle>
            <p className="text-sm text-gray-600">
              Review and approve prescribed medicines from consultation and laboratory. This section is optional if no medicines were prescribed.
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            {medicineApprovals.map((medicine, index) => {
              const stockQuantity = checkInventoryStock(medicine.name);
              const inStock = stockQuantity > 0;

              return (
                <Card key={index} className={`p-4 border-2 ${medicine.approved ? 'border-green-200 bg-green-50' : 'border-gray-200'}`}>
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <h4 className="font-medium text-gray-900">{medicine.name}</h4>
                      <p className="text-sm text-gray-600">
                        {medicine.dosage} • {medicine.frequency} • {medicine.duration}
                      </p>
                      {medicine.instructions && (
                        <p className="text-xs text-gray-500 mt-1">{medicine.instructions}</p>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant={inStock ? "default" : "destructive"}>
                        {inStock ? `${stockQuantity} in stock` : 'Out of stock'}
                      </Badge>
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id={`approve-${index}`}
                          checked={medicine.approved}
                          onChange={(e) => updateMedicineApproval(index, 'approved', e.target.checked)}
                          disabled={!inStock}
                          className="rounded"
                        />
                        <Label htmlFor={`approve-${index}`} className="text-sm">
                          {medicine.approved ? 'Approved' : 'Approve'}
                        </Label>
                      </div>
                    </div>
                  </div>

                  {medicine.approved && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4 pt-4 border-t">
                      <div>
                        <Label htmlFor={`quantity-${index}`}>Dispensed Quantity</Label>
                        <Input
                          id={`quantity-${index}`}
                          type="number"
                          min="1"
                          max={stockQuantity}
                          value={medicine.dispensed_quantity}
                          onChange={(e) => updateMedicineApproval(index, 'dispensed_quantity', parseInt(e.target.value) || 0)}
                          placeholder="Enter quantity"
                        />
                      </div>
                      <div>
                        <Label htmlFor={`notes-${index}`}>Pharmacist Notes</Label>
                        <Input
                          id={`notes-${index}`}
                          value={medicine.notes}
                          onChange={(e) => updateMedicineApproval(index, 'notes', e.target.value)}
                          placeholder="Any special notes"
                        />
                      </div>
                    </div>
                  )}

                  {!inStock && (
                    <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded">
                      <p className="text-sm text-red-700">
                        <AlertTriangle className="h-4 w-4 inline mr-1" />
                        This medicine is out of stock. Please check inventory or suggest alternative.
                      </p>
                    </div>
                  )}
                </Card>
              );
            })}

            <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded">
              <h4 className="font-medium text-blue-900 mb-2">Approval Summary</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-blue-700">Total Prescribed:</span>
                  <span className="font-medium ml-2">{getAllPrescribedMedicines().length}</span>
                </div>
                <div>
                  <span className="text-blue-700">Approved:</span>
                  <span className="font-medium ml-2">{medicineApprovals.filter(m => m.approved).length}</span>
                </div>
                <div>
                  <span className="text-blue-700">In Stock:</span>
                  <span className="font-medium ml-2">{medicineApprovals.filter(m => checkInventoryStock(m.name) > 0).length}</span>
                </div>
                <div>
                  <span className="text-blue-700">Out of Stock:</span>
                  <span className="font-medium ml-2">{medicineApprovals.filter(m => checkInventoryStock(m.name) === 0).length}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* No Prescribed Medicines Message */}
      {getAllPrescribedMedicines().length === 0 && (
        <Card>
          <CardContent className="p-6">
            <div className="text-center py-8">
              <Pill className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Medicines Prescribed</h3>
              <p className="text-gray-500 mb-2">
                No medicines were prescribed during consultation or laboratory stages.
              </p>
              <p className="text-sm text-gray-400">
                This is optional - you can proceed without dispensing any medicines.
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Pharmacy Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="pharmacist_name">Pharmacist Name *</Label>
            <Input
              id="pharmacist_name"
              type="text"
              required
              value={formData.pharmacist_name}
              onChange={(e) => setFormData(prev => ({ ...prev, pharmacist_name: e.target.value }))}
            />
          </div>
        </CardContent>
      </Card>

      {/* Dispensing Summary */}
      {medicineApprovals.filter(med => med.approved).length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <DollarSign className="mr-2 h-5 w-5" />
              Dispensing Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Approved Medicines:</span>
                <span className="font-medium">{medicineApprovals.filter(med => med.approved).length}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Total Quantity:</span>
                <span className="font-medium">
                  {medicineApprovals.filter(med => med.approved).reduce((total, med) => total + med.dispensed_quantity, 0)} units
                </span>
              </div>
              <div className="flex justify-between items-center text-lg font-bold border-t pt-3">
                <span className="text-gray-900">Total Cost:</span>
                <span className="text-green-600">${getTotalApprovedCost().toFixed(2)}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="space-y-2">
        <Label htmlFor="notes">Pharmacy Notes</Label>
        <Textarea
          id="notes"
          rows={3}
          value={formData.notes}
          onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
        />
      </div>

      {/* Fees Section */}
      <Card>
        <CardHeader>
          <CardTitle>Pharmacy Fees</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="fee_amount">Fee Amount</Label>
              <Input
                id="fee_amount"
                type="number"
                step="0.01"
                min="0"
                value={formData.fee_amount}
                onChange={(e) => {
                  const amount = e.target.value;
                  setFormData(prev => ({
                    ...prev,
                    fee_amount: amount,
                    // Automatically set as paid when amount is added
                    fee_paid: amount && parseFloat(amount) > 0 ? true : false
                  }));
                }}
                placeholder="0.00"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="fee_paid">Payment Status</Label>
              <div className="flex items-center space-x-2">
                <input
                  id="fee_paid"
                  type="checkbox"
                  checked={!formData.fee_paid} // Inverted: checked means NOT paid
                  onChange={(e) => setFormData(prev => ({ ...prev, fee_paid: !e.target.checked }))} // Inverted logic
                  className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                />
                <Label htmlFor="fee_paid" className="text-sm font-normal">
                  {!formData.fee_paid ? '❌ Mark as Not Paid' : '✅ Payment Received'}
                </Label>
              </div>
              <p className="text-xs text-gray-500">
                {formData.fee_paid ? 'Payment has been received' : 'Check this box if payment was not received'}
              </p>
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="fee_notes">Fee Notes (Optional)</Label>
            <Input
              id="fee_notes"
              type="text"
              value={formData.fee_notes}
              onChange={(e) => setFormData(prev => ({ ...prev, fee_notes: e.target.value }))}
              placeholder="Payment method, receipt number, etc."
            />
          </div>
        </CardContent>
      </Card>

      <Button
        type="submit"
        disabled={isSubmitting || medicineApprovals.filter(med => med.approved).length === 0}
        className="w-full bg-green-600 hover:bg-green-700"
      >
        {isSubmitting ? 'Processing...' : 'Complete Pharmacy Approval & Push to Final Records'}
      </Button>

      {medicineApprovals.filter(med => med.approved).length === 0 && (
        <p className="text-sm text-red-600 text-center">
          Please approve at least one medicine before completing the pharmacy record.
        </p>
      )}

      {/* Laboratory Details Dialog */}
      <Dialog open={showLaboratoryDetails} onOpenChange={setShowLaboratoryDetails}>
        <DialogContent className="w-[95vw] max-w-4xl max-h-[90vh] overflow-y-auto mx-auto">
          <DialogHeader>
            <DialogTitle>Laboratory Details</DialogTitle>
            <DialogDescription>
              Laboratory test results from previous step
            </DialogDescription>
          </DialogHeader>
          {currentLaboratory && (
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center">
                    <TestTube className="h-5 w-5 mr-2" />
                    Laboratory Information
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium text-gray-500">Technician Name</p>
                      <p className="text-gray-900">{currentLaboratory.technician_name}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Test Date</p>
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                        <p className="text-gray-900">{new Date(currentLaboratory.test_date).toLocaleDateString()}</p>
                      </div>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Test Type</p>
                      <p className="text-gray-900">{currentLaboratory.test_type}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Status</p>
                      <Badge className={currentLaboratory.status === 'completed' ? 'bg-green-100 text-green-800' : 'bg-orange-100 text-orange-800'}>
                        {currentLaboratory.status}
                      </Badge>
                    </div>
                  </div>

                  <div className="mt-4 space-y-3">
                    {currentLaboratory.test_results && Object.keys(currentLaboratory.test_results).length > 0 && (
                      <div>
                        <p className="text-sm font-medium text-gray-500">Test Results</p>
                        <div className="bg-gray-50 p-3 rounded">
                          <div className="grid grid-cols-2 gap-2 text-sm">
                            {Object.entries(currentLaboratory.test_results).map(([key, value]) => (
                              value && (
                                <div key={key} className="flex justify-between">
                                  <span className="font-medium">{key.replace('_', ' ').toUpperCase()}:</span>
                                  <span>{value}</span>
                                </div>
                              )
                            ))}
                          </div>
                        </div>
                      </div>
                    )}

                    {currentLaboratory.lab_notes && (
                      <div>
                        <p className="text-sm font-medium text-gray-500">Lab Notes</p>
                        <p className="text-gray-900 bg-gray-50 p-2 rounded">{currentLaboratory.lab_notes}</p>
                      </div>
                    )}
                  </div>

                  {/* Fee Information from Previous Step */}
                  <div className="mt-4 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
                    <p className="text-sm font-bold text-blue-800 mb-3 flex items-center">
                      💰 Laboratory Fee Details
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <div className="text-sm">
                          <span className="font-medium text-blue-700">Fee Amount:</span>
                          <span className="ml-2 text-blue-900 font-semibold">
                            {formatCurrency(currentLaboratory.fee_amount || 0)}
                          </span>
                        </div>
                        <div className="text-sm">
                          <span className="font-medium text-blue-700">Payment Status:</span>
                          <span className={`ml-2 font-bold ${currentLaboratory.fee_paid ? 'text-green-700' : 'text-red-700'}`}>
                            {currentLaboratory.fee_paid ? '✅ PAID' : '❌ NOT PAID'}
                          </span>
                        </div>
                      </div>
                      {currentLaboratory.fee_notes && (
                        <div className="text-sm">
                          <span className="font-medium text-blue-700">Payment Notes:</span>
                          <p className="ml-2 text-blue-900 bg-white p-2 rounded border">
                            {currentLaboratory.fee_notes}
                          </p>
                        </div>
                      )}
                    </div>
                    {!currentLaboratory.fee_paid && currentLaboratory.fee_amount > 0 && (
                      <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded">
                        <p className="text-xs text-yellow-700 font-medium">
                          ⚠️ Outstanding laboratory fee
                        </p>
                      </div>
                    )}
                  </div>

                  <div className="mt-4 pt-4 border-t">
                    <Badge className="bg-green-100 text-green-800">
                      ✅ Laboratory Tests Completed
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </form>
  );
};
