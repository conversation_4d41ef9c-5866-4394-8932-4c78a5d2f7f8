import React from 'react';
import { Badge } from '@/components/ui/badge';
import { 
  UserCheck, 
  Stethoscope, 
  TestTube, 
  UserCog, 
  CheckCircle,
  Clock,
  ArrowRight
} from 'lucide-react';

interface WorkflowStatusIndicatorProps {
  currentStatus: string;
  patientId?: string;
  showProgress?: boolean;
  size?: 'sm' | 'md' | 'lg';
  orientation?: 'horizontal' | 'vertical';
}

export const WorkflowStatusIndicator = ({
  currentStatus,
  patientId,
  showProgress = true,
  size = 'md',
  orientation = 'horizontal'
}: WorkflowStatusIndicatorProps) => {
  
  const workflowSteps = [
    {
      id: 'reception',
      label: 'Reception',
      icon: UserCheck,
      color: 'bg-blue-500',
      description: 'Patient Registration'
    },
    {
      id: 'consultation',
      label: 'Initial Consultation',
      icon: Stethoscope,
      color: 'bg-green-500',
      description: 'Medical Examination'
    },
    {
      id: 'laboratory',
      label: 'Laboratory',
      icon: TestTube,
      color: 'bg-purple-500',
      description: 'Lab Tests & Analysis'
    },
    {
      id: 'follow_up_consultation',
      label: 'Follow-up',
      icon: UserCog,
      color: 'bg-orange-500',
      description: 'Results Review'
    },
    {
      id: 'completed',
      label: 'Completed',
      icon: CheckCircle,
      color: 'bg-gray-500',
      description: 'Workflow Finished'
    }
  ];

  const getCurrentStepIndex = () => {
    return workflowSteps.findIndex(step => step.id === currentStatus);
  };

  const currentStepIndex = getCurrentStepIndex();

  const getStepStatus = (stepIndex: number) => {
    if (stepIndex < currentStepIndex) return 'completed';
    if (stepIndex === currentStepIndex) return 'current';
    return 'pending';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'current': return 'text-blue-600 bg-blue-100';
      case 'pending': return 'text-gray-400 bg-gray-100';
      default: return 'text-gray-400 bg-gray-100';
    }
  };

  const getIconSize = () => {
    switch (size) {
      case 'sm': return 'h-4 w-4';
      case 'md': return 'h-5 w-5';
      case 'lg': return 'h-6 w-6';
      default: return 'h-5 w-5';
    }
  };

  const getBadgeSize = () => {
    switch (size) {
      case 'sm': return 'text-xs px-2 py-1';
      case 'md': return 'text-sm px-3 py-1';
      case 'lg': return 'text-base px-4 py-2';
      default: return 'text-sm px-3 py-1';
    }
  };

  if (!showProgress) {
    // Simple status badge
    const currentStep = workflowSteps.find(step => step.id === currentStatus);
    if (!currentStep) return null;

    const Icon = currentStep.icon;
    return (
      <Badge className={`${getStatusColor('current')} ${getBadgeSize()} flex items-center gap-2`}>
        <Icon className={getIconSize()} />
        {currentStep.label}
      </Badge>
    );
  }

  // Full progress indicator
  return (
    <div className={`workflow-status-indicator ${orientation === 'vertical' ? 'flex flex-col space-y-2' : 'flex items-center space-x-2'}`}>
      {workflowSteps.map((step, index) => {
        const Icon = step.icon;
        const status = getStepStatus(index);
        const isLast = index === workflowSteps.length - 1;

        return (
          <React.Fragment key={step.id}>
            <div className={`flex items-center ${orientation === 'vertical' ? 'flex-col text-center' : 'flex-row'} space-x-2`}>
              <div className={`
                flex items-center justify-center rounded-full p-2
                ${status === 'completed' ? 'bg-green-100 text-green-600' : ''}
                ${status === 'current' ? 'bg-blue-100 text-blue-600 ring-2 ring-blue-300' : ''}
                ${status === 'pending' ? 'bg-gray-100 text-gray-400' : ''}
              `}>
                <Icon className={getIconSize()} />
              </div>
              
              {orientation === 'vertical' && (
                <div className="text-center">
                  <div className={`text-xs font-medium ${
                    status === 'current' ? 'text-blue-600' : 
                    status === 'completed' ? 'text-green-600' : 'text-gray-400'
                  }`}>
                    {step.label}
                  </div>
                  <div className="text-xs text-gray-500">{step.description}</div>
                </div>
              )}
              
              {orientation === 'horizontal' && (
                <div className={`text-sm font-medium ${
                  status === 'current' ? 'text-blue-600' : 
                  status === 'completed' ? 'text-green-600' : 'text-gray-400'
                }`}>
                  {step.label}
                </div>
              )}
            </div>
            
            {!isLast && orientation === 'horizontal' && (
              <ArrowRight className={`h-4 w-4 ${
                status === 'completed' ? 'text-green-400' : 'text-gray-300'
              }`} />
            )}
            
            {!isLast && orientation === 'vertical' && (
              <div className={`w-px h-8 mx-auto ${
                status === 'completed' ? 'bg-green-300' : 'bg-gray-200'
              }`} />
            )}
          </React.Fragment>
        );
      })}
    </div>
  );
};

// Helper component for current status only
export const CurrentWorkflowStatus = ({ 
  currentStatus, 
  size = 'sm' 
}: { 
  currentStatus: string; 
  size?: 'sm' | 'md' | 'lg' 
}) => {
  return (
    <WorkflowStatusIndicator 
      currentStatus={currentStatus} 
      showProgress={false} 
      size={size} 
    />
  );
};

// Helper component for full progress
export const WorkflowProgress = ({ 
  currentStatus, 
  patientId, 
  orientation = 'horizontal' 
}: { 
  currentStatus: string; 
  patientId?: string; 
  orientation?: 'horizontal' | 'vertical' 
}) => {
  return (
    <WorkflowStatusIndicator 
      currentStatus={currentStatus} 
      patientId={patientId}
      showProgress={true} 
      orientation={orientation}
      size="md"
    />
  );
};
