
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Clock, User, Calendar } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface WorkflowPatientCardProps {
  workflow: any;
  onClick: () => void;
  showCompleted?: boolean;
}

export const WorkflowPatientCard = ({ workflow, onClick, showCompleted = false }: WorkflowPatientCardProps) => {
  const patient = workflow.patients;
  
  const getDepartmentColor = (department: string) => {
    switch (department) {
      case 'reception': return 'bg-blue-100 text-blue-800';
      case 'consultation': return 'bg-green-100 text-green-800';
      case 'laboratory': return 'bg-orange-100 text-orange-800';
      case 'pharmacy': return 'bg-purple-100 text-purple-800';
      case 'completed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const calculateAge = (dateOfBirth: string) => {
    if (!dateOfBirth) return 'N/A';
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  return (
    <Card 
      className="cursor-pointer hover:shadow-md transition-shadow border-l-4 border-l-blue-500"
      onClick={onClick}
    >
      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center space-x-3">
            <Avatar className="h-10 w-10">
              <AvatarFallback>
                {patient?.patient_name?.split(' ').map((n: string) => n[0]).join('') || 'P'}
              </AvatarFallback>
            </Avatar>
            <div>
              <h3 className="font-semibold text-gray-900">{patient?.patient_name}</h3>
              <p className="text-sm text-gray-500">Age: {calculateAge(patient?.date_of_birth)}</p>
            </div>
          </div>
          <Badge className={getDepartmentColor(workflow.current_department)}>
            {workflow.current_department}
          </Badge>
        </div>

        <div className="space-y-2">
          {workflow.assigned_to && (
            <div className="flex items-center text-sm text-gray-600">
              <User className="h-4 w-4 mr-2" />
              <span>Assigned to: {workflow.assigned_to}</span>
            </div>
          )}
          
          <div className="flex items-center text-sm text-gray-600">
            <Clock className="h-4 w-4 mr-2" />
            <span>Updated {formatDistanceToNow(new Date(workflow.updated_at))} ago</span>
          </div>

          <div className="flex items-center text-sm text-gray-600">
            <Calendar className="h-4 w-4 mr-2" />
            <span>Started {formatDistanceToNow(new Date(workflow.created_at))} ago</span>
          </div>
        </div>

        {workflow.notes && (
          <div className="mt-3 p-2 bg-gray-50 rounded text-sm text-gray-700">
            <strong>Notes:</strong> {workflow.notes}
          </div>
        )}

        {showCompleted && (
          <div className="mt-3 text-center">
            <Badge variant="outline" className="text-green-600">
              Workflow Completed
            </Badge>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
