
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { 
  Users, 
  Stethoscope, 
  FlaskConical, 
  Pill, 
  CheckCircle,
  ArrowRight,
  Clock,
  User,
  Phone,
  Mail
} from 'lucide-react';
import { useWorkflowData } from '@/hooks/useWorkflowData';
import { ConsultationForm } from './ConsultationForm';
import { LaboratoryForm } from './LaboratoryForm';
import { PharmacyForm } from './PharmacyForm';
import { WorkflowAdvanceDialog } from './WorkflowAdvanceDialog';

interface PatientWorkflowDialogProps {
  workflow: any;
  isOpen: boolean;
  onClose: () => void;
}

export const PatientWorkflowDialog = ({ workflow, isOpen, onClose }: PatientWorkflowDialogProps) => {
  const [showAdvanceDialog, setShowAdvanceDialog] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  if (!workflow) return null;

  const patient = workflow.patients;
  
  const getDepartmentIcon = (department: string) => {
    switch (department) {
      case 'reception': return Users;
      case 'consultation': return Stethoscope;
      case 'laboratory': return FlaskConical;
      case 'pharmacy': return Pill;
      case 'completed': return CheckCircle;
      default: return Clock;
    }
  };

  const getDepartmentColor = (department: string) => {
    switch (department) {
      case 'reception': return 'bg-blue-100 text-blue-800';
      case 'consultation': return 'bg-green-100 text-green-800';
      case 'laboratory': return 'bg-orange-100 text-orange-800';
      case 'pharmacy': return 'bg-purple-100 text-purple-800';
      case 'completed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const calculateAge = (dateOfBirth: string) => {
    if (!dateOfBirth) return 'N/A';
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  const canAdvanceWorkflow = workflow.current_department !== 'completed';

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Avatar className="h-12 w-12">
                  <AvatarFallback>
                    {patient?.patient_name?.split(' ').map((n: string) => n[0]).join('') || 'P'}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h2 className="text-xl font-bold">{patient?.patient_name}</h2>
                  <p className="text-sm text-gray-500">Patient Workflow</p>
                </div>
              </div>
              <Badge className={getDepartmentColor(workflow.current_department)}>
                {workflow.current_department}
              </Badge>
            </DialogTitle>
            <DialogDescription>
              View and manage patient workflow progress through different departments
            </DialogDescription>
          </DialogHeader>

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="consultation">Consultation</TabsTrigger>
              <TabsTrigger value="laboratory">Laboratory</TabsTrigger>
              <TabsTrigger value="pharmacy">Pharmacy</TabsTrigger>
              <TabsTrigger value="timeline">Timeline</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              {/* Patient Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Patient Information</h3>
                  <div className="space-y-3">
                    <div className="flex items-center">
                      <User className="h-4 w-4 mr-2 text-gray-500" />
                      <span>Age: {calculateAge(patient?.date_of_birth)} years</span>
                    </div>
                    <div className="flex items-center">
                      <Mail className="h-4 w-4 mr-2 text-gray-500" />
                      <span>{patient?.email}</span>
                    </div>
                    <div className="flex items-center">
                      <Phone className="h-4 w-4 mr-2 text-gray-500" />
                      <span>{patient?.phone_number}</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Workflow Status</h3>
                  <div className="space-y-3">
                    <div>
                      <span className="text-sm text-gray-500">Current Department:</span>
                      <Badge className={`ml-2 ${getDepartmentColor(workflow.current_department)}`}>
                        {workflow.current_department}
                      </Badge>
                    </div>
                    {workflow.assigned_to && (
                      <div>
                        <span className="text-sm text-gray-500">Assigned To:</span>
                        <span className="ml-2 font-medium">{workflow.assigned_to}</span>
                      </div>
                    )}
                    {workflow.notes && (
                      <div>
                        <span className="text-sm text-gray-500">Notes:</span>
                        <p className="mt-1 p-2 bg-gray-50 rounded text-sm">{workflow.notes}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end space-x-3">
                {canAdvanceWorkflow && (
                  <Button onClick={() => setShowAdvanceDialog(true)}>
                    <ArrowRight className="mr-2 h-4 w-4" />
                    Advance to Next Department
                  </Button>
                )}
              </div>
            </TabsContent>

            <TabsContent value="consultation">
              <ConsultationForm 
                patientId={patient?.id} 
                workflowId={workflow.id}
                onSuccess={() => setActiveTab('overview')}
              />
            </TabsContent>

            <TabsContent value="laboratory">
              <LaboratoryForm 
                patientId={patient?.id} 
                workflowId={workflow.id}
                onSuccess={() => setActiveTab('overview')}
              />
            </TabsContent>

            <TabsContent value="pharmacy">
              <PharmacyForm 
                patientId={patient?.id} 
                workflowId={workflow.id}
                onSuccess={() => setActiveTab('overview')}
              />
            </TabsContent>

            <TabsContent value="timeline">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Workflow Timeline</h3>
                <div className="space-y-3">
                  {/* This would show the complete workflow history */}
                  <div className="text-sm text-gray-500">
                    Detailed timeline view would be implemented here showing all department transitions and activities.
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>

      <WorkflowAdvanceDialog
        workflow={workflow}
        isOpen={showAdvanceDialog}
        onClose={() => setShowAdvanceDialog(false)}
        onSuccess={() => {
          setShowAdvanceDialog(false);
          onClose();
        }}
      />
    </>
  );
};
