import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Clock, Calendar, Users, DollarSign } from 'lucide-react';
import { useSupabaseData } from '@/hooks/useSupabaseData';
import { Settings, SettingsFormData, defaultSettings, workingDaysOptions } from '@/types/settings';

interface SettingsFormBusinessProps {}

export const SettingsFormBusiness = ({}: SettingsFormBusinessProps) => {
  const { useSettings, updateSettings } = useSupabaseData();
  const { data: settings, isLoading } = useSettings();
  const updateSettingsMutation = updateSettings();

  const [formData, setFormData] = useState<Partial<SettingsFormData>>({
    ...defaultSettings,
    business_hours_start: '08:00',
    business_hours_end: '18:00',
    working_days: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
    appointment_duration: 30,
    max_appointments_per_day: 50,
    tax_rate: 0,
    tax_name: 'VAT',
    invoice_prefix: 'INV',
    receipt_prefix: 'RCP',
    auto_invoice_numbering: true,
    payment_terms_days: 30,
  });

  // Load settings data when available
  useEffect(() => {
    if (settings) {
      setFormData({
        business_hours_start: settings.business_hours_start || '08:00',
        business_hours_end: settings.business_hours_end || '18:00',
        working_days: settings.working_days || ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
        appointment_duration: settings.appointment_duration || 30,
        max_appointments_per_day: settings.max_appointments_per_day || 50,
        tax_rate: settings.tax_rate || 0,
        tax_name: settings.tax_name || 'VAT',
        invoice_prefix: settings.invoice_prefix || 'INV',
        receipt_prefix: settings.receipt_prefix || 'RCP',
        auto_invoice_numbering: settings.auto_invoice_numbering ?? true,
        payment_terms_days: settings.payment_terms_days || 30,
      });
    }
  }, [settings]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({ 
      ...prev, 
      [name]: type === 'checkbox' ? checked : (type === 'number' ? Number(value) : value)
    }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleWorkingDaysChange = (day: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      working_days: checked 
        ? [...(prev.working_days || []), day]
        : (prev.working_days || []).filter(d => d !== day)
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const settingsData = {
      business_hours_start: formData.business_hours_start,
      business_hours_end: formData.business_hours_end,
      working_days: formData.working_days,
      appointment_duration: formData.appointment_duration,
      max_appointments_per_day: formData.max_appointments_per_day,
      tax_rate: formData.tax_rate,
      tax_name: formData.tax_name,
      invoice_prefix: formData.invoice_prefix,
      receipt_prefix: formData.receipt_prefix,
      auto_invoice_numbering: formData.auto_invoice_numbering,
      payment_terms_days: formData.payment_terms_days,
    };

    updateSettingsMutation.mutate(settingsData);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="text-lg text-gray-600">Loading business settings...</div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto">
      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Business Hours */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Clock className="mr-2 h-5 w-5" />
              Business Hours
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="business_hours_start">Opening Time</Label>
                <Input
                  id="business_hours_start"
                  name="business_hours_start"
                  type="time"
                  value={formData.business_hours_start || ''}
                  onChange={handleChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="business_hours_end">Closing Time</Label>
                <Input
                  id="business_hours_end"
                  name="business_hours_end"
                  type="time"
                  value={formData.business_hours_end || ''}
                  onChange={handleChange}
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label>Working Days</Label>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                {workingDaysOptions.map(day => (
                  <div key={day} className="flex items-center space-x-2">
                    <Checkbox
                      id={day}
                      checked={(formData.working_days || []).includes(day)}
                      onCheckedChange={(checked) => handleWorkingDaysChange(day, checked as boolean)}
                    />
                    <Label htmlFor={day} className="text-sm">{day}</Label>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Appointment Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="mr-2 h-5 w-5" />
              Appointment Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="appointment_duration">Default Duration (minutes)</Label>
                <Input
                  id="appointment_duration"
                  name="appointment_duration"
                  type="number"
                  min="15"
                  max="240"
                  value={formData.appointment_duration || ''}
                  onChange={handleChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="max_appointments_per_day">Max Appointments per Day</Label>
                <Input
                  id="max_appointments_per_day"
                  name="max_appointments_per_day"
                  type="number"
                  min="1"
                  max="200"
                  value={formData.max_appointments_per_day || ''}
                  onChange={handleChange}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Financial Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <DollarSign className="mr-2 h-5 w-5" />
              Financial Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="tax_rate">Tax Rate (%)</Label>
                <Input
                  id="tax_rate"
                  name="tax_rate"
                  type="number"
                  min="0"
                  max="100"
                  step="0.01"
                  value={formData.tax_rate || ''}
                  onChange={handleChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="tax_name">Tax Name</Label>
                <Input
                  id="tax_name"
                  name="tax_name"
                  value={formData.tax_name || ''}
                  onChange={handleChange}
                  placeholder="e.g., VAT, GST, Sales Tax"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="invoice_prefix">Invoice Prefix</Label>
                <Input
                  id="invoice_prefix"
                  name="invoice_prefix"
                  value={formData.invoice_prefix || ''}
                  onChange={handleChange}
                  placeholder="INV"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="receipt_prefix">Receipt Prefix</Label>
                <Input
                  id="receipt_prefix"
                  name="receipt_prefix"
                  value={formData.receipt_prefix || ''}
                  onChange={handleChange}
                  placeholder="RCP"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="payment_terms_days">Payment Terms (days)</Label>
                <Input
                  id="payment_terms_days"
                  name="payment_terms_days"
                  type="number"
                  min="0"
                  max="365"
                  value={formData.payment_terms_days || ''}
                  onChange={handleChange}
                />
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="auto_invoice_numbering"
                name="auto_invoice_numbering"
                checked={formData.auto_invoice_numbering || false}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, auto_invoice_numbering: checked as boolean }))}
              />
              <Label htmlFor="auto_invoice_numbering">Auto-generate invoice numbers</Label>
            </div>
          </CardContent>
        </Card>

        {/* Save Button */}
        <div className="flex justify-end space-x-4 pt-6 border-t">
          <Button
            type="submit"
            disabled={updateSettingsMutation.isPending}
            className="px-8 py-2"
          >
            {updateSettingsMutation.isPending ? 'Saving...' : 'Save Business Settings'}
          </Button>
        </div>
      </form>
    </div>
  );
};
