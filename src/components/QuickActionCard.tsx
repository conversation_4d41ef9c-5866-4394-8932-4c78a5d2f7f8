import React from 'react';
import { LucideIcon, ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';

interface QuickActionCardProps {
  title: string;
  description?: string;
  icon: LucideIcon;
  color: 'blue' | 'purple' | 'green' | 'orange' | 'red' | 'cyan';
  href?: string;
}

const colorClasses = {
  blue: 'from-blue-500 to-blue-600',
  purple: 'from-purple-500 to-purple-600',
  green: 'from-green-500 to-green-600',
  orange: 'from-orange-500 to-orange-600',
  red: 'from-red-500 to-red-600',
  cyan: 'from-cyan-500 to-cyan-600',
};

const lightColorClasses = {
  blue: 'bg-blue-50 text-blue-600 border-blue-200',
  purple: 'bg-purple-50 text-purple-600 border-purple-200',
  green: 'bg-green-50 text-green-600 border-green-200',
  orange: 'bg-orange-50 text-orange-600 border-orange-200',
  red: 'bg-red-50 text-red-600 border-red-200',
  cyan: 'bg-cyan-50 text-cyan-600 border-cyan-200',
};

export const QuickActionCard = ({ title, description, icon: Icon, color, href }: QuickActionCardProps) => {
  const CardContent = () => (
    <div className="flex flex-col h-full">
      <div className={`p-3 rounded-lg ${lightColorClasses[color]} w-fit mb-3 border`}>
        <Icon className="h-5 w-5" />
      </div>
      <h3 className="font-semibold text-gray-900 mb-1">{title}</h3>
      {description && <p className="text-sm text-gray-500 mb-3">{description}</p>}
      <div className="mt-auto flex items-center text-sm font-medium text-purple-600 pt-2">
        <span>Get started</span>
        <ArrowRight className="h-4 w-4 ml-1" />
      </div>
    </div>
  );

  return href ? (
    <Link 
      to={href}
      className="block bg-white p-5 rounded-xl border border-gray-100 shadow-sm hover:shadow-md transition-all duration-200 hover:border-purple-100 h-full"
    >
      <CardContent />
    </Link>
  ) : (
    <div className="bg-white p-5 rounded-xl border border-gray-100 shadow-sm h-full">
      <CardContent />
    </div>
  );
};
