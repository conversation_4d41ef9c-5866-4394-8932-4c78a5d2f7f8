
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { useSupabaseData } from '@/hooks/useSupabaseData';
import { useToast } from '@/hooks/use-toast';

interface DoctorFormProps {
  doctor?: any;
  onClose: () => void;
}

export const DoctorForm = ({ doctor, onClose }: DoctorFormProps) => {
  const { createDoctor } = useSupabaseData();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [formData, setFormData] = useState({
    name: doctor?.name || '',
    specialty: doctor?.specialty || '',
    qualification: doctor?.qualification || '',
    experience: doctor?.experience || '',
    email: doctor?.email || '',
    phone_number: doctor?.phone_number || '',
    bio: doctor?.bio || '',
    status: doctor?.status || 'active',
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name, value) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const doctorData = {
        ...formData,
        experience: formData.experience ? parseInt(formData.experience) : null,
      };

      await createDoctor.mutateAsync(doctorData);
      onClose();
    } catch (error) {
      console.error('Error saving doctor:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to save doctor",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Sample data
  const specialties = [
    'Cardiology',
    'Neurology',
    'Orthopedics',
    'Pediatrics',
    'General Medicine',
    'Dermatology',
    'Ophthalmology',
    'Psychiatry',
    'Gynecology',
    'Urology'
  ];

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          {doctor ? 'Edit Doctor Profile' : 'Add New Doctor'}
        </h2>
        <p className="text-gray-500">
          {doctor ? 'Update the doctor\'s information and settings' : 'Enter the doctor\'s information to add them to the system'}
        </p>
      </div>

      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="basic">Basic Info</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>
        
        {/* Basic Information Tab */}
        <TabsContent value="basic" className="space-y-6 pt-4">
          {/* Profile Picture */}
          <div className="flex flex-col items-center sm:flex-row sm:items-start gap-6">
            <div className="flex flex-col items-center">
              <Avatar className="h-24 w-24">
                <AvatarImage src="" alt={formData.name} />
                <AvatarFallback>{formData.name ? formData.name.split(' ').map(n => n[0]).join('') : 'DR'}</AvatarFallback>
              </Avatar>
              <Button variant="outline" size="sm" className="mt-4" type="button">
                Change Photo
              </Button>
            </div>
            
            <div className="flex-1 space-y-4">
              {/* Name */}
              <div className="space-y-2">
                <Label htmlFor="name">Full Name</Label>
                <Input 
                  id="name" 
                  name="name" 
                  value={formData.name} 
                  onChange={handleChange} 
                  placeholder="e.g., Dr. John Smith"
                  required
                />
              </div>
              
              {/* Specialty and Qualification */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="specialty">Specialty</Label>
                  <div className="space-y-2">
                    <Select
                      value={formData.specialty}
                      onValueChange={(value) => {
                        if (value === 'custom') {
                          // Clear to allow manual input
                          handleSelectChange('specialty', '');
                        } else {
                          handleSelectChange('specialty', value);
                        }
                      }}
                      required
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select specialty" />
                      </SelectTrigger>
                      <SelectContent>
                        {specialties.map(specialty => (
                          <SelectItem key={specialty} value={specialty}>{specialty}</SelectItem>
                        ))}
                        <SelectItem value="custom">Other (enter manually below)</SelectItem>
                      </SelectContent>
                    </Select>

                    {/* Manual input field */}
                    <Input
                      placeholder="Or enter custom specialty"
                      value={formData.specialty === 'custom' ? '' : formData.specialty}
                      onChange={(e) => handleSelectChange('specialty', e.target.value)}
                      required
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="qualification">Qualification</Label>
                  <Input 
                    id="qualification" 
                    name="qualification" 
                    value={formData.qualification} 
                    onChange={handleChange} 
                    placeholder="e.g., MD, FACC"
                  />
                </div>
              </div>
              
              {/* Experience */}
              <div className="space-y-2">
                <Label htmlFor="experience">Experience (years)</Label>
                <Input 
                  id="experience" 
                  name="experience" 
                  type="number"
                  value={formData.experience} 
                  onChange={handleChange} 
                  placeholder="e.g., 10"
                />
              </div>
            </div>
          </div>
          
          <Separator />
          
          {/* Contact Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Contact Information</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input 
                  id="email" 
                  name="email" 
                  type="email" 
                  value={formData.email} 
                  onChange={handleChange} 
                  placeholder="<EMAIL>"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone_number">Phone Number</Label>
                <Input 
                  id="phone_number" 
                  name="phone_number" 
                  value={formData.phone_number} 
                  onChange={handleChange} 
                  placeholder="e.g., (*************"
                  required
                />
              </div>
            </div>
          </div>
          
          <Separator />
          
          {/* Bio */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Professional Bio</h3>
            <div className="space-y-2">
              <Label htmlFor="bio">Biography</Label>
              <Textarea 
                id="bio" 
                name="bio" 
                value={formData.bio} 
                onChange={handleChange} 
                placeholder="Enter doctor's professional biography and specializations..."
                className="min-h-[120px]"
              />
            </div>
          </div>
        </TabsContent>
        
        {/* Settings Tab */}
        <TabsContent value="settings" className="space-y-6 pt-4">
          {/* Status */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Account Status</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900">Active Status</p>
                  <p className="text-sm text-gray-500">Set whether this doctor is currently active in the hospital</p>
                </div>
                <div className="flex items-center gap-2">
                  <Switch 
                    checked={formData.status === 'active'} 
                    onCheckedChange={(checked) => handleSelectChange('status', checked ? 'active' : 'inactive')} 
                  />
                  <Badge className={formData.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                    {formData.status === 'active' ? 'Active' : 'Inactive'}
                  </Badge>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900">Online Appointments</p>
                  <p className="text-sm text-gray-500">Allow patients to book online appointments with this doctor</p>
                </div>
                <Switch defaultChecked />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900">Email Notifications</p>
                  <p className="text-sm text-gray-500">Send email notifications for new appointments</p>
                </div>
                <Switch defaultChecked />
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      {/* Form Actions */}
      <div className="flex justify-end space-x-4 pt-4">
        <Button type="button" variant="outline" onClick={onClose}>
          Cancel
        </Button>
        <Button type="submit" className="bg-purple-600 hover:bg-purple-700 text-white" disabled={isSubmitting}>
          {isSubmitting ? 'Saving...' : (doctor ? 'Update Doctor' : 'Add Doctor')}
        </Button>
      </div>
    </form>
  );
};
