
import React from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { 
  LayoutDashboard, 
  Users, 
  Calendar, 
  Stethoscope,
  Building2,
  FileText,
  TestTube,
  Pill,
  DollarSign,
  Settings,
  BarChart2,
  ChevronRight,
  X,
  Activity
} from 'lucide-react';
import { cn } from '../lib/utils';

const navItems = [
  // Main Workflow
  { to: '/dashboard', icon: LayoutDashboard, label: 'Dashboard', category: 'main' },
  { to: '/reception', icon: Users, label: 'Reception', category: 'main' },
  { to: '/consultation-department', icon: Activity, label: 'Consultation', category: 'main' },
  { to: '/laboratory-department', icon: TestTube, label: 'Laboratory', category: 'main' },
  { to: '/pharmacy-department', icon: Pill, label: 'Pharmacy', category: 'main' },
  { to: '/medical-records', icon: FileText, label: 'Medical Records', category: 'main' },
  { to: '/workflow', icon: Activity, label: 'Workflow Dashboard', category: 'main' },

  // Management
  { to: '/appointments', icon: Calendar, label: 'Appointments', category: 'clinical' },
  { to: '/doctors', icon: Stethoscope, label: 'Doctors', category: 'clinical' },
  { to: '/departments', icon: Building2, label: 'Departments', category: 'clinical' },
  
  // Administration
  { to: '/billing-accounting', icon: DollarSign, label: 'Billing', category: 'admin' },
  { to: '/reports', icon: BarChart2, label: 'Reports', category: 'admin' },
  { to: '/workflow-reports', icon: BarChart2, label: 'Workflow Reports', category: 'admin' },
  { to: '/settings', icon: Settings, label: 'Settings', category: 'admin' },
];

type NavCategory = 'main' | 'clinical' | 'admin';

interface SidebarProps {
  onCloseMobile?: () => void;
}

const categoryLabels: Record<NavCategory, string> = {
  main: 'Workflow Departments',
  clinical: 'Management',
  admin: 'Administration'
};

export const Sidebar = ({ onCloseMobile }: SidebarProps) => {
  const location = useLocation();

  // Group nav items by category
  const groupedNavItems = navItems.reduce<Record<NavCategory, typeof navItems>>(
    (acc, item) => {
      const category = item.category as NavCategory;
      if (!acc[category]) acc[category] = [];
      acc[category].push(item);
      return acc;
    },
    { main: [], clinical: [], admin: [] }
  );

  return (
    <div className="w-64 bg-gradient-to-b from-purple-700 to-purple-900 text-white flex flex-col h-screen shadow-xl">
      {/* Sidebar Header */}
      <div className="p-5 border-b border-purple-600/30">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold">Menu</h2>
          
          {/* Mobile Close Button */}
          {onCloseMobile && (
            <button 
              onClick={onCloseMobile}
              className="lg:hidden p-2 rounded-lg text-purple-200 hover:text-white hover:bg-purple-600/50 transition-colors"
              aria-label="Close sidebar"
            >
              <X size={24} />
            </button>
          )}
        </div>
      </div>
      
      {/* Navigation */}
      <nav className="flex-1 py-6 px-3 overflow-y-auto scrollbar-thin scrollbar-thumb-purple-500/20 scrollbar-track-transparent">
        <div className="space-y-6">
          {Object.entries(groupedNavItems).map(([category, items]) => (
            <div key={category} className="space-y-1">
              <h2 className="text-xs font-semibold text-purple-300 uppercase tracking-wider px-4 mb-2">
                {categoryLabels[category as NavCategory]}
              </h2>
              
              {items.map(({ to, icon: Icon, label }) => {
                const isActive = location.pathname === to || 
                  (to === '/' && location.pathname === '/');
                  
                return (
                  <NavLink
                    key={to}
                    to={to}
                    className={({ isActive }) => cn(
                      "flex items-center justify-between px-4 py-2.5 rounded-lg transition-all duration-200 group",
                      isActive 
                        ? "bg-white/15 text-white" 
                        : "text-purple-200 hover:bg-white/10 hover:text-white"
                    )}
                  >
                    <div className="flex items-center space-x-3">
                      <Icon size={18} className={cn(
                        "transition-colors",
                        isActive ? "text-white" : "text-purple-300 group-hover:text-white"
                      )} />
                      <span className="font-medium text-sm">{label}</span>
                    </div>
                    
                    {isActive && (
                      <ChevronRight size={16} className="text-purple-200" />
                    )}
                  </NavLink>
                );
              })}
            </div>
          ))}
        </div>
      </nav>
      
      {/* User Profile Section */}
      {/* Removed user profile section */}
    </div>
  );
};
