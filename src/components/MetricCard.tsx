
import React from 'react';
import { LucideIcon, TrendingUp, TrendingDown } from 'lucide-react';

interface MetricCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  trend?: string;
  icon: LucideIcon;
  color?: 'blue' | 'purple' | 'green' | 'yellow' | 'red' | 'cyan';
}

const colorClasses = {
  blue: 'from-blue-500 to-blue-600',
  purple: 'from-purple-500 to-purple-600',
  green: 'from-green-500 to-green-600',
  yellow: 'from-yellow-500 to-yellow-600',
  red: 'from-red-500 to-red-600',
  cyan: 'from-cyan-500 to-cyan-600',
};

const lightColorClasses = {
  blue: 'bg-blue-50 text-blue-600',
  purple: 'bg-purple-50 text-purple-600',
  green: 'bg-green-50 text-green-600',
  yellow: 'bg-yellow-50 text-yellow-600',
  red: 'bg-red-50 text-red-600',
  cyan: 'bg-cyan-50 text-cyan-600',
};

export const MetricCard = ({ 
  title, 
  value, 
  subtitle, 
  trend, 
  icon: Icon, 
  color = 'blue' 
}: MetricCardProps) => {
  const isTrendUp = trend?.includes('↑');
  const isTrendDown = trend?.includes('↓');
  const trendColor = isTrendUp ? 'text-green-600' : isTrendDown ? 'text-red-600' : 'text-gray-600';
  
  return (
    <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200 hover:border-purple-100">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600 mb-2">{title}</p>
          <p className="text-3xl font-bold text-gray-900 mb-1">{value}</p>
          {subtitle && (
            <p className="text-sm text-gray-500">{subtitle}</p>
          )}
          {trend && (
            <div className="flex items-center gap-1 mt-2">
              {isTrendUp && <TrendingUp className="h-3.5 w-3.5 text-green-600" />}
              {isTrendDown && <TrendingDown className="h-3.5 w-3.5 text-red-600" />}
              <p className={`text-sm font-medium ${trendColor}`}>{trend}</p>
            </div>
          )}
        </div>
        <div className={`p-3 rounded-lg bg-gradient-to-br ${colorClasses[color]} shadow-sm`}>
          <Icon size={24} className="text-white" />
        </div>
      </div>
    </div>
  );
};
