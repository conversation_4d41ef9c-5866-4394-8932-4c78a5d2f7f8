import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Pill, Clock, Calendar, User, Info } from 'lucide-react';

interface Medicine {
  name: string;
  dosage: string;
  frequency?: string;
  duration?: string;
  instructions?: string;
  consultationDate?: string;
  doctor?: string;
}

interface PrescribedMedicinesListProps {
  medicines: Medicine[];
  title?: string;
  showEmpty?: boolean;
}

export const PrescribedMedicinesList = ({ 
  medicines, 
  title = "Prescribed Medicines",
  showEmpty = true 
}: PrescribedMedicinesListProps) => {
  if (medicines.length === 0 && !showEmpty) {
    return null;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Pill className="h-5 w-5 text-blue-600" />
          {title} ({medicines.length})
        </CardTitle>
      </CardHeader>
      <CardContent>
        {medicines.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Pill className="mx-auto h-12 w-12 text-gray-300 mb-4" />
            <p>No prescribed medicines found</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {medicines.map((medicine, index) => (
              <div 
                key={index} 
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow bg-white"
              >
                {/* Medicine Header */}
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900 text-sm sm:text-base line-clamp-2">
                      {medicine.name}
                    </h4>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant="secondary" className="text-xs">
                        {medicine.dosage}
                      </Badge>
                    </div>
                  </div>
                  <div className="ml-2 flex-shrink-0">
                    <div className="w-10 h-10 bg-blue-50 rounded-full flex items-center justify-center">
                      <Pill className="h-5 w-5 text-blue-600" />
                    </div>
                  </div>
                </div>

                {/* Medicine Details */}
                <div className="space-y-2">
                  {medicine.frequency && (
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Clock className="h-3 w-3 flex-shrink-0" />
                      <span className="truncate">{medicine.frequency}</span>
                    </div>
                  )}
                  
                  {medicine.duration && (
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Calendar className="h-3 w-3 flex-shrink-0" />
                      <span className="truncate">{medicine.duration}</span>
                    </div>
                  )}

                  {medicine.instructions && (
                    <div className="flex items-start gap-2 text-sm text-gray-600">
                      <Info className="h-3 w-3 flex-shrink-0 mt-0.5" />
                      <span className="text-xs leading-relaxed line-clamp-2">
                        {medicine.instructions}
                      </span>
                    </div>
                  )}
                </div>

                {/* Prescription Info */}
                {(medicine.doctor || medicine.consultationDate) && (
                  <div className="mt-3 pt-3 border-t border-gray-100">
                    <div className="flex flex-col gap-1 text-xs text-gray-500">
                      {medicine.doctor && (
                        <div className="flex items-center gap-1">
                          <User className="h-3 w-3 flex-shrink-0" />
                          <span className="truncate">Dr. {medicine.doctor}</span>
                        </div>
                      )}
                      {medicine.consultationDate && (
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3 flex-shrink-0" />
                          <span>
                            {new Date(medicine.consultationDate).toLocaleDateString()}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
