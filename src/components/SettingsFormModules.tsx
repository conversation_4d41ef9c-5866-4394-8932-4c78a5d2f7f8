import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { Pill, TestTube, Stethoscope, Settings as SettingsIcon } from 'lucide-react';
import { useSupabaseData } from '@/hooks/useSupabaseData';
import { Settings, SettingsFormData, defaultSettings } from '@/types/settings';

interface SettingsFormModulesProps {}

export const SettingsFormModules = ({}: SettingsFormModulesProps) => {
  const { useSettings, updateSettings } = useSupabaseData();
  const { data: settings, isLoading } = useSettings();
  const updateSettingsMutation = updateSettings();

  const [formData, setFormData] = useState<Partial<SettingsFormData>>({
    ...defaultSettings,
    // Pharmacy
    pharmacy_enabled: true,
    auto_deduct_inventory: true,
    require_prescription: false,
    pharmacy_markup_percentage: 0,
    // Laboratory
    lab_enabled: true,
    auto_generate_lab_numbers: true,
    lab_result_approval_required: true,
    lab_report_template: '',
    // Consultation
    consultation_enabled: true,
    require_appointment: false,
    allow_walk_ins: true,
    consultation_fee: 0,
  });

  // Load settings data when available
  useEffect(() => {
    if (settings) {
      setFormData({
        // Pharmacy
        pharmacy_enabled: settings.pharmacy_enabled ?? true,
        auto_deduct_inventory: settings.auto_deduct_inventory ?? true,
        require_prescription: settings.require_prescription ?? false,
        pharmacy_markup_percentage: settings.pharmacy_markup_percentage || 0,
        // Laboratory
        lab_enabled: settings.lab_enabled ?? true,
        auto_generate_lab_numbers: settings.auto_generate_lab_numbers ?? true,
        lab_result_approval_required: settings.lab_result_approval_required ?? true,
        lab_report_template: settings.lab_report_template || '',
        // Consultation
        consultation_enabled: settings.consultation_enabled ?? true,
        require_appointment: settings.require_appointment ?? false,
        allow_walk_ins: settings.allow_walk_ins ?? true,
        consultation_fee: settings.consultation_fee || 0,
      });
    }
  }, [settings]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    const checked = (e.target as HTMLInputElement).checked;
    setFormData(prev => ({ 
      ...prev, 
      [name]: type === 'checkbox' ? checked : (type === 'number' ? Number(value) : value)
    }));
  };

  const handleCheckboxChange = (name: string, checked: boolean) => {
    setFormData(prev => ({ ...prev, [name]: checked }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const settingsData = {
      // Pharmacy
      pharmacy_enabled: formData.pharmacy_enabled,
      auto_deduct_inventory: formData.auto_deduct_inventory,
      require_prescription: formData.require_prescription,
      pharmacy_markup_percentage: formData.pharmacy_markup_percentage,
      // Laboratory
      lab_enabled: formData.lab_enabled,
      auto_generate_lab_numbers: formData.auto_generate_lab_numbers,
      lab_result_approval_required: formData.lab_result_approval_required,
      lab_report_template: formData.lab_report_template,
      // Consultation
      consultation_enabled: formData.consultation_enabled,
      require_appointment: formData.require_appointment,
      allow_walk_ins: formData.allow_walk_ins,
      consultation_fee: formData.consultation_fee,
    };

    updateSettingsMutation.mutate(settingsData);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="text-lg text-gray-600">Loading module settings...</div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto">
      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Pharmacy Module */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Pill className="mr-2 h-5 w-5" />
              Pharmacy Module
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="pharmacy_enabled"
                checked={formData.pharmacy_enabled || false}
                onCheckedChange={(checked) => handleCheckboxChange('pharmacy_enabled', checked as boolean)}
              />
              <Label htmlFor="pharmacy_enabled" className="font-medium">Enable Pharmacy Module</Label>
            </div>
            
            {formData.pharmacy_enabled && (
              <div className="space-y-4 ml-6 border-l-2 border-gray-200 pl-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="auto_deduct_inventory"
                    checked={formData.auto_deduct_inventory || false}
                    onCheckedChange={(checked) => handleCheckboxChange('auto_deduct_inventory', checked as boolean)}
                  />
                  <Label htmlFor="auto_deduct_inventory">Auto-deduct inventory on sales</Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="require_prescription"
                    checked={formData.require_prescription || false}
                    onCheckedChange={(checked) => handleCheckboxChange('require_prescription', checked as boolean)}
                  />
                  <Label htmlFor="require_prescription">Require prescription for medicines</Label>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="pharmacy_markup_percentage">Default Markup Percentage (%)</Label>
                  <Input
                    id="pharmacy_markup_percentage"
                    name="pharmacy_markup_percentage"
                    type="number"
                    min="0"
                    max="1000"
                    step="0.01"
                    value={formData.pharmacy_markup_percentage || ''}
                    onChange={handleChange}
                    placeholder="0.00"
                  />
                  <p className="text-sm text-gray-500">
                    Default markup percentage applied to medicine costs
                  </p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Laboratory Module */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <TestTube className="mr-2 h-5 w-5" />
              Laboratory Module
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="lab_enabled"
                checked={formData.lab_enabled || false}
                onCheckedChange={(checked) => handleCheckboxChange('lab_enabled', checked as boolean)}
              />
              <Label htmlFor="lab_enabled" className="font-medium">Enable Laboratory Module</Label>
            </div>
            
            {formData.lab_enabled && (
              <div className="space-y-4 ml-6 border-l-2 border-gray-200 pl-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="auto_generate_lab_numbers"
                    checked={formData.auto_generate_lab_numbers || false}
                    onCheckedChange={(checked) => handleCheckboxChange('auto_generate_lab_numbers', checked as boolean)}
                  />
                  <Label htmlFor="auto_generate_lab_numbers">Auto-generate lab test numbers</Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="lab_result_approval_required"
                    checked={formData.lab_result_approval_required || false}
                    onCheckedChange={(checked) => handleCheckboxChange('lab_result_approval_required', checked as boolean)}
                  />
                  <Label htmlFor="lab_result_approval_required">Require approval for lab results</Label>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="lab_report_template">Lab Report Template</Label>
                  <Textarea
                    id="lab_report_template"
                    name="lab_report_template"
                    value={formData.lab_report_template || ''}
                    onChange={handleChange}
                    placeholder="Enter default lab report template..."
                    rows={4}
                  />
                  <p className="text-sm text-gray-500">
                    Default template for lab reports (supports variables like {'{patient_name}'}, {'{test_date}'}, etc.)
                  </p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Consultation Module */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Stethoscope className="mr-2 h-5 w-5" />
              Consultation Module
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="consultation_enabled"
                checked={formData.consultation_enabled || false}
                onCheckedChange={(checked) => handleCheckboxChange('consultation_enabled', checked as boolean)}
              />
              <Label htmlFor="consultation_enabled" className="font-medium">Enable Consultation Module</Label>
            </div>
            
            {formData.consultation_enabled && (
              <div className="space-y-4 ml-6 border-l-2 border-gray-200 pl-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="require_appointment"
                    checked={formData.require_appointment || false}
                    onCheckedChange={(checked) => handleCheckboxChange('require_appointment', checked as boolean)}
                  />
                  <Label htmlFor="require_appointment">Require appointment for consultations</Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="allow_walk_ins"
                    checked={formData.allow_walk_ins || false}
                    onCheckedChange={(checked) => handleCheckboxChange('allow_walk_ins', checked as boolean)}
                  />
                  <Label htmlFor="allow_walk_ins">Allow walk-in patients</Label>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="consultation_fee">Default Consultation Fee</Label>
                  <Input
                    id="consultation_fee"
                    name="consultation_fee"
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.consultation_fee || ''}
                    onChange={handleChange}
                    placeholder="0.00"
                  />
                  <p className="text-sm text-gray-500">
                    Default fee charged for consultations (can be overridden per consultation)
                  </p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Save Button */}
        <div className="flex justify-end space-x-4 pt-6 border-t">
          <Button
            type="submit"
            disabled={updateSettingsMutation.isPending}
            className="px-8 py-2"
          >
            {updateSettingsMutation.isPending ? 'Saving...' : 'Save Module Settings'}
          </Button>
        </div>
      </form>
    </div>
  );
};
