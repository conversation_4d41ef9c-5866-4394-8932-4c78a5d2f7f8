
import React from 'react';
import { <PERSON>alog, DialogContent, Di<PERSON>Header, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { User, Calendar, Phone, Mail, Heart, Shield, FileText, AlertCircle } from 'lucide-react';
import { PrescribedMedicinesList } from './PrescribedMedicinesList';
import { useCurrency } from '@/contexts/CurrencyContext';

interface PatientDetailsDialogProps {
  patient: any;
  consultationRecords?: any[];
  laboratoryRecords?: any[];
  isOpen: boolean;
  onClose: () => void;
}

export const PatientDetailsDialog = ({ patient, consultationRecords = [], laboratoryRecords = [], isOpen, onClose }: PatientDetailsDialogProps) => {
  const { formatCurrency } = useCurrency();

  // Early return if patient is null/undefined
  if (!patient) return null;

  // Get consultation and laboratory records for this patient (with safety checks)
  const patientConsultation = Array.isArray(consultationRecords)
    ? consultationRecords.find(record => record?.patient_id === patient?.id)
    : null;
  const patientLaboratory = Array.isArray(laboratoryRecords)
    ? laboratoryRecords.find(record => record?.patient_id === patient?.id)
    : null;

  const calculateAge = (dateOfBirth: string) => {
    if (!dateOfBirth) return 'N/A';
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  // Get patient's consultation records
  const patientConsultations = Array.isArray(consultationRecords)
    ? consultationRecords.filter(record => record?.patient_id === patient?.id)
    : [];

  // Extract prescribed medicines from consultation records
  const getAllPrescribedMedicines = () => {
    const medicines: any[] = [];

    patientConsultations.forEach(consultation => {
      // Check if prescribed_medicines field exists
      if (consultation.prescribed_medicines && Array.isArray(consultation.prescribed_medicines)) {
        medicines.push(...consultation.prescribed_medicines.map((med: any) => ({
          ...med,
          consultationDate: consultation.consultation_date,
          doctor: consultation.doctor_name
        })));
      }

      // Also check notes for prescribed medicines (fallback)
      if (consultation.notes && consultation.notes.includes('--- PRESCRIBED MEDICINES ---')) {
        const medicinesSection = consultation.notes.split('--- PRESCRIBED MEDICINES ---')[1];
        if (medicinesSection) {
          const medicineLines = medicinesSection.trim().split('\n').filter((line: string) => line.trim() && line.match(/^\d+\./));
          medicineLines.forEach((line: string) => {
            const match = line.match(/^\d+\.\s*(.+?)\s*-\s*(.+)/);
            if (match) {
              medicines.push({
                name: match[1].trim(),
                dosage: match[2].trim(),
                consultationDate: consultation.consultation_date,
                doctor: consultation.doctor_name
              });
            }
          });
        }
      }
    });

    return medicines;
  };

  const prescribedMedicines = getAllPrescribedMedicines();

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[95vw] max-w-[900px] max-h-[90vh] overflow-y-auto mx-2 sm:mx-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <Avatar className="h-12 w-12">
              <AvatarImage src="" alt={patient?.patient_name || 'Patient'} />
              <AvatarFallback>
                {patient?.patient_name?.split(' ').map((n: string) => n[0]).join('') || 'P'}
              </AvatarFallback>
            </Avatar>
            <div>
              <h2 className="text-2xl font-bold">{patient?.patient_name || 'Unknown Patient'}</h2>
              <p className="text-sm text-gray-500">Patient ID: {patient?.id || 'N/A'}</p>
            </div>
          </DialogTitle>
          <DialogDescription>
            View detailed patient information including personal details, medical history, and contact information
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Basic Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Age:</span>
                  <span className="font-medium">{calculateAge(patient?.date_of_birth)} years</span>
                </div>
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Gender:</span>
                  <span className="font-medium">{patient?.gender || 'Not specified'}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Date of Birth:</span>
                  <span className="font-medium">
                    {patient?.date_of_birth ? new Date(patient.date_of_birth).toLocaleDateString() : 'Not specified'}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Heart className="h-4 w-4 text-red-500" />
                  <span className="text-sm text-gray-600">Blood Type:</span>
                  {patient.blood_type ? (
                    <Badge variant="outline">{patient.blood_type}</Badge>
                  ) : (
                    <span className="text-gray-400">Not specified</span>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Phone className="h-5 w-5" />
                Contact Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Email:</span>
                  <span className="font-medium">{patient?.email && patient.email.trim() ? patient.email : 'Not specified'}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Phone:</span>
                  <span className="font-medium">{patient?.phone_number || 'Not specified'}</span>
                </div>
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-4 w-4 text-orange-500" />
                  <span className="text-sm text-gray-600">Emergency Contact:</span>
                  <span className="font-medium">{patient.emergency_contact || 'Not specified'}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Registration Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Registration Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-blue-500" />
                  <span className="text-sm text-gray-600">Registration Date:</span>
                  <span className="font-medium">
                    {patient?.registration_date ? new Date(patient.registration_date).toLocaleDateString() : 'Not specified'}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-purple-500" />
                  <span className="text-sm text-gray-600">Patient ID:</span>
                  <span className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">
                    {patient?.id?.slice(0, 8)}...
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">Workflow Status:</span>
                  <Badge className={`${
                    patient?.workflow_status === 'registered' ? 'bg-green-100 text-green-800' :
                    patient?.workflow_status === 'sent_to_consultation' ? 'bg-orange-100 text-orange-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {patient?.workflow_status === 'registered' && '✓ Ready for Consultation'}
                    {patient?.workflow_status === 'sent_to_consultation' && '⏳ In Consultation'}
                    {!patient?.workflow_status && '📝 Registered'}
                  </Badge>
                </div>
                {patient?.department_name && (
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-600">Department:</span>
                    <Badge
                      className="text-white"
                      style={{ backgroundColor: patient.department_color || '#3B82F6' }}
                    >
                      {patient.department_name}
                    </Badge>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Insurance Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Insurance Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                <Shield className="h-4 w-4 text-green-500" />
                <span className="text-sm text-gray-600">Provider:</span>
                {patient.insurance ? (
                  <Badge className="bg-green-100 text-green-800">{patient.insurance}</Badge>
                ) : (
                  <span className="text-gray-400">No insurance specified</span>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Fee Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                💰 Registration Fee Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="p-4 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="text-sm">
                      <span className="font-medium text-green-700">Fee Amount:</span>
                      <span className="ml-2 text-green-900 font-semibold">
                        {formatCurrency(patient?.fee_amount || 0)}
                      </span>
                    </div>
                    <div className="text-sm">
                      <span className="font-medium text-green-700">Payment Status:</span>
                      <span className={`ml-2 font-bold ${patient?.fee_paid ? 'text-green-700' : 'text-red-700'}`}>
                        {patient?.fee_paid ? '✅ PAID' : '❌ NOT PAID'}
                      </span>
                    </div>
                  </div>
                  {patient?.fee_notes && (
                    <div className="text-sm">
                      <span className="font-medium text-green-700">Payment Notes:</span>
                      <p className="ml-2 text-green-900 bg-white p-2 rounded border">
                        {patient.fee_notes}
                      </p>
                    </div>
                  )}
                </div>
                {!patient?.fee_paid && (patient?.fee_amount || 0) > 0 && (
                  <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded">
                    <p className="text-xs text-yellow-700 font-medium">
                      ⚠️ Outstanding registration fee
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Consultation Fee Information */}
          {patientConsultation && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  💰 Consultation Fee Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border border-yellow-200">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <div className="text-sm">
                        <span className="font-medium text-yellow-700">Fee Amount:</span>
                        <span className="ml-2 text-yellow-900 font-semibold">
                          {formatCurrency(patientConsultation.fee_amount || 0)}
                        </span>
                      </div>
                      <div className="text-sm">
                        <span className="font-medium text-yellow-700">Payment Status:</span>
                        <span className={`ml-2 font-bold ${patientConsultation.fee_paid ? 'text-green-700' : 'text-red-700'}`}>
                          {patientConsultation.fee_paid ? '✅ PAID' : '❌ NOT PAID'}
                        </span>
                      </div>
                    </div>
                    {patientConsultation.fee_notes && (
                      <div className="text-sm">
                        <span className="font-medium text-yellow-700">Payment Notes:</span>
                        <p className="ml-2 text-yellow-900 bg-white p-2 rounded border">
                          {patientConsultation.fee_notes}
                        </p>
                      </div>
                    )}
                  </div>
                  {!patientConsultation.fee_paid && patientConsultation.fee_amount > 0 && (
                    <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded">
                      <p className="text-xs text-yellow-700 font-medium">
                        ⚠️ Outstanding consultation fee
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Laboratory Fee Information */}
          {patientLaboratory && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  💰 Laboratory Fee Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <div className="text-sm">
                        <span className="font-medium text-blue-700">Fee Amount:</span>
                        <span className="ml-2 text-blue-900 font-semibold">
                          {formatCurrency(patientLaboratory.fee_amount || 0)}
                        </span>
                      </div>
                      <div className="text-sm">
                        <span className="font-medium text-blue-700">Payment Status:</span>
                        <span className={`ml-2 font-bold ${patientLaboratory.fee_paid ? 'text-green-700' : 'text-red-700'}`}>
                          {patientLaboratory.fee_paid ? '✅ PAID' : '❌ NOT PAID'}
                        </span>
                      </div>
                    </div>
                    {patientLaboratory.fee_notes && (
                      <div className="text-sm">
                        <span className="font-medium text-blue-700">Payment Notes:</span>
                        <p className="ml-2 text-blue-900 bg-white p-2 rounded border">
                          {patientLaboratory.fee_notes}
                        </p>
                      </div>
                    )}
                  </div>
                  {!patientLaboratory.fee_paid && patientLaboratory.fee_amount > 0 && (
                    <div className="mt-3 p-2 bg-blue-50 border border-blue-200 rounded">
                      <p className="text-xs text-blue-700 font-medium">
                        ⚠️ Outstanding laboratory fee
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Notes */}
          {patient?.notes && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Notes
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 whitespace-pre-wrap">{patient.notes}</p>
              </CardContent>
            </Card>
          )}

          {/* Prescribed Medicines */}
          <PrescribedMedicinesList medicines={prescribedMedicines} />

          {/* Registration Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Registration Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <span className="text-sm text-gray-600">Registration Date:</span>
                  <p className="font-medium">
                    {patient.registration_date ? new Date(patient.registration_date).toLocaleDateString() : 'Not specified'}
                  </p>
                </div>
                <div>
                  <span className="text-sm text-gray-600">Created:</span>
                  <p className="font-medium">
                    {new Date(patient.created_at).toLocaleDateString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
};
