import React, { useState, forwardRef, useImperativeHandle } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { format } from 'date-fns';
import { Calendar as CalendarIcon, Clock, User, Search } from 'lucide-react';
import { cn } from '@/lib/utils';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useSupabaseData } from '@/hooks/useSupabaseData';
import { useWorkflowData } from '@/hooks/useWorkflowData';

interface AppointmentFormProps {
  onClose: () => void;
  onSuccess?: () => void;
  existingAppointment?: any;
}

export interface AppointmentFormRef {
  submitForm: () => void;
  isPending: boolean;
}

export const AppointmentForm = forwardRef<AppointmentFormRef, AppointmentFormProps>(({ onClose, onSuccess, existingAppointment }, ref) => {
  const { createAppointment, useDoctors, useDepartments, usePatients } = useSupabaseData();
  const { usePatientWorkflows } = useWorkflowData();

  // Fetch data from database with error handling
  let doctors = [];
  let departments = [];
  let patients = [];
  let workflows = [];
  let doctorsLoading = false;
  let departmentsLoading = false;
  let patientsLoading = false;

  try {
    const doctorsQuery = useDoctors();
    doctors = doctorsQuery.data || [];
    doctorsLoading = doctorsQuery.isLoading;
  } catch (error) {
    console.warn('Error loading doctors:', error);
    doctors = [];
    doctorsLoading = false;
  }

  try {
    const departmentsQuery = useDepartments();
    departments = departmentsQuery.data || [];
    departmentsLoading = departmentsQuery.isLoading;
  } catch (error) {
    console.warn('Error loading departments:', error);
    departments = [];
    departmentsLoading = false;
  }

  try {
    const patientsQuery = usePatients();
    patients = patientsQuery.data || [];
    patientsLoading = patientsQuery.isLoading;
  } catch (error) {
    console.warn('Error loading patients:', error);
    patients = [];
    patientsLoading = false;
  }

  try {
    const workflowsQuery = usePatientWorkflows();
    workflows = workflowsQuery.data || [];
  } catch (error) {
    console.warn('Error loading workflows:', error);
    workflows = [];
  }
  const [date, setDate] = useState<Date | undefined>(existingAppointment?.date ? new Date(existingAppointment.date) : undefined);
  const [formData, setFormData] = useState({
    patientId: existingAppointment?.patientId || '',
    patientName: existingAppointment?.patientName || '',
    doctorId: existingAppointment?.doctorId || '',
    doctorName: existingAppointment?.doctorName || '',
    doctorSpecialty: existingAppointment?.doctorSpecialty || '',
    department: existingAppointment?.department || '',
    departmentName: existingAppointment?.departmentName || '',
    time: existingAppointment?.time || '',
    type: existingAppointment?.type || 'consultation',
    status: existingAppointment?.status || 'pending',
    notes: existingAppointment?.notes || '',
  });

  // Enhanced patient selection state
  const [patientSelectionMode, setPatientSelectionMode] = useState<'existing' | 'new'>('existing');
  const [selectedPatientId, setSelectedPatientId] = useState('');

  // Get patients with workflow information
  const patientsWithWorkflow = patients.map(patient => {
    const workflow = workflows.find(w => w.patient_id === patient.id);
    return {
      ...patient,
      workflowStatus: workflow?.current_department || 'registered'
    };
  });

  // Handle patient selection
  const handlePatientSelect = (patientId: string) => {
    const patient = patients.find(p => p.id === patientId);
    if (patient) {
      setSelectedPatientId(patientId);
      setFormData(prev => ({
        ...prev,
        patientId: patient.id,
        patientName: patient.patient_name
      }));
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const submitForm = async () => {

    if (!date) {
      alert('Please select a date');
      return;
    }

    if (!formData.time) {
      alert('Please select a time');
      return;
    }

    // Validate required fields - doctor and department are optional if no data available
    const isDoctorRequired = doctors.length > 0;
    const isDepartmentRequired = departments.length > 0;

    if (!formData.patientName) {
      alert('Please enter patient name');
      return;
    }

    if (isDoctorRequired && !formData.doctorName) {
      alert('Please select a doctor');
      return;
    }

    // Department is completely optional when no departments exist
    if (isDepartmentRequired && !formData.departmentName) {
      alert('Please select a department');
      return;
    }

    try {
      // Map form data to match database schema
      const doctorWithSpecialty = formData.doctorSpecialty
        ? `${formData.doctorName || 'Not specified'} (${formData.doctorSpecialty})`
        : formData.doctorName || 'Not specified';

      const appointmentData = {
        patient_name: formData.patientName,
        doctor: doctorWithSpecialty, // Include specialty in doctor field
        department: formData.departmentName || null, // Department is optional - can be null
        date_time: `${format(date, 'yyyy-MM-dd')} ${formData.time}`,
        type: formData.type,
        status: 'scheduled'
      };

      await createAppointment.mutateAsync(appointmentData);
      onSuccess?.();
      // Let the parent handle closing
    } catch (error) {
      console.error('Error creating appointment:', error);
    }
  };

  useImperativeHandle(ref, () => ({
    submitForm,
    isPending: createAppointment.isPending,
  }));

  // Transform database data for easier use
  const departmentOptions = departments.map(dept => ({
    id: dept.id,
    name: dept.department_name
  }));

  const doctorOptions = doctors.map(doc => ({
    id: doc.id,
    name: doc.name,
    specialty: doc.specialty
  }));

  // Predefined specialties for manual entry
  const specialties = [
    'Cardiology',
    'Neurology',
    'Orthopedics',
    'Pediatrics',
    'General Medicine',
    'Dermatology',
    'Ophthalmology',
    'Psychiatry',
    'Gynecology',
    'Urology',
    'Emergency Medicine',
    'Internal Medicine',
    'Surgery',
    'Anesthesiology',
    'Radiology'
  ];

  const timeSlots = [
    '09:00 AM', '09:30 AM', '10:00 AM', '10:30 AM', '11:00 AM', '11:30 AM',
    '12:00 PM', '12:30 PM', '01:00 PM', '01:30 PM', '02:00 PM', '02:30 PM',
    '03:00 PM', '03:30 PM', '04:00 PM', '04:30 PM', '05:00 PM'
  ];

  const appointmentTypes = [
    { id: 'consultation', name: 'Consultation' },
    { id: 'followup', name: 'Follow-up' },
    { id: 'checkup', name: 'Routine Check-up' },
    { id: 'emergency', name: 'Emergency' },
    { id: 'procedure', name: 'Procedure' },
  ];

  // Add error boundary
  try {
    return (
      <form className="space-y-6">
        {/* Removed title as it is in the dialog header */}

      {/* Patient Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Patient Information</h3>
        {/* Enhanced Patient Selection */}
        <div className="space-y-4">
          <div className="space-y-3">
            <Label className="text-base font-semibold">Patient Selection</Label>
            <RadioGroup
              value={patientSelectionMode}
              onValueChange={(value: 'existing' | 'new') => setPatientSelectionMode(value)}
              className="flex space-x-6"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="existing" id="existing" />
                <Label htmlFor="existing" className="cursor-pointer">Select Existing Patient</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="new" id="new" />
                <Label htmlFor="new" className="cursor-pointer">New Patient</Label>
              </div>
            </RadioGroup>
          </div>

          {patientSelectionMode === 'existing' ? (
            <div className="space-y-3">
              <Label htmlFor="existingPatient">Choose Patient from Workflow</Label>
              <Select
                value={selectedPatientId}
                onValueChange={handlePatientSelect}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder={patientsLoading ? "Loading patients..." : "Select a patient"} />
                </SelectTrigger>
                <SelectContent>
                  {patientsWithWorkflow.map((patient) => (
                    <SelectItem key={patient.id} value={patient.id}>
                      <div className="flex items-center space-x-3 py-1">
                        <User className="h-4 w-4 text-gray-500" />
                        <div>
                          <div className="font-medium">{patient.patient_name}</div>
                          <div className="text-sm text-gray-500">
                            {patient.email} • Status: {patient.workflowStatus}
                          </div>
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {selectedPatientId && (
                <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="text-sm text-blue-800">
                    <strong>Selected:</strong> {formData.patientName}
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="patientId">Patient ID (Optional)</Label>
                <Input
                  id="patientId"
                  name="patientId"
                  value={formData.patientId}
                  onChange={handleChange}
                  placeholder="Enter patient ID"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="patientName">Patient Name *</Label>
                <Input
                  id="patientName"
                  name="patientName"
                  value={formData.patientName}
                  onChange={handleChange}
                  placeholder="Enter patient name"
                  required
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Department and Doctor */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Department & Doctor</h3>
        {(departmentOptions.length === 0 || doctorOptions.length === 0) && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <p className="text-sm text-blue-800">
              <strong>Note:</strong> {departmentOptions.length === 0 && doctorOptions.length === 0
                ? 'No departments or doctors have been added yet. Department is optional, and you can enter doctor name manually or add them from the Doctors page first.'
                : departmentOptions.length === 0
                ? 'No departments have been added yet. Department is optional - you can leave it blank or enter a department name manually.'
                : 'No doctors have been added yet. You can enter the doctor name manually or add doctors from the Doctors page first.'
              }
            </p>
          </div>
        )}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="department">
              Department <span className="text-sm text-gray-500">(Optional)</span>
              {departmentOptions.length === 0 && <span className="text-sm text-gray-500"> - No departments added yet</span>}
            </Label>
            {departmentsLoading ? (
              <div className="text-sm text-gray-500">Loading departments...</div>
            ) : departmentOptions.length === 0 ? (
              <Input
                placeholder="Enter department name (optional)"
                value={formData.departmentName}
                onChange={(e) => handleSelectChange('departmentName', e.target.value)}
              />
            ) : (
              <Select
                value={formData.department}
                onValueChange={(value) => {
                  if (value === 'no-department') {
                    // Handle "No department" selection
                    handleSelectChange('department', '');
                    handleSelectChange('departmentName', '');
                  } else {
                    const department = departmentOptions.find(d => d.id === value);
                    handleSelectChange('department', value);
                    handleSelectChange('departmentName', department?.name || '');
                  }
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select department (optional)" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="no-department">No department</SelectItem>
                  {departmentOptions.map(dept => (
                    <SelectItem key={dept.id} value={dept.id}>{dept.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          </div>
          <div className="space-y-2">
            <Label htmlFor="doctorId">
              Doctor {doctorOptions.length === 0 && <span className="text-sm text-gray-500">(Optional - No doctors added yet)</span>}
            </Label>
            {doctorsLoading ? (
              <div className="text-sm text-gray-500">Loading doctors...</div>
            ) : doctorOptions.length === 0 ? (
              <Input
                placeholder="Enter doctor name (optional)"
                value={formData.doctorName}
                onChange={(e) => handleSelectChange('doctorName', e.target.value)}
              />
            ) : (
              <Select
                value={formData.doctorId}
                onValueChange={(value) => {
                  const doctor = doctorOptions.find(d => d.id === value);
                  handleSelectChange('doctorId', value);
                  handleSelectChange('doctorName', doctor?.name || '');
                  handleSelectChange('doctorSpecialty', doctor?.specialty || '');
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select doctor" />
                </SelectTrigger>
                <SelectContent>
                  {doctorOptions.map(doc => (
                    <SelectItem key={doc.id} value={doc.id}>
                      {doc.name} {doc.specialty && <span className="text-gray-500">- {doc.specialty}</span>}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          </div>
        </div>

        {/* Doctor Specialty - Show when doctor is manually entered or selected */}
        {(formData.doctorName || formData.doctorId) && (
          <div className="space-y-2">
            <Label htmlFor="doctorSpecialty">
              Doctor Specialty <span className="text-sm text-gray-500">(Optional)</span>
            </Label>
            <div className="space-y-2">
              <Select
                value={formData.doctorSpecialty}
                onValueChange={(value) => {
                  if (value === 'custom') {
                    // Clear to allow manual input
                    handleSelectChange('doctorSpecialty', '');
                  } else if (value === 'no-specialty') {
                    // Handle "No specialty" selection
                    handleSelectChange('doctorSpecialty', '');
                  } else {
                    handleSelectChange('doctorSpecialty', value);
                  }
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select specialty (optional)" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="no-specialty">No specialty</SelectItem>
                  {specialties.map(specialty => (
                    <SelectItem key={specialty} value={specialty}>{specialty}</SelectItem>
                  ))}
                  <SelectItem value="custom">Other (enter manually below)</SelectItem>
                </SelectContent>
              </Select>

              {/* Manual input field - always available */}
              <Input
                placeholder="Or enter custom specialty"
                value={formData.doctorSpecialty === 'custom' ? '' : formData.doctorSpecialty}
                onChange={(e) => handleSelectChange('doctorSpecialty', e.target.value)}
              />

              {/* Show selected doctor's default specialty if available */}
              {formData.doctorId && doctorOptions.find(d => d.id === formData.doctorId)?.specialty && (
                <p className="text-xs text-gray-500">
                  Doctor's default specialty: {doctorOptions.find(d => d.id === formData.doctorId)?.specialty}
                </p>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Date and Time */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Date & Time</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label>Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !date && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {date ? format(date, "PPP") : <span>Select date</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={date}
                  onSelect={setDate}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
          <div className="space-y-2">
            <Label htmlFor="time">Time</Label>
            <Select 
              value={formData.time} 
              onValueChange={(value) => handleSelectChange('time', value)}
              required
            >
              <SelectTrigger>
                <SelectValue placeholder="Select time">
                  <div className="flex items-center">
                    <Clock className="mr-2 h-4 w-4" />
                    {formData.time || "Select time"}
                  </div>
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                {timeSlots.map(time => (
                  <SelectItem key={time} value={time}>{time}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* Appointment Type */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Appointment Details</h3>
        <div className="space-y-2">
          <Label>Appointment Type</Label>
          <RadioGroup 
            value={formData.type}
            onValueChange={(value) => handleSelectChange('type', value)}
            className="grid grid-cols-2 gap-2"
          >
            {appointmentTypes.map(type => (
              <div key={type.id} className="flex items-center space-x-2">
                <RadioGroupItem value={type.id} id={`type-${type.id}`} />
                <Label htmlFor={`type-${type.id}`}>{type.name}</Label>
              </div>
            ))}
          </RadioGroup>
        </div>
      </div>

      {/* Notes */}
      <div className="space-y-2">
        <Label htmlFor="notes">Notes</Label>
        <Textarea 
          id="notes" 
          name="notes" 
          value={formData.notes} 
          onChange={handleChange} 
          placeholder="Add any additional notes or instructions"
          className="min-h-[100px]"
        />
      </div>
    </form>
  );
  } catch (error) {
    console.error('Error rendering AppointmentForm:', error);
    return (
      <div className="p-4 border border-red-200 rounded-lg bg-red-50">
        <h3 className="text-lg font-semibold text-red-800 mb-2">Error Loading Form</h3>
        <p className="text-red-600 mb-4">
          There was an error loading the appointment form. This might be due to missing database tables.
        </p>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Patient Name *</label>
            <input
              type="text"
              className="w-full border border-gray-300 rounded-md px-3 py-2"
              placeholder="Enter patient name"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Doctor Name</label>
            <input
              type="text"
              className="w-full border border-gray-300 rounded-md px-3 py-2"
              placeholder="Enter doctor name"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Department</label>
            <input
              type="text"
              className="w-full border border-gray-300 rounded-md px-3 py-2"
              placeholder="Enter department"
            />
          </div>
          <p className="text-sm text-gray-600">
            Please check the browser console for more details about the error.
          </p>
        </div>
      </div>
    );
  }
});
