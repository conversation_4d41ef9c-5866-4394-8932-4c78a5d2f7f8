
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { format } from 'date-fns';
import { Calendar as CalendarIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useSupabaseData } from '@/hooks/useSupabaseData';
import { useNavigate } from 'react-router-dom';
import { toast } from '@/hooks/use-toast';

interface PatientFormProps {
  onClose?: () => void;
  onSuccess?: () => void;
  existingPatient?: any;
  useWorkflowIntegration?: boolean;
  autoRedirectToConsultation?: boolean;
}

export const PatientForm = ({
  onClose,
  onSuccess,
  existingPatient,
  useWorkflowIntegration = false,
  autoRedirectToConsultation = false
}: PatientFormProps) => {
  const { createPatient, createPatientWithWorkflow } = useSupabaseData();
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const [dateOfBirth, setDateOfBirth] = useState<Date | undefined>(
    existingPatient?.date_of_birth ? new Date(existingPatient.date_of_birth) : undefined
  );
  
  const [manualDate, setManualDate] = useState(
    existingPatient?.date_of_birth || ''
  );
  
  const [useCalendar, setUseCalendar] = useState(true);
  
  const [formData, setFormData] = useState({
    name: existingPatient?.patient_name || '',
    gender: existingPatient?.gender || '',
    email: existingPatient?.email || '',
    phone: existingPatient?.phone_number || '',
    bloodType: existingPatient?.blood_type || '',
    insurance: existingPatient?.insurance || '',
    emergencyContact: existingPatient?.emergency_contact || '',
    notes: existingPatient?.notes || '',
    registration_time: existingPatient?.registration_time || new Date().toTimeString().slice(0, 5), // HH:MM format
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleManualDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setManualDate(value);
    if (value) {
      setDateOfBirth(new Date(value));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const finalDate = useCalendar ? dateOfBirth : (manualDate ? new Date(manualDate) : undefined);
    
    if (!formData.name || !formData.phone || !finalDate) {
      return;
    }

    setIsSubmitting(true);

    try {
      const patientData = {
        patient_name: formData.name,
        email: formData.email || '', // Use empty string instead of null for database compatibility
        phone_number: formData.phone,
        date_of_birth: format(finalDate, 'yyyy-MM-dd'),
        blood_type: formData.bloodType || null,
        insurance: formData.insurance || null,
        emergency_contact: formData.emergencyContact || null,
        notes: formData.notes || null,
        registration_date: format(new Date(), 'yyyy-MM-dd'),
        registration_time: formData.registration_time
      };

      let result;
      if (useWorkflowIntegration) {
        result = await createPatientWithWorkflow.mutateAsync(patientData);
      } else {
        result = await createPatient.mutateAsync(patientData);
      }

      // Show success message
      toast({
        title: "Success",
        description: useWorkflowIntegration
          ? "Patient registered successfully. Redirecting to Consultation Department..."
          : "Patient registered successfully",
      });

      // Handle callbacks first
      if (onSuccess) {
        onSuccess();
      } else if (onClose) {
        onClose();
      }

      // Auto-redirect to consultation if workflow integration is enabled
      if (useWorkflowIntegration && autoRedirectToConsultation) {
        setTimeout(() => {
          navigate('/consultation-department');
        }, 1500); // Small delay to show success message
      }
    } catch (error) {
      console.error('Error creating patient:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const genders = ['male', 'female', 'other'];
  const bloodTypes = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'];

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          {existingPatient ? 'Edit Patient' : 'Add New Patient'}
        </h2>
      </div>

      {/* Patient Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Patient Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="name">Full Name *</Label>
            <Input
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              placeholder="Enter patient full name"
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="gender">Gender</Label>
            <Select value={formData.gender} onValueChange={(value) => handleSelectChange('gender', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select gender" />
              </SelectTrigger>
              <SelectContent>
                {genders.map(gender => (
                  <SelectItem key={gender} value={gender}>{gender.charAt(0).toUpperCase() + gender.slice(1)}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2 md:col-span-2">
            <Label htmlFor="dateOfBirth">Date of Birth *</Label>
            <div className="flex items-center gap-2 mb-2">
              <Button
                type="button"
                variant={useCalendar ? "default" : "outline"}
                size="sm"
                onClick={() => setUseCalendar(true)}
              >
                Calendar
              </Button>
              <Button
                type="button"
                variant={!useCalendar ? "default" : "outline"}
                size="sm"
                onClick={() => setUseCalendar(false)}
              >
                Manual Entry
              </Button>
            </div>
            {useCalendar ? (
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !dateOfBirth && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dateOfBirth ? format(dateOfBirth, "PPP") : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={dateOfBirth}
                    onSelect={setDateOfBirth}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            ) : (
              <Input
                type="date"
                value={manualDate}
                onChange={handleManualDateChange}
                className="w-full"
                required
              />
            )}
          </div>
          <div className="space-y-2">
            <Label htmlFor="bloodType">Blood Type (Optional)</Label>
            <Select value={formData.bloodType} onValueChange={(value) => handleSelectChange('bloodType', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select blood type (optional)" />
              </SelectTrigger>
              <SelectContent>
                {bloodTypes.map(type => (
                  <SelectItem key={type} value={type}>{type}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>



      {/* Contact Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Contact Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="Enter patient email (optional)"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="phone">Phone Number *</Label>
            <Input
              id="phone"
              name="phone"
              type="tel"
              value={formData.phone}
              onChange={handleChange}
              placeholder="Enter patient phone number"
              required
            />
          </div>
          <div className="space-y-2 md:col-span-2">
            <Label htmlFor="emergencyContact">Emergency Contact</Label>
            <Input
              id="emergencyContact"
              name="emergencyContact"
              value={formData.emergencyContact}
              onChange={handleChange}
              placeholder="Enter emergency contact"
            />
          </div>
        </div>
      </div>

      {/* Insurance Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Insurance Information</h3>
        <div className="space-y-2">
          <Label htmlFor="insurance">Insurance Provider</Label>
          <Input
            id="insurance"
            name="insurance"
            value={formData.insurance}
            onChange={handleChange}
            placeholder="Enter insurance provider"
          />
        </div>
      </div>

      {/* Notes */}
      <div className="space-y-2">
        <Label htmlFor="notes">Notes</Label>
        <Textarea
          id="notes"
          name="notes"
          value={formData.notes}
          onChange={handleChange}
          placeholder="Add any additional notes"
          className="min-h-[100px]"
        />
      </div>

      {/* Registration Time */}
      <div className="space-y-2">
        <Label htmlFor="registration_time">Registration Time *</Label>
        <Input
          id="registration_time"
          name="registration_time"
          type="time"
          value={formData.registration_time}
          onChange={handleChange}
          required
        />
        <p className="text-xs text-gray-500">
          Time when patient registration was started
        </p>
      </div>

      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>
          Cancel
        </Button>
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? 'Adding...' : (existingPatient ? 'Save Changes' : 'Add Patient')}
        </Button>
      </div>
    </form>
  );
};
