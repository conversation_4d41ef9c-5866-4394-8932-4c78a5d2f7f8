import React from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { 
  LayoutDashboard, 
  Users, 
  Calendar, 
  Stethoscope,
  DollarSign,
  // Removed icons for pages no longer in bottom nav
  // Book,
  // Feather,
  // Mail,
  // Info
} from 'lucide-react';
import { cn } from '@/lib/utils';

export const MobileBottomNav = () => {
  const location = useLocation();

  const navItems = [
    // Updated mobile navigation links
    { href: '/dashboard', icon: LayoutDashboard, label: 'Dashboard' },
    { href: '/doctors', icon: Stethoscope, label: 'Doctors' },
    { href: '/appointments', icon: Calendar, label: 'Appointments' },
    { href: '/billing-accounting', icon: DollarSign, label: 'Billing' },
  ];

  return (
    <div className="fixed bottom-0 left-0 right-0 z-40 bg-white border-t border-gray-200 shadow-lg md:hidden">
      <nav className="flex h-16 items-center justify-around px-2">
        {navItems.map((item) => {
          const isActive = location.pathname === item.href;
          return (
            <NavLink
              key={item.href}
              to={item.href}
              className={cn(
                "flex flex-col items-center justify-center space-y-1 text-xs font-medium transition-colors w-16",
                isActive 
                  ? "text-purple-600" 
                  : "text-gray-500 hover:text-gray-700"
              )}
            >
              <div className={cn(
                "flex items-center justify-center w-8 h-8 rounded-full",
                isActive 
                  ? "bg-gradient-to-r from-purple-600 to-blue-500 text-white" 
                  : "bg-gray-100 text-gray-500"
              )}>
                <item.icon className="h-4 w-4" />
              </div>
              <span>{item.label}</span>
            </NavLink>
          );
        })}
      </nav>
    </div>
  );
}; 