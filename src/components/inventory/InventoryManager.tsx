import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { 
  Package, 
  AlertTriangle, 
  Search, 
  Plus,
  Minus,
  Calendar,
  DollarSign,
  TrendingDown,
  TrendingUp
} from 'lucide-react';
import { useSupabaseData } from '@/hooks/useSupabaseData';

interface InventoryManagerProps {
  showLowStockOnly?: boolean;
  maxItems?: number;
}

export const InventoryManager = ({ showLowStockOnly = false, maxItems }: InventoryManagerProps) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [stockAdjustment, setStockAdjustment] = useState(0);
  
  const { usePharmacyInventory, updatePharmacyInventory } = useSupabaseData();
  const { data: inventory = [], isLoading } = usePharmacyInventory();

  const filteredInventory = inventory
    .filter(item => {
      const matchesSearch = item.medication_name.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesLowStock = showLowStockOnly ? item.stock_quantity < 10 : true;
      return matchesSearch && matchesLowStock;
    })
    .slice(0, maxItems || inventory.length);

  const getLowStockItems = () => {
    return inventory.filter(item => item.stock_quantity < 10);
  };

  const getExpiringItems = () => {
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
    
    return inventory.filter(item => {
      const expiryDate = new Date(item.expiry_date);
      return expiryDate <= thirtyDaysFromNow;
    });
  };

  const getTotalValue = () => {
    return inventory.reduce((total, item) => total + (item.price * item.stock_quantity), 0);
  };

  const getStockStatus = (quantity: number) => {
    if (quantity === 0) return { label: 'Out of Stock', color: 'bg-red-100 text-red-800' };
    if (quantity < 10) return { label: 'Low Stock', color: 'bg-orange-100 text-orange-800' };
    if (quantity < 50) return { label: 'Medium Stock', color: 'bg-yellow-100 text-yellow-800' };
    return { label: 'In Stock', color: 'bg-green-100 text-green-800' };
  };

  const isExpiringSoon = (expiryDate: string) => {
    const expiry = new Date(expiryDate);
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
    return expiry <= thirtyDaysFromNow;
  };

  const handleStockAdjustment = async () => {
    if (!selectedItem || stockAdjustment === 0) return;

    try {
      const newQuantity = Math.max(0, selectedItem.stock_quantity + stockAdjustment);
      await updatePharmacyInventory.mutateAsync({
        id: selectedItem.id,
        updates: {
          stock_quantity: newQuantity,
          updated_at: new Date().toISOString()
        }
      });
      
      setIsDialogOpen(false);
      setSelectedItem(null);
      setStockAdjustment(0);
    } catch (error) {
      console.error('Error updating inventory:', error);
    }
  };

  const openAdjustmentDialog = (item: any) => {
    setSelectedItem(item);
    setStockAdjustment(0);
    setIsDialogOpen(true);
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">Loading inventory...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Inventory Stats */}
      {!showLowStockOnly && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <Package className="h-8 w-8 text-blue-600 mr-3" />
                <div>
                  <p className="text-2xl font-bold">{inventory.length}</p>
                  <p className="text-sm text-gray-600">Total Items</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <AlertTriangle className="h-8 w-8 text-red-600 mr-3" />
                <div>
                  <p className="text-2xl font-bold">{getLowStockItems().length}</p>
                  <p className="text-sm text-gray-600">Low Stock</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <Calendar className="h-8 w-8 text-orange-600 mr-3" />
                <div>
                  <p className="text-2xl font-bold">{getExpiringItems().length}</p>
                  <p className="text-sm text-gray-600">Expiring Soon</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <DollarSign className="h-8 w-8 text-green-600 mr-3" />
                <div>
                  <p className="text-2xl font-bold">${getTotalValue().toFixed(2)}</p>
                  <p className="text-sm text-gray-600">Total Value</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center">
              <Package className="mr-2 h-5 w-5" />
              {showLowStockOnly ? 'Low Stock Items' : 'Inventory Management'}
              {filteredInventory.length > 0 && ` (${filteredInventory.length})`}
            </span>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search medications..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 w-64"
              />
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {filteredInventory.length === 0 ? (
            <div className="text-center py-8">
              <Package className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <p className="text-gray-500">
                {searchTerm ? 'No medications found matching your search' : 'No inventory items found'}
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {filteredInventory.map((item) => {
                const stockStatus = getStockStatus(item.stock_quantity);
                const expiringSoon = isExpiringSoon(item.expiry_date);
                
                return (
                  <div key={item.id} className="p-4 border rounded-lg hover:bg-gray-50">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="font-medium text-gray-900">{item.medication_name}</h3>
                          <Badge className={stockStatus.color}>
                            {stockStatus.label}
                          </Badge>
                          {expiringSoon && (
                            <Badge className="bg-red-100 text-red-800">
                              <Calendar className="h-3 w-3 mr-1" />
                              Expiring Soon
                            </Badge>
                          )}
                        </div>
                        
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                          <div>
                            <span className="font-medium">Stock:</span> {item.stock_quantity} units
                          </div>
                          <div>
                            <span className="font-medium">Price:</span> ${item.price}
                          </div>
                          <div>
                            <span className="font-medium">Value:</span> ${(item.stock_quantity * item.price).toFixed(2)}
                          </div>
                          <div>
                            <span className="font-medium">Expires:</span> {new Date(item.expiry_date).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => openAdjustmentDialog(item)}
                        >
                          Adjust Stock
                        </Button>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Stock Adjustment Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Adjust Stock - {selectedItem?.medication_name}</DialogTitle>
          </DialogHeader>
          
          {selectedItem && (
            <div className="space-y-4">
              <div className="p-4 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-600">Current Stock: <span className="font-medium">{selectedItem.stock_quantity} units</span></p>
                <p className="text-sm text-gray-600">Unit Price: <span className="font-medium">${selectedItem.price}</span></p>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Stock Adjustment</label>
                <div className="flex items-center space-x-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setStockAdjustment(prev => prev - 1)}
                  >
                    <Minus className="h-4 w-4" />
                  </Button>
                  <Input
                    type="number"
                    value={stockAdjustment}
                    onChange={(e) => setStockAdjustment(parseInt(e.target.value) || 0)}
                    className="text-center w-24"
                  />
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setStockAdjustment(prev => prev + 1)}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                <p className="text-xs text-gray-500">
                  New stock will be: {Math.max(0, selectedItem.stock_quantity + stockAdjustment)} units
                </p>
              </div>
              
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button 
                  onClick={handleStockAdjustment}
                  disabled={stockAdjustment === 0 || updatePharmacyInventory.isPending}
                >
                  {updatePharmacyInventory.isPending ? 'Updating...' : 'Update Stock'}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};
