import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { format } from 'date-fns';
import { Calendar as CalendarIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

interface InventoryFormProps {
  onClose: () => void;
  existingItem?: any;
}

export const InventoryForm = ({ onClose, existingItem }: InventoryFormProps) => {
  const [expiryDate, setExpiryDate] = useState<Date | undefined>(
    existingItem?.expiryDate ? new Date(existingItem.expiryDate) : undefined
  );
  
  const [formData, setFormData] = useState({
    id: existingItem?.id || '',
    name: existingItem?.name || '',
    category: existingItem?.category || '',
    quantity: existingItem?.quantity || 0,
    unit: existingItem?.unit || 'pcs',
    location: existingItem?.location || '',
    supplier: existingItem?.supplier || '',
    reorderLevel: existingItem?.reorderLevel || 10,
    description: existingItem?.description || '',
    status: existingItem?.status || 'in-stock',
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleNumberChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: Number(value) }));
  };

  const handleSelectChange = (name, value) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Here you would typically send the data to your API
    console.log({
      ...formData,
      expiryDate: expiryDate ? format(expiryDate, 'yyyy-MM-dd') : null,
    });
    onClose();
  };

  // Sample data
  const categories = [
    'Medications',
    'Surgical Supplies',
    'Laboratory Supplies',
    'Diagnostic Equipment',
    'Office Supplies',
    'Cleaning Supplies',
    'Personal Protective Equipment',
    'Medical Devices'
  ];

  const units = [
    'pcs', 'boxes', 'bottles', 'vials', 'packs', 'kits', 'sets', 'pairs', 'rolls', 'tubes'
  ];

  const locations = [
    'Main Storage',
    'Pharmacy',
    'Emergency Room',
    'Operating Room',
    'Laboratory',
    'Radiology',
    'ICU',
    'General Ward',
    'Outpatient Department'
  ];

  const suppliers = [
    'MedSupply Co.',
    'Healthcare Essentials',
    'PharmaMed Inc.',
    'LabTech Solutions',
    'Medical Devices Ltd.',
    'Surgical Instruments Inc.'
  ];

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          {existingItem ? 'Edit Inventory Item' : 'Add New Inventory Item'}
        </h2>
        <p className="text-gray-500">
          {existingItem ? 'Update the item details and stock information' : 'Enter the item details to add it to inventory'}
        </p>
      </div>

      {/* Basic Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Basic Information</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="id">Item ID</Label>
            <Input 
              id="id" 
              name="id" 
              value={formData.id} 
              onChange={handleChange} 
              placeholder="e.g., INV-001"
              disabled={!!existingItem}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="name">Item Name</Label>
            <Input 
              id="name" 
              name="name" 
              value={formData.name} 
              onChange={handleChange} 
              placeholder="Enter item name"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="category">Category</Label>
            <Select 
              value={formData.category} 
              onValueChange={(value) => handleSelectChange('category', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent>
                {categories.map(category => (
                  <SelectItem key={category} value={category}>{category}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea 
              id="description" 
              name="description" 
              value={formData.description} 
              onChange={handleChange} 
              placeholder="Brief description of the item"
            />
          </div>
        </div>
      </div>

      {/* Stock Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Stock Information</h3>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label htmlFor="quantity">Quantity</Label>
            <Input 
              id="quantity" 
              name="quantity" 
              type="number" 
              value={formData.quantity} 
              onChange={handleNumberChange} 
              min={0}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="unit">Unit</Label>
            <Select 
              value={formData.unit} 
              onValueChange={(value) => handleSelectChange('unit', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select unit" />
              </SelectTrigger>
              <SelectContent>
                {units.map(unit => (
                  <SelectItem key={unit} value={unit}>{unit}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="reorderLevel">Reorder Level</Label>
            <Input 
              id="reorderLevel" 
              name="reorderLevel" 
              type="number" 
              value={formData.reorderLevel} 
              onChange={handleNumberChange} 
              min={0}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label>Expiry Date (if applicable)</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !expiryDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {expiryDate ? format(expiryDate, "PPP") : <span>Select date</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={expiryDate}
                  onSelect={setExpiryDate}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select 
              value={formData.status} 
              onValueChange={(value) => handleSelectChange('status', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="in-stock">In Stock</SelectItem>
                <SelectItem value="low-stock">Low Stock</SelectItem>
                <SelectItem value="out-of-stock">Out of Stock</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* Location and Supplier */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Location & Supplier</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="location">Storage Location</Label>
            <Select 
              value={formData.location} 
              onValueChange={(value) => handleSelectChange('location', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select location" />
              </SelectTrigger>
              <SelectContent>
                {locations.map(location => (
                  <SelectItem key={location} value={location}>{location}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="supplier">Supplier</Label>
            <Select 
              value={formData.supplier} 
              onValueChange={(value) => handleSelectChange('supplier', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select supplier" />
              </SelectTrigger>
              <SelectContent>
                {suppliers.map(supplier => (
                  <SelectItem key={supplier} value={supplier}>{supplier}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-4 pt-4">
        <Button type="button" variant="outline" onClick={onClose}>
          Cancel
        </Button>
        <Button type="submit" className="bg-purple-600 hover:bg-purple-700 text-white">
          {existingItem ? 'Update Item' : 'Add Item'}
        </Button>
      </div>
    </form>
  );
};
