import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { format } from 'date-fns';
import { Calendar as CalendarIcon, Package, Pill, Stethoscope, Wrench, Trash2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useSupabaseData } from '@/hooks/useSupabaseData';
import { toast } from '@/hooks/use-toast';

interface PharmacyInventoryFormProps {
  onClose: () => void;
  onSuccess?: () => void;
  existingItem?: any;
  action: 'add' | 'edit' | 'view';
}

export const PharmacyInventoryForm = ({ 
  onClose, 
  onSuccess, 
  existingItem, 
  action 
}: PharmacyInventoryFormProps) => {
  const { createPharmacyInventory, updatePharmacyInventory } = useSupabaseData();
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const [expiryDate, setExpiryDate] = useState<Date | undefined>(
    existingItem?.expiry_date ? new Date(existingItem.expiry_date) : undefined
  );
  
  const [formData, setFormData] = useState({
    name: existingItem?.medication_name || existingItem?.name || '',
    generic_name: existingItem?.generic_name || '',
    manufacturer: existingItem?.manufacturer || '',
    category: existingItem?.category || '',
    type: existingItem?.type || 'medicine',
    dosage_form: existingItem?.dosage_form || '',
    strength: existingItem?.strength || '',
    unit: existingItem?.unit || 'tablets',
    unit_price: existingItem?.price || existingItem?.unit_price || '',
    stock_quantity: existingItem?.stock_quantity || '',
    reorder_level: existingItem?.reorder_level || '',
    batch_number: existingItem?.batch_number || '',
    supplier: existingItem?.supplier || '',
    location: existingItem?.location || '',
    description: existingItem?.description || ''
  });

  const productTypes = [
    { value: 'medicine', label: 'Medicine', icon: Pill },
    { value: 'medical_supply', label: 'Medical Supply', icon: Package },
    { value: 'equipment', label: 'Equipment', icon: Stethoscope },
    { value: 'consumable', label: 'Consumable', icon: Trash2 },
    { value: 'other', label: 'Other', icon: Wrench }
  ];

  const medicineCategories = [
    'Analgesics', 'Antibiotics', 'Antivirals', 'Antifungals', 'Cardiovascular',
    'Respiratory', 'Gastrointestinal', 'Neurological', 'Endocrine', 'Vitamins',
    'Vaccines', 'Emergency Medicine', 'Other'
  ];

  const medicalSupplyCategories = [
    'PPE', 'Injection Supplies', 'Wound Care', 'Diagnostic Supplies',
    'Surgical Supplies', 'Laboratory Supplies', 'Other'
  ];

  const equipmentCategories = [
    'Diagnostic Equipment', 'Monitoring Equipment', 'Surgical Equipment',
    'Laboratory Equipment', 'Emergency Equipment', 'Other'
  ];

  const consumableCategories = [
    'Cleaning Supplies', 'Office Supplies', 'Patient Care', 'Laboratory Consumables', 'Other'
  ];

  const dosageForms = [
    'Tablet', 'Capsule', 'Syrup', 'Injection', 'Cream', 'Ointment',
    'Drops', 'Inhaler', 'Patch', 'Suppository', 'Other'
  ];

  const units = [
    'tablets', 'capsules', 'bottles', 'vials', 'boxes', 'pieces',
    'kg', 'grams', 'liters', 'ml', 'units', 'sets', 'packs'
  ];

  const suppliers = [
    'MedSupply Co.', 'PharmaCorp', 'MedEquip Ltd.', 'TechMed Solutions',
    'CleanMed Supplies', 'HealthPlus Distributors', 'Global Medical Inc.',
    'Local Supplier', 'Other'
  ];

  const getCategoriesByType = (type: string) => {
    switch (type) {
      case 'medicine':
        return medicineCategories;
      case 'medical_supply':
        return medicalSupplyCategories;
      case 'equipment':
        return equipmentCategories;
      case 'consumable':
        return consumableCategories;
      default:
        return ['General', 'Other'];
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: Number(value) || '' }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ 
      ...prev, 
      [name]: value,
      // Reset category when type changes
      ...(name === 'type' ? { category: '' } : {})
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Create a comprehensive medication name that includes additional details
      let medicationName = formData.name;

      // Add additional details to the medication name for better identification
      const details = [];
      if (formData.generic_name) details.push(`(${formData.generic_name})`);
      if (formData.strength) details.push(formData.strength);
      if (formData.dosage_form) details.push(formData.dosage_form);
      if (formData.manufacturer) details.push(`by ${formData.manufacturer}`);

      if (details.length > 0) {
        medicationName += ` - ${details.join(' ')}`;
      }

      // Match the actual database schema: id, medication_name, stock_quantity, price, expiry_date, status, user_id, created_at, updated_at
      const inventoryData = {
        medication_name: medicationName,
        stock_quantity: Number(formData.stock_quantity),
        price: Number(formData.unit_price),
        expiry_date: expiryDate ? format(expiryDate, 'yyyy-MM-dd') : new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // Default to 1 year from now
        status: Number(formData.stock_quantity) > Number(formData.reorder_level || 10) ? 'In Stock' : 'Low Stock'
      };

      if (action === 'edit' && existingItem?.id) {
        await updatePharmacyInventory.mutateAsync({
          id: existingItem.id,
          inventoryData
        });
      } else {
        await createPharmacyInventory.mutateAsync(inventoryData);
      }

      onSuccess?.();
      onClose();
    } catch (error) {
      console.error('Error saving inventory item:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const isReadOnly = action === 'view';
  const title = action === 'add' ? 'Add New Product' : 
                action === 'edit' ? 'Edit Product' : 'View Product';

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">{title}</h2>
        <Button variant="outline" onClick={onClose}>
          Close
        </Button>
      </div>

      {/* Database Schema Notice */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <Package className="h-5 w-5 text-blue-600 mt-0.5 mr-3" />
          <div>
            <h3 className="text-sm font-medium text-blue-800">Database Schema Notice</h3>
            <p className="text-sm text-blue-700 mt-1">
              Currently, the database supports basic inventory fields: medication name, stock quantity, price, expiry date, and status.
              Additional fields shown below are for demonstration and will be stored as part of the medication name or description.
            </p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Product Type Selection */}
        <div className="space-y-2">
          <Label htmlFor="type">Product Type *</Label>
          <Select 
            value={formData.type} 
            onValueChange={(value) => handleSelectChange('type', value)}
            disabled={isReadOnly}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select product type" />
            </SelectTrigger>
            <SelectContent>
              {productTypes.map(type => {
                const IconComponent = type.icon;
                return (
                  <SelectItem key={type.value} value={type.value}>
                    <div className="flex items-center">
                      <IconComponent className="mr-2 h-4 w-4" />
                      {type.label}
                    </div>
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>
        </div>

        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="name">Product Name *</Label>
            <Input
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              placeholder="Enter product name"
              required
              readOnly={isReadOnly}
            />
          </div>
          
          {formData.type === 'medicine' && (
            <div className="space-y-2">
              <Label htmlFor="generic_name">Generic Name</Label>
              <Input
                id="generic_name"
                name="generic_name"
                value={formData.generic_name}
                onChange={handleChange}
                placeholder="Enter generic name"
                readOnly={isReadOnly}
              />
            </div>
          )}
        </div>

        {/* Category and Manufacturer */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="category">Category *</Label>
            <Select 
              value={formData.category} 
              onValueChange={(value) => handleSelectChange('category', value)}
              disabled={isReadOnly}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent>
                {getCategoriesByType(formData.type).map(category => (
                  <SelectItem key={category} value={category}>{category}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="manufacturer">Manufacturer/Brand</Label>
            <Input
              id="manufacturer"
              name="manufacturer"
              value={formData.manufacturer}
              onChange={handleChange}
              placeholder="Enter manufacturer"
              readOnly={isReadOnly}
            />
          </div>
        </div>

        {/* Medicine-specific fields */}
        {formData.type === 'medicine' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="dosage_form">Dosage Form</Label>
              <Select 
                value={formData.dosage_form} 
                onValueChange={(value) => handleSelectChange('dosage_form', value)}
                disabled={isReadOnly}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select dosage form" />
                </SelectTrigger>
                <SelectContent>
                  {dosageForms.map(form => (
                    <SelectItem key={form} value={form}>{form}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="strength">Strength</Label>
              <Input
                id="strength"
                name="strength"
                value={formData.strength}
                onChange={handleChange}
                placeholder="e.g., 500mg, 10ml"
                readOnly={isReadOnly}
              />
            </div>
          </div>
        )}

        {/* Inventory Details */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label htmlFor="unit">Unit *</Label>
            <Select 
              value={formData.unit} 
              onValueChange={(value) => handleSelectChange('unit', value)}
              disabled={isReadOnly}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select unit" />
              </SelectTrigger>
              <SelectContent>
                {units.map(unit => (
                  <SelectItem key={unit} value={unit}>{unit}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="unit_price">Unit Price ($) *</Label>
            <Input
              id="unit_price"
              name="unit_price"
              type="number"
              step="0.01"
              min="0"
              value={formData.unit_price}
              onChange={handleNumberChange}
              placeholder="0.00"
              required
              readOnly={isReadOnly}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="stock_quantity">Stock Quantity *</Label>
            <Input
              id="stock_quantity"
              name="stock_quantity"
              type="number"
              min="0"
              value={formData.stock_quantity}
              onChange={handleNumberChange}
              placeholder="0"
              required
              readOnly={isReadOnly}
            />
          </div>
        </div>

        {/* Additional Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="reorder_level">Reorder Level</Label>
            <Input
              id="reorder_level"
              name="reorder_level"
              type="number"
              min="0"
              value={formData.reorder_level}
              onChange={handleNumberChange}
              placeholder="10"
              readOnly={isReadOnly}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="batch_number">Batch Number</Label>
            <Input
              id="batch_number"
              name="batch_number"
              value={formData.batch_number}
              onChange={handleChange}
              placeholder="Enter batch number"
              readOnly={isReadOnly}
            />
          </div>
        </div>

        {/* Expiry Date and Supplier */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label>Expiry Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !expiryDate && "text-muted-foreground"
                  )}
                  disabled={isReadOnly}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {expiryDate ? format(expiryDate, "PPP") : "Pick a date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={expiryDate}
                  onSelect={setExpiryDate}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="supplier">Supplier</Label>
            <Select 
              value={formData.supplier} 
              onValueChange={(value) => handleSelectChange('supplier', value)}
              disabled={isReadOnly}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select supplier" />
              </SelectTrigger>
              <SelectContent>
                {suppliers.map(supplier => (
                  <SelectItem key={supplier} value={supplier}>{supplier}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Location and Description */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="location">Storage Location</Label>
            <Input
              id="location"
              name="location"
              value={formData.location}
              onChange={handleChange}
              placeholder="e.g., Shelf A1, Room B"
              readOnly={isReadOnly}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              placeholder="Additional notes or description"
              readOnly={isReadOnly}
              rows={3}
            />
          </div>
        </div>

        {/* Form Actions */}
        {!isReadOnly && (
          <div className="flex justify-end space-x-4 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button 
              type="submit" 
              className="bg-blue-600 hover:bg-blue-700 text-white"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : (action === 'edit' ? 'Update Product' : 'Add Product')}
            </Button>
          </div>
        )}
      </form>
    </div>
  );
};
