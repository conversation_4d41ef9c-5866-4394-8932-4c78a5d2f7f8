
import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Users, 
  Stethoscope, 
  FlaskConical, 
  Pill, 
  CheckCircle,
  ArrowRight
} from 'lucide-react';
import { useWorkflowData } from '@/hooks/useWorkflowData';
import { useNavigate } from 'react-router-dom';

interface WorkflowIntegrationProps {
  patient: any;
}

export const WorkflowIntegration = ({ patient }: WorkflowIntegrationProps) => {
  const navigate = useNavigate();
  const { usePatientWorkflows } = useWorkflowData();
  const { data: workflows = [] } = usePatientWorkflows();

  // Check if patient already has an active workflow
  const existingWorkflow = workflows.find(w => w.patient_id === patient.id);

  const getDepartmentIcon = (department: string) => {
    switch (department) {
      case 'reception': return Users;
      case 'consultation': return Stethoscope;
      case 'laboratory': return FlaskConical;
      case 'pharmacy': return Pill;
      case 'completed': return CheckCircle;
      default: return Users;
    }
  };

  const getDepartmentColor = (department: string) => {
    switch (department) {
      case 'reception': return 'bg-blue-100 text-blue-800';
      case 'consultation': return 'bg-green-100 text-green-800';
      case 'laboratory': return 'bg-orange-100 text-orange-800';
      case 'pharmacy': return 'bg-purple-100 text-purple-800';
      case 'completed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };



  if (existingWorkflow) {
    const Icon = getDepartmentIcon(existingWorkflow.current_department);
    
    return (
      <Card className="border-l-4 border-l-blue-500">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm flex items-center justify-between">
            <span>Active Workflow</span>
            <Badge className={getDepartmentColor(existingWorkflow.current_department)}>
              <Icon className="h-3 w-3 mr-1" />
              {existingWorkflow.current_department}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-2">
            {existingWorkflow.assigned_to && (
              <p className="text-sm text-gray-600">
                Assigned to: <span className="font-medium">{existingWorkflow.assigned_to}</span>
              </p>
            )}
            <p className="text-xs text-gray-500">
              Started: {new Date(existingWorkflow.created_at).toLocaleDateString()}
            </p>
            <Button 
              size="sm" 
              variant="outline" 
              className="w-full mt-2"
              onClick={() => window.open('/workflow', '_blank')}
            >
              <ArrowRight className="h-3 w-3 mr-1" />
              View in Workflow
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // No workflow exists - show automatic workflow info
  return (
    <Card className="border-l-4 border-l-green-500 bg-green-50">
      <CardContent className="p-4">
        <div className="flex items-center space-x-3">
          <div className="p-2 rounded-full bg-green-100">
            <CheckCircle className="h-4 w-4 text-green-600" />
          </div>
          <div>
            <p className="text-sm font-medium text-green-900">
              Ready for Workflow
            </p>
            <p className="text-xs text-green-700">
              Patient will automatically enter consultation workflow
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
