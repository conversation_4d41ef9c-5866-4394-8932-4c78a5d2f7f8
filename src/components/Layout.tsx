
import React, { useState, useEffect } from 'react';
import { Sidebar } from './Sidebar';
import { TopBar } from './TopBar';
import { MobileBottomNav } from './MobileBottomNav';
import { DemoUserWelcomePopup } from './DemoUserWelcomePopup';
import { Menu, X, MessageSquare } from 'lucide-react';
import { useLocation, Link } from 'react-router-dom';
import { Footer as LandingFooter } from './landing/Footer';
import { LandingHeader } from './landing/LandingHeader';

interface LayoutProps {
  children: React.ReactNode;
}

// Custom hook for detecting screen size
const useMediaQuery = (query: string) => {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    const media = window.matchMedia(query);
    if (media.matches !== matches) {
      setMatches(media.matches);
    }

    const listener = () => setMatches(media.matches);
    media.addEventListener('change', listener);
    
    return () => media.removeEventListener('change', listener);
  }, [matches, query]);

  return matches;
}

export const Layout = ({ children }: LayoutProps) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const isMobile = useMediaQuery('(max-width: 768px)');
  const location = useLocation();
  
  // Define routes where the landing layout should be used (these are also the routes where bottom nav should NOT be shown)
  const publicRoutes = ['/', '/auth', '/login', '/register', '/about-us', '/features', '/contact-us', '/demo-login'];

  // Check if current route is a public route
  const isPublicRoute = publicRoutes.includes(location.pathname);

  // Close sidebar when transitioning from mobile to desktop
  useEffect(() => {
    if (!isMobile) {
      setSidebarOpen(false);
    }
  }, [isMobile]);

  // For public pages, use a different layout without sidebar
  if (isPublicRoute) {
    return (
      <div className="relative flex flex-col min-h-screen bg-white overflow-hidden">
        {/* Landing Header for public pages */}
        <LandingHeader />
        
        {/* Main content */}
        <main className="flex-1 overflow-x-hidden overflow-y-auto">
          <div className="mx-auto w-full">
            {children}
          </div>
        </main>
        
        {/* Footer for public pages */}
        <LandingFooter />
        
        {/* Mobile bottom navigation - never shown on public pages */}
        
        {/* Floating Support Icon for public pages */}
        <div className="fixed bottom-20 right-4 z-50 group">
          <Link to="/contact-us" className="flex items-center">
            <button className="bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full shadow-lg transition-colors duration-200 hover:scale-110 transform">
              <MessageSquare className="h-6 w-6" />
            </button>
            <span className="hidden group-hover:block ml-2 text-white bg-blue-600 p-2 rounded-md whitespace-nowrap transition-opacity duration-200 opacity-0 group-hover:opacity-100">
              Support
            </span>
          </Link>
        </div>
      </div>
    );
  }
  
  // For admin/dashboard pages, use the original layout with sidebar
  return (
    <div className="relative flex h-screen bg-gray-50 overflow-hidden">
      {/* Mobile sidebar backdrop */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 bg-gray-900/50 z-20 lg:hidden" 
          onClick={() => setSidebarOpen(false)}
          aria-hidden="true"
        />
      )}
      
      {/* Sidebar - hidden on mobile unless toggled */}
      <div 
        className={`
          ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
          fixed inset-y-0 left-0 z-30 w-64 transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:z-auto
        `}
      >
        <Sidebar onCloseMobile={() => setSidebarOpen(false)} />
      </div>
      
      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <TopBar onMenuClick={() => setSidebarOpen(true)} />

        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 p-4 md:p-6 pb-4 md:pb-6">
          <div className="mx-auto w-full max-w-7xl">
            {children}
          </div>
        </main>
      </div>

      {/* Demo User Welcome Popup - only <NAME_EMAIL> user */}
      <DemoUserWelcomePopup />
      
      {/* Mobile bottom navigation - only on dashboard/protected pages */}
      {isMobile && <MobileBottomNav />}
      
      {/* Floating Support Icon for protected pages */}
      <div className="fixed bottom-20 right-4 z-50 group">
        <Link to="/contact-us" className="flex items-center">
          <button className="bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full shadow-lg transition-colors duration-200 hover:scale-110 transform">
            <MessageSquare className="h-6 w-6" />
          </button>
          <span className="hidden group-hover:block ml-2 text-white bg-blue-600 p-2 rounded-md whitespace-nowrap transition-opacity duration-200 opacity-0 group-hover:opacity-100">
            Support
          </span>
        </Link>
      </div>
    </div>
  );
};
