import React, { createContext, useContext, useEffect, useState } from 'react';
import { useSupabaseData } from '@/hooks/useSupabaseData';

interface CurrencyContextType {
  currency: string;
  currencySymbol: string;
  formatCurrency: (amount: number) => string;
  setCurrency: (currency: string) => void;
  isLoading: boolean;
}

const CurrencyContext = createContext<CurrencyContextType | undefined>(undefined);

// Currency symbols mapping
const currencySymbols: Record<string, string> = {
  KES: 'KSh',
  NGN: '₦',
  ZAR: 'R',
  EGP: 'E£',
  MAD: 'DH',
  TND: 'DT',
  GHS: 'GH₵',
  UGX: 'USh',
  TZS: 'TSh',
  ETB: 'Br',
  RWF: 'RF',
  XOF: 'CFA',
  XAF: 'FCFA',
  BWP: 'P',
  MUR: '₨',
  USD: '$',
  EUR: '€',
  GBP: '£',
};

// Locale mapping for proper number formatting
const currencyLocales: Record<string, string> = {
  KES: 'en-KE',
  NGN: 'en-NG',
  ZAR: 'en-ZA',
  EGP: 'ar-EG',
  MAD: 'ar-MA',
  TND: 'ar-TN',
  GHS: 'en-GH',
  UGX: 'en-UG',
  TZS: 'en-TZ',
  ETB: 'en-ET',
  RWF: 'en-RW',
  XOF: 'fr-SN',
  XAF: 'fr-CM',
  BWP: 'en-BW',
  MUR: 'en-MU',
  USD: 'en-US',
  EUR: 'en-EU',
  GBP: 'en-GB',
};

interface CurrencyProviderProps {
  children: React.ReactNode;
}

export const CurrencyProvider: React.FC<CurrencyProviderProps> = ({ children }) => {
  const [currency, setCurrencyState] = useState<string>('KES');
  const [isLoading, setIsLoading] = useState(true);
  const { useSettings } = useSupabaseData();
  const { data: settings, isLoading: settingsLoading } = useSettings();

  // Load currency from settings
  useEffect(() => {
    if (!settingsLoading) {
      if (settings?.currency) {
        setCurrencyState(settings.currency);
      }
      setIsLoading(false);
    }
  }, [settings, settingsLoading]);

  const setCurrency = (newCurrency: string) => {
    setCurrencyState(newCurrency);
    // Force re-render of components using currency
    window.dispatchEvent(new CustomEvent('currencyChanged', { detail: newCurrency }));
  };

  const currencySymbol = currencySymbols[currency] || currency;

  const formatCurrency = (amount: number): string => {
    const locale = currencyLocales[currency] || 'en-US';
    
    try {
      // For currencies that don't have built-in Intl support, use custom formatting
      if (['XOF', 'XAF', 'RWF', 'UGX', 'TZS', 'ETB'].includes(currency)) {
        const formattedNumber = new Intl.NumberFormat(locale, {
          minimumFractionDigits: 0,
          maximumFractionDigits: 2,
        }).format(amount);
        return `${currencySymbol} ${formattedNumber}`;
      }
      
      // Use standard Intl.NumberFormat for supported currencies
      return new Intl.NumberFormat(locale, {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 0,
        maximumFractionDigits: 2,
      }).format(amount);
    } catch (error) {
      // Fallback formatting if Intl fails
      const formattedNumber = new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2,
      }).format(amount);
      return `${currencySymbol} ${formattedNumber}`;
    }
  };

  const value: CurrencyContextType = {
    currency,
    currencySymbol,
    formatCurrency,
    setCurrency,
    isLoading,
  };

  return (
    <CurrencyContext.Provider value={value}>
      {children}
    </CurrencyContext.Provider>
  );
};

export const useCurrency = (): CurrencyContextType => {
  const context = useContext(CurrencyContext);
  if (context === undefined) {
    throw new Error('useCurrency must be used within a CurrencyProvider');
  }
  return context;
};
