// Settings Types Definition
export interface Settings {
  id?: string;
  user_id?: string;
  
  // General Hospital Information
  hospital_name?: string;
  hospital_code?: string;
  address?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
  phone_number?: string;
  email?: string;
  website?: string;
  logo_url?: string;
  
  // Currency and Localization
  currency?: string;
  timezone?: string;
  date_format?: string;
  time_format?: string;
  language?: string;
  
  // Business Settings
  business_hours_start?: string;
  business_hours_end?: string;
  working_days?: string[];
  appointment_duration?: number;
  max_appointments_per_day?: number;
  
  // Financial Settings
  tax_rate?: number;
  tax_name?: string;
  invoice_prefix?: string;
  receipt_prefix?: string;
  auto_invoice_numbering?: boolean;
  payment_terms_days?: number;
  
  // Notification Settings
  email_notifications?: boolean;
  sms_notifications?: boolean;
  appointment_reminders?: boolean;
  reminder_hours_before?: number;
  low_stock_alerts?: boolean;
  low_stock_threshold?: number;
  
  // Security Settings
  session_timeout_minutes?: number;
  password_expiry_days?: number;
  max_login_attempts?: number;
  two_factor_auth?: boolean;
  
  // Pharmacy Settings
  pharmacy_enabled?: boolean;
  auto_deduct_inventory?: boolean;
  require_prescription?: boolean;
  pharmacy_markup_percentage?: number;
  
  // Laboratory Settings
  lab_enabled?: boolean;
  auto_generate_lab_numbers?: boolean;
  lab_result_approval_required?: boolean;
  lab_report_template?: string;
  
  // Consultation Settings
  consultation_enabled?: boolean;
  require_appointment?: boolean;
  allow_walk_ins?: boolean;
  consultation_fee?: number;
  
  // Backup and Data Settings
  auto_backup_enabled?: boolean;
  backup_frequency?: string;
  data_retention_months?: number;
  
  // Integration Settings
  paystack_public_key?: string;
  paystack_secret_key?: string;
  sms_api_key?: string;
  email_smtp_host?: string;
  email_smtp_port?: number;
  email_smtp_username?: string;
  email_smtp_password?: string;
  
  // Custom Fields
  custom_fields?: Record<string, any>;
  
  // Audit Fields
  created_at?: string;
  updated_at?: string;
}

export interface SettingsFormData {
  // General
  hospital_name: string;
  hospital_code: string;
  address: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  phone_number: string;
  email: string;
  website: string;
  logo_url: string;
  
  // Localization
  currency: string;
  timezone: string;
  date_format: string;
  time_format: string;
  language: string;
  
  // Business
  business_hours_start: string;
  business_hours_end: string;
  working_days: string[];
  appointment_duration: number;
  max_appointments_per_day: number;
  
  // Financial
  tax_rate: number;
  tax_name: string;
  invoice_prefix: string;
  receipt_prefix: string;
  auto_invoice_numbering: boolean;
  payment_terms_days: number;
  
  // Notifications
  email_notifications: boolean;
  sms_notifications: boolean;
  appointment_reminders: boolean;
  reminder_hours_before: number;
  low_stock_alerts: boolean;
  low_stock_threshold: number;
  
  // Security
  session_timeout_minutes: number;
  password_expiry_days: number;
  max_login_attempts: number;
  two_factor_auth: boolean;
  
  // Modules
  pharmacy_enabled: boolean;
  auto_deduct_inventory: boolean;
  require_prescription: boolean;
  pharmacy_markup_percentage: number;
  lab_enabled: boolean;
  auto_generate_lab_numbers: boolean;
  lab_result_approval_required: boolean;
  lab_report_template: string;
  consultation_enabled: boolean;
  require_appointment: boolean;
  allow_walk_ins: boolean;
  consultation_fee: number;
  
  // Data & Backup
  auto_backup_enabled: boolean;
  backup_frequency: string;
  data_retention_months: number;
  
  // Integrations
  paystack_public_key: string;
  paystack_secret_key: string;
  sms_api_key: string;
  email_smtp_host: string;
  email_smtp_port: number;
  email_smtp_username: string;
  email_smtp_password: string;
}

export const defaultSettings: Partial<SettingsFormData> = {
  hospital_name: 'My Hospital',
  country: 'Kenya',
  currency: 'KES',
  timezone: 'Africa/Nairobi',
  date_format: 'DD/MM/YYYY',
  time_format: '24h',
  language: 'en',
  business_hours_start: '08:00',
  business_hours_end: '18:00',
  working_days: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
  appointment_duration: 30,
  max_appointments_per_day: 50,
  tax_rate: 0,
  tax_name: 'VAT',
  invoice_prefix: 'INV',
  receipt_prefix: 'RCP',
  auto_invoice_numbering: true,
  payment_terms_days: 30,
  email_notifications: true,
  sms_notifications: false,
  appointment_reminders: true,
  reminder_hours_before: 24,
  low_stock_alerts: true,
  low_stock_threshold: 10,
  session_timeout_minutes: 480,
  password_expiry_days: 90,
  max_login_attempts: 5,
  two_factor_auth: false,
  pharmacy_enabled: true,
  auto_deduct_inventory: true,
  require_prescription: false,
  pharmacy_markup_percentage: 0,
  lab_enabled: true,
  auto_generate_lab_numbers: true,
  lab_result_approval_required: true,
  consultation_enabled: true,
  require_appointment: false,
  allow_walk_ins: true,
  consultation_fee: 0,
  auto_backup_enabled: false,
  backup_frequency: 'weekly',
  data_retention_months: 60,
  email_smtp_port: 587
};

export const currencies = [
  { code: 'KES', name: 'Kenyan Shilling', symbol: 'KSh' },
  { code: 'USD', name: 'US Dollar', symbol: '$' },
  { code: 'EUR', name: 'Euro', symbol: '€' },
  { code: 'GBP', name: 'British Pound', symbol: '£' },
  { code: 'UGX', name: 'Ugandan Shilling', symbol: 'USh' },
  { code: 'TZS', name: 'Tanzanian Shilling', symbol: 'TSh' },
  { code: 'RWF', name: 'Rwandan Franc', symbol: 'RF' },
  { code: 'ETB', name: 'Ethiopian Birr', symbol: 'Br' },
  { code: 'ZAR', name: 'South African Rand', symbol: 'R' },
  { code: 'NGN', name: 'Nigerian Naira', symbol: '₦' }
];

export const timezones = [
  { value: 'Africa/Nairobi', label: 'East Africa Time (EAT)' },
  { value: 'Africa/Lagos', label: 'West Africa Time (WAT)' },
  { value: 'Africa/Cairo', label: 'Eastern European Time (EET)' },
  { value: 'Africa/Johannesburg', label: 'South Africa Standard Time (SAST)' },
  { value: 'UTC', label: 'Coordinated Universal Time (UTC)' }
];

export const languages = [
  { code: 'en', name: 'English' },
  { code: 'sw', name: 'Swahili' },
  { code: 'fr', name: 'French' },
  { code: 'ar', name: 'Arabic' }
];

export const dateFormats = [
  { value: 'DD/MM/YYYY', label: 'DD/MM/YYYY (31/12/2024)' },
  { value: 'MM/DD/YYYY', label: 'MM/DD/YYYY (12/31/2024)' },
  { value: 'YYYY-MM-DD', label: 'YYYY-MM-DD (2024-12-31)' },
  { value: 'DD-MM-YYYY', label: 'DD-MM-YYYY (31-12-2024)' }
];

export const timeFormats = [
  { value: '24h', label: '24 Hour (14:30)' },
  { value: '12h', label: '12 Hour (2:30 PM)' }
];

export const backupFrequencies = [
  { value: 'daily', label: 'Daily' },
  { value: 'weekly', label: 'Weekly' },
  { value: 'monthly', label: 'Monthly' }
];

export const workingDaysOptions = [
  'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'
];
