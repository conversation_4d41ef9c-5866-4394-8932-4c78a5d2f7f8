
import React, { useState } from 'react';
import { Layout } from '../components/Layout';
import { Search, Plus, MoreVertical } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useSupabaseData } from '@/hooks/useSupabaseData';
import { useToast } from '@/hooks/use-toast';

// Department Form component
const DepartmentForm = ({ onClose }) => {
  const { createDepartment } = useSupabaseData();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    const formData = new FormData(e.target);
    const departmentData = {
      department_name: formData.get('name'),
      department_head: formData.get('head'),
      phone_number: formData.get('phone'),
      email_address: formData.get('email'),
      description: formData.get('services'),
    };

    try {
      await createDepartment.mutateAsync(departmentData);
      onClose();
    } catch (error) {
      console.error('Error saving department:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to save department",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="space-y-2">
        <h2 className="text-2xl font-bold">Add New Department</h2>
        <p className="text-gray-500">Enter the details for the new department.</p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="name">Department Name</Label>
            <Input
              id="name"
              name="name"
              placeholder="e.g., Cardiology"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="head">Department Head</Label>
            <Input
              id="head"
              name="head"
              placeholder="e.g., Dr. John Smith"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="phone">Phone Number</Label>
            <Input
              id="phone"
              name="phone"
              type="tel"
              placeholder="e.g., (*************"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">Email Address</Label>
            <Input
              id="email"
              name="email"
              type="email"
              placeholder="e.g., <EMAIL>"
              required
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="services">Services Description</Label>
          <Textarea
            id="services"
            name="services"
            placeholder="Describe the services provided by this department..."
            className="min-h-[100px]"
            required
          />
        </div>

        <div className="flex justify-end space-x-4 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? 'Adding...' : 'Add Department'}
          </Button>
        </div>
      </form>
    </div>
  );
};

const Departments = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isFormOpen, setIsFormOpen] = useState(false);

  const { useDepartments } = useSupabaseData();
  const { data: departments = [], isLoading, error } = useDepartments();

  // Filter departments based on search term
  const filteredDepartments = departments.filter(department =>
    department.department_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    department.department_head?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    department.id?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (isLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg text-gray-600">Loading departments...</div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg text-red-600">Error loading departments: {error.message}</div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Departments</h1>
            <p className="text-gray-600 mt-1">Manage hospital departments and services</p>
          </div>
          <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
            <DialogTrigger asChild>
              <Button className="bg-indigo-600 hover:bg-indigo-700 text-white">
                <Plus className="mr-2 h-4 w-4" />
                New Department
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
              <DepartmentForm onClose={() => setIsFormOpen(false)} />
            </DialogContent>
          </Dialog>
        </div>

        {/* Search and Filter */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <Input
              type="text"
              placeholder="Search departments by name, head, or ID..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* No departments message */}
        {filteredDepartments.length === 0 && !isLoading && (
          <Card className="p-8 text-center">
            <h3 className="text-lg font-medium text-gray-900 mb-2">No departments found</h3>
            <p className="text-gray-500 mb-4">
              {searchTerm ? 'Try adjusting your search terms.' : 'Get started by adding your first department.'}
            </p>
            <Button onClick={() => setIsFormOpen(true)} className="bg-indigo-600 hover:bg-indigo-700">
              <Plus className="mr-2 h-4 w-4" />
              Add Department
            </Button>
          </Card>
        )}

        {/* Departments Table */}
        {filteredDepartments.length > 0 && (
          <Card className="shadow-sm">
            <CardHeader>
              <CardTitle>Departments List</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>ID</TableHead>
                      <TableHead>NAME</TableHead>
                      <TableHead>HEAD</TableHead>
                      <TableHead>CONTACT</TableHead>
                      <TableHead>SERVICES</TableHead>
                      <TableHead>ACTIONS</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredDepartments.map((department) => (
                      <TableRow key={department.id}>
                        <TableCell className="font-medium">{department.id}</TableCell>
                        <TableCell>{department.department_name}</TableCell>
                        <TableCell>{department.department_head}</TableCell>
                        <TableCell>
                          <p>{department.phone_number}</p>
                          <p className="text-sm text-gray-500">{department.email_address}</p>
                        </TableCell>
                        <TableCell className="text-sm text-gray-600 max-w-xs overflow-hidden text-ellipsis whitespace-nowrap">{department.description}</TableCell>
                        <TableCell>
                           <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => alert('View details for ' + department.department_name)}>
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => alert('Edit ' + department.department_name)}>
                                Edit Department
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem onClick={() => alert('Delete ' + department.department_name)} className="text-red-600">
                                Delete Department
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </Layout>
  );
};

export default Departments;
