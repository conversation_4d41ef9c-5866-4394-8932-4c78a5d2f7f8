import React from 'react';
import { Layout } from '../components/Layout';

const Blog = () => {
  // Placeholder data for blog posts
  const blogPosts = [
    {
      id: 1,
      title: 'Improving Patient Care with VertiQ',
      summary: 'Discover how VertiQ streamlines workflows and enhances patient engagement.',
      date: 'October 26, 2023',
      // Add author, image, etc. later
    },
    {
      id: 2,
      title: 'The Future of Hospital Management',
      summary: 'An outlook on the role of technology in modern healthcare facilities.',
      date: 'October 20, 2023',
    },
    {
      id: 3,
      title: 'Maximizing Efficiency in Healthcare Operations',
      summary: 'Tips and strategies for optimizing administrative and clinical processes.',
      date: 'October 15, 2023',
    },
  ];

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        {/* Blog Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">VertiQ Blog</h1>
          <p className="text-xl text-gray-600">Insights, updates, and perspectives on healthcare technology.</p>
        </div>

        {/* Blog Posts List */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {blogPosts.map((post) => (
            <div key={post.id} className="bg-white p-6 rounded-lg shadow-md">
              <h2 className="text-2xl font-semibold text-gray-800 mb-2">{post.title}</h2>
              <p className="text-gray-600 mb-4">{post.summary}</p>
              <p className="text-sm text-gray-500 mb-4">Published on {post.date}</p>
              {/* Add a link to read the full post later */}
              <a href="#" className="text-purple-600 hover:underline font-medium">Read More</a>
            </div>
          ))}
        </div>
      </div>
    </Layout>
  );
};

export default Blog; 