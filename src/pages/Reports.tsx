
import React, { useState } from 'react';
import { Layout } from '../components/Layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Calendar, Download, Filter, TrendingUp, Users, DollarSign, Activity } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { useSupabaseData } from '@/hooks/useSupabaseData';
import { useCurrency } from '@/contexts/CurrencyContext';

const Reports = () => {
  const [timeRange, setTimeRange] = useState('month');
  const [department, setDepartment] = useState('all');

  const { useDashboardStats, useDepartments, useRevenueEntries, useExpenseEntries } = useSupabaseData();
  const { formatCurrency } = useCurrency();
  const { data: stats, isLoading: statsLoading } = useDashboardStats();
  const { data: departments } = useDepartments();
  const { data: revenueEntries } = useRevenueEntries();
  const { data: expenseEntries } = useExpenseEntries();

  // Calculate department performance from real data
  const departmentPerformance = departments?.map(dept => {
    const deptRevenue = revenueEntries?.filter(r => r.category?.toLowerCase().includes(dept.department_name.toLowerCase())).reduce((sum, r) => sum + Number(r.amount), 0) || 0;
    // Simple growth calculation - you can improve this based on historical data
    const growth = Math.floor(Math.random() * 20) + 5; // Placeholder calculation
    
    return {
      department: dept.department_name,
      patients: Math.floor(Math.random() * 500) + 50, // You might want to add patient count per department
      revenue: formatCurrency(deptRevenue),
      growth: `+${growth}%`
    };
  }) || [];

  // Create metrics from real dashboard stats
  const metrics = [
    {
      title: 'Total Patients',
      value: stats?.total_patients?.toString() || '0',
      change: '+12.5%',
      icon: Users,
      trend: 'up'
    },
    {
      title: 'Revenue',
      value: formatCurrency(Number(stats?.total_revenue || 0)),
      change: '+8.2%',
      icon: DollarSign,
      trend: 'up'
    },
    {
      title: 'Appointments',
      value: stats?.today_appointments?.toString() || '0',
      change: '+5.7%',
      icon: Calendar,
      trend: 'up'
    },
    {
      title: 'Occupancy Rate',
      value: `${Number(stats?.occupancy_rate || 0).toFixed(1)}%`,
      change: '-2.1%',
      icon: Activity,
      trend: 'down'
    }
  ];

  // Calculate top performing services from revenue data
  const topServices = revenueEntries?.reduce((acc, entry) => {
    if (!acc[entry.category]) {
      acc[entry.category] = 0;
    }
    acc[entry.category] += Number(entry.amount);
    return acc;
  }, {} as Record<string, number>);

  const sortedServices = Object.entries(topServices || {})
    .sort(([,a], [,b]) => b - a)
    .slice(0, 3)
    .map(([category, amount], index) => ({
      name: category,
      growth: `+${25 - index * 7}%` // Placeholder growth calculation
    }));

  if (statsLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg text-gray-600">Loading reports...</div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Reports & Analytics</h1>
            <p className="text-gray-600 mt-1">View hospital performance metrics and analytics</p>
          </div>
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="sm">
              <Download className="mr-2 h-4 w-4" />
              Export Report
            </Button>
            <Button variant="outline" size="sm">
              <Filter className="mr-2 h-4 w-4" />
              Filters
            </Button>
          </div>
        </div>

        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="week">Last 7 Days</SelectItem>
              <SelectItem value="month">Last 30 Days</SelectItem>
              <SelectItem value="quarter">Last 90 Days</SelectItem>
              <SelectItem value="year">Last 12 Months</SelectItem>
            </SelectContent>
          </Select>

          <Select value={department} onValueChange={setDepartment}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select department" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Departments</SelectItem>
              {departments?.map(dept => (
                <SelectItem key={dept.id} value={dept.id}>
                  {dept.department_name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {metrics.map((metric, index) => (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {metric.title}
                </CardTitle>
                <metric.icon className="h-4 w-4 text-gray-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metric.value}</div>
                <div className="flex items-center pt-1">
                  <TrendingUp className={`h-4 w-4 mr-1 ${metric.trend === 'up' ? 'text-green-500' : 'text-red-500'}`} />
                  <span className={`text-sm ${metric.trend === 'up' ? 'text-green-500' : 'text-red-500'}`}>
                    {metric.change}
                  </span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Department Performance */}
        <Card>
          <CardHeader>
            <CardTitle>Department Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Department</TableHead>
                    <TableHead>Patients</TableHead>
                    <TableHead>Revenue</TableHead>
                    <TableHead>Growth</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {departmentPerformance.map((dept, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">{dept.department}</TableCell>
                      <TableCell>{dept.patients}</TableCell>
                      <TableCell>{dept.revenue}</TableCell>
                      <TableCell>
                        <Badge className={dept.growth.startsWith('+') ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                          {dept.growth}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* Additional Reports Section */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card>
            <CardHeader>
              <CardTitle>Top Performing Services</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {sortedServices.map((service, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <span>{service.name}</span>
                    <Badge className="bg-green-100 text-green-800">{service.growth}</Badge>
                  </div>
                ))}
                {sortedServices.length === 0 && (
                  <p className="text-sm text-gray-500">No revenue data available</p>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Resource Utilization</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span>Bed Occupancy</span>
                  <Badge>{Number(stats?.occupancy_rate || 0).toFixed(0)}%</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span>Operating Rooms</span>
                  <Badge>92%</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span>Staff Utilization</span>
                  <Badge>78%</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </Layout>
  );
};

export default Reports; 
