import React from 'react';
import { Layout } from '../components/Layout';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import {
  Users,
  Calendar,
  DollarSign,
  FileText,
  FlaskConical,
  Package,
  Settings,
  Stethoscope,
  BriefcaseMedical,
  MessageSquare,
  BarChart2,
  ShieldCheck,
  Clock,
  CreditCard,
  BookOpen,
  ClipboardList,
  Hospital,
  LocateFixed,
  Bell,
  Mail,
  CheckCircle,
  ArrowRight
} from 'lucide-react';
import { CTASection } from '../components/landing/CTASection';

const Features = () => {
  const featureItems = [
    {
      icon: Users,
      title: 'Patient Management',
      description: 'Comprehensive patient profiles, history, and demographics.',
    },
    {
      icon: Calendar,
      title: 'Appointment Scheduling',
      description: 'Easy booking, rescheduling, and cancellation of appointments with reminders.',
    },
    {
      icon: Stethoscope,
      title: 'Doctor Management',
      description: 'Manage doctor schedules, specializations, and availability.',
    },
    {
      icon: BriefcaseMedical,
      title: 'Electronic Medical Records (EMR)',
      description: 'Secure storage and easy access to patient medical history, diagnoses, and treatment plans.',
    },
    {
      icon: DollarSign,
      title: 'Billing & Accounting',
      description: 'Invoice generation, payment tracking, revenue/expense management, and financial reporting.',
    },
    {
      icon: FileText,
      title: 'Medical Records',
      description: 'Organized storage and retrieval of all patient medical documents and reports.',
    },
    {
      icon: FlaskConical,
      title: 'Lab Tests Management',
      description: 'Order, track, and manage lab tests and results.',
    },
    {
      icon: Package,
      title: 'Pharmacy Management',
      description: 'Manage medication inventory, prescriptions, and dispensing.',
    },
    {
      icon: BarChart2,
      title: 'Reporting & Analytics',
      description: 'Generate detailed reports on various aspects of hospital operations for informed decision-making.',
    },
    {
      icon: Settings,
      title: 'Settings & Configuration',
      description: 'Customize system settings, user roles, and permissions.',
    },
    {
      icon: MessageSquare,
      title: 'Internal Messaging',
      description: 'Secure communication channel for staff members.',
    },
    {
      icon: Hospital,
      title: 'Department Management',
      description: 'Organize and manage different hospital departments.',
    },
    {
      icon: Clock,
      title: 'Real-time Updates',
      description: 'Stay informed with instant notifications and updates.',
    },
    {
      icon: ShieldCheck,
      title: 'Data Security',
      description: 'Robust security measures to protect sensitive patient and operational data.',
    },
    {
      icon: LocateFixed,
      title: 'Location Tracking',
      description: '(Optional) Track assets or personnel within the facility.',
    },
    {
      icon: Bell,
      title: 'Notifications',
      description: 'Automated notifications for appointments, payments, and important events.',
    },
    {
      icon: Mail,
      title: 'Email Integration',
      description: 'Send emails for appointments, billing, and communication directly from the system.',
    },
  ];

  // Group features into categories for better organization
  const featureCategories = [
    {
      name: 'Core Management',
      description: 'Essential features for day-to-day hospital operations',
      items: featureItems.slice(0, 6)
    },
    {
      name: 'Advanced Capabilities',
      description: 'Specialized tools for comprehensive healthcare management',
      items: featureItems.slice(6, 12)
    },
    {
      name: 'Security & Communication',
      description: 'Features that keep your data safe and your team connected',
      items: featureItems.slice(12)
    }
  ];

  return (
    <Layout>
      {/* Hero Section */}
      <div className="bg-gradient-to-b from-purple-50 to-white py-16">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto">
            <div className="inline-block bg-purple-100 text-purple-800 px-4 py-1 rounded-full text-sm font-medium mb-6">Features</div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">Powerful Features for Modern Healthcare</h1>
            <p className="text-xl text-gray-600 mb-8">VertiQ offers a comprehensive suite of tools designed specifically for healthcare providers.</p>
            <div className="flex flex-wrap justify-center gap-4">
              <Link to="/pricing">
                <Button className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2">View Pricing</Button>
              </Link>
              <Link to="/contact-us">
                <Button variant="outline" className="border-purple-200 text-purple-700 hover:bg-purple-50 px-6 py-2">Request Demo</Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Feature Categories */}
      <div className="py-16 bg-white">
        <div className="container mx-auto px-4">
          {featureCategories.map((category, categoryIndex) => (
            <div key={categoryIndex} className="mb-16 last:mb-0">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-gray-900 mb-3">{category.name}</h2>
                <p className="text-xl text-gray-600 max-w-2xl mx-auto">{category.description}</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
                {category.items.map((item, index) => (
                  <div 
                    key={index} 
                    className="bg-white p-8 rounded-xl shadow-md border border-gray-100 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1"
                  >
                    <div className="w-14 h-14 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg flex items-center justify-center mb-6">
                      <item.icon className="h-7 w-7 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-800 mb-3">{item.title}</h3>
                    <p className="text-gray-600 mb-4">{item.description}</p>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Benefits Section */}
      <div className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-3">Why Choose VertiQ?</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">Our platform offers unique advantages for healthcare providers</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
            {[
              {
                title: 'Seamless Integration',
                description: 'Easily integrates with your existing systems and workflows without disruption.'
              },
              {
                title: 'User-Friendly Interface',
                description: 'Intuitive design that requires minimal training for your staff to master.'
              },
              {
                title: 'Scalable Solution',
                description: 'Grows with your facility, from small clinics to large hospital networks.'
              },
              {
                title: 'Dedicated Support',
                description: '24/7 customer support and regular updates to ensure optimal performance.'
              }
            ].map((benefit, index) => (
              <div key={index} className="bg-white p-6 rounded-xl shadow-sm flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <CheckCircle className="h-6 w-6 text-green-500" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-2">{benefit.title}</h3>
                  <p className="text-gray-600">{benefit.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <CTASection />
    </Layout>
  );
};

export default Features; 