import { useState } from 'react';
import { Layout } from '../components/Layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import {
  TestTube,
  Clock,
  Activity,
  Heart,
  Search,
  User,
  Phone,
  Mail,
  Stethoscope
} from 'lucide-react';
import { useWorkflowData } from '@/hooks/useWorkflowData';
import { useSupabaseData } from '@/hooks/useSupabaseData';
import { useCurrency } from '@/contexts/CurrencyContext';
import { LaboratoryForm } from '@/components/workflow/LaboratoryForm';
import { useNavigate } from 'react-router-dom';
import { toast } from '@/hooks/use-toast';

const LaboratoryDepartment = () => {
  const navigate = useNavigate();
  const { formatCurrency } = useCurrency();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPatient, setSelectedPatient] = useState<any>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('unedited'); // 'unedited' or 'edited'

  // Helper function to get department information
  const getDepartmentInfo = (patient: any) => {
    // Handle both flat structure (from reception function) and nested structure (from regular patients query)
    if (patient.department_name) {
      return {
        name: patient.department_name,
        color: patient.department_color,
        icon: patient.department_icon,
        notes: patient.department_notes
      };
    } else if (patient.medical_departments) {
      return {
        name: patient.medical_departments.name,
        color: patient.medical_departments.color,
        icon: patient.medical_departments.icon,
        notes: patient.department_notes
      };
    }
    return null;
  };
  
  const {
    useWorkflowStats,
    useLaboratoryRecords,
    createLaboratoryRecord,
    updateLaboratoryRecord,
    useConsultationRecords
  } = useWorkflowData();
  
  const { usePatients } = useSupabaseData();
  
  const { data: allPatients = [], isLoading: patientsLoading } = usePatients();
  const { data: laboratoryRecords = [], error: labRecordsError } = useLaboratoryRecords();
  const { data: consultationRecords = [] } = useConsultationRecords();

  // Handle laboratory records error
  if (labRecordsError) {
    console.error('Laboratory records error:', labRecordsError);
  }

  // Debug logging (only show errors)
  if (labRecordsError) {
    console.error('Laboratory Records Error:', labRecordsError);
  }

  // Get patients who have completed consultation (eligible for lab tests)
  const patientsWithConsultation = allPatients.filter(patient =>
    Array.isArray(consultationRecords) && consultationRecords.some((record: any) => record.patient_id === patient.id)
  );

  // ALL patients with consultation are shown, but categorized by lab test status
  // Unedited: Patients without lab records (need lab tests)
  const uneditedPatients = patientsWithConsultation.filter(patient =>
    !Array.isArray(laboratoryRecords) || !laboratoryRecords.some((record: any) => record.patient_id === patient.id)
  );

  // Edited: Patients with lab records (lab tests completed)
  const editedPatients = patientsWithConsultation.filter(patient =>
    Array.isArray(laboratoryRecords) && laboratoryRecords.some((record: any) => record.patient_id === patient.id)
  );

  // Filter based on search term
  const filteredUneditedPatients = uneditedPatients.filter(patient =>
    patient.patient_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.phone_number.includes(searchTerm)
  );

  const filteredEditedPatients = editedPatients.filter(patient =>
    patient.patient_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.phone_number.includes(searchTerm)
  );

  const handleStartTest = (patient: any) => {
    setSelectedPatient(patient);
    setIsFormOpen(true);
  };

  const handleSendToFollowUp = (patient: any) => {
    toast({
      title: "Patient Sent to Follow-up",
      description: `${patient.patient_name} has been sent to consultation for follow-up review of lab results.`,
    });

    // Navigate to consultation department to show the patient is ready for follow-up
    setTimeout(() => {
      navigate('/consultation-department');
    }, 1500);
  };

  const handleLaboratoryComplete = async (laboratoryData: any) => {
    if (!selectedPatient) return;

    try {
      // Check if patient already has a laboratory record
      const existingRecord = Array.isArray(laboratoryRecords) ? laboratoryRecords.find((record: any) => record.patient_id === selectedPatient.id) : null;

      console.log('🔍 LABORATORY DEPARTMENT DEBUG:', {
        selectedPatientId: selectedPatient.id,
        existingRecord: existingRecord ? 'found' : 'not found',
        existingRecordId: existingRecord?.id,
        laboratoryRecordsCount: laboratoryRecords.length,
        laboratoryDataKeys: Object.keys(laboratoryData)
      });

      if (existingRecord) {
        // Patient already has laboratory record - update it instead of creating new one
        console.log('🔄 Updating existing laboratory record for patient:', selectedPatient.id);

        await updateLaboratoryRecord.mutateAsync({
          recordId: existingRecord.id,
          labData: {
            ...laboratoryData,
            test_date: new Date().toISOString().split('T')[0],
            updated_at: new Date().toISOString()
          },
          showToast: false // We'll show our own toast
        });

        toast({
          title: "Laboratory Record Updated",
          description: "Laboratory record updated successfully. Redirecting to consultation for follow-up.",
        });

        // Close dialog and navigate to patients
        setIsFormOpen(false);
        setSelectedPatient(null);

        // Auto-navigate to consultation department for follow-up after update
        setTimeout(() => {
          navigate('/consultation-department');
        }, 1500);
        return; // Exit early after successful update
      }

      // Create new laboratory record only if none exists
      console.log('🆕 Creating new laboratory record for patient:', selectedPatient.id);
      await createLaboratoryRecord.mutateAsync({
        patient_id: selectedPatient.id,
        ...laboratoryData,
        test_date: new Date().toISOString().split('T')[0]
      });

      toast({
        title: "Success",
        description: "Laboratory record created successfully! Redirecting to consultation for follow-up.",
      });

      setIsFormOpen(false);
      setSelectedPatient(null);

      // Auto-navigate to consultation department for follow-up after new lab test completion
      setTimeout(() => {
        navigate('/consultation-department');
      }, 1500);

    } catch (error) {
      console.error('Error completing laboratory test:', error);

      // Don't show error toast here since the LaboratoryForm handles its own errors
      // and the operation might actually be succeeding despite the caught error
      // The success toasts above will show if the operation completed successfully
    }
  };

  const getPatientAge = (dateOfBirth: string) => {
    if (!dateOfBirth) return 'Unknown';
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  // Debug logging (only show errors)
  if (labRecordsError) {
    console.error('Laboratory Records Error:', labRecordsError);
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Laboratory Department</h1>
            <p className="text-gray-600 mt-1 text-sm sm:text-base">Medical tests and laboratory analysis → Final records</p>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
            <Button
              variant="outline"
              onClick={() => navigate('/consultation-department')}
              className="w-full sm:w-auto text-sm"
            >
              <span className="hidden sm:inline">Back to Consultation</span>
              <span className="sm:hidden">Consultation</span>
            </Button>
            <Button
              variant="outline"
              onClick={() => navigate('/workflow')}
              className="w-full sm:w-auto text-sm"
            >
              <span className="hidden sm:inline">View Workflow Dashboard</span>
              <span className="sm:hidden">Workflow</span>
            </Button>
            <Button
              variant="outline"
              onClick={() => navigate('/patients')}
              className="w-full sm:w-auto text-sm"
            >
              <span className="hidden sm:inline">View Final Records</span>
              <span className="sm:hidden">Records</span>
            </Button>
          </div>
        </div>

        {/* Search Bar */}
        <div className="flex justify-between items-center">
          <div className="relative w-full sm:w-80">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search patients..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-full text-sm sm:text-base"
            />
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
          <Card>
            <CardContent className="p-3 sm:p-6">
              <div className="flex items-center">
                <div className="p-1.5 sm:p-2 bg-orange-100 rounded-lg mr-2 sm:mr-4 flex-shrink-0">
                  <Clock className="h-4 w-4 sm:h-6 sm:w-6 text-orange-600" />
                </div>
                <div className="min-w-0">
                  <p className="text-lg sm:text-2xl font-bold text-gray-900">{filteredUneditedPatients.length}</p>
                  <p className="text-xs sm:text-sm text-gray-600">From Consultation</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-3 sm:p-6">
              <div className="flex items-center">
                <div className="p-1.5 sm:p-2 bg-green-100 rounded-lg mr-2 sm:mr-4 flex-shrink-0">
                  <TestTube className="h-4 w-4 sm:h-6 sm:w-6 text-green-600" />
                </div>
                <div className="min-w-0">
                  <p className="text-lg sm:text-2xl font-bold text-gray-900">{filteredEditedPatients.length}</p>
                  <p className="text-xs sm:text-sm text-gray-600">Test Complete</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-3 sm:p-6">
              <div className="flex items-center">
                <div className="p-1.5 sm:p-2 bg-blue-100 rounded-lg mr-2 sm:mr-4 flex-shrink-0">
                  <Activity className="h-4 w-4 sm:h-6 sm:w-6 text-blue-600" />
                </div>
                <div className="min-w-0">
                  <p className="text-lg sm:text-2xl font-bold text-gray-900">{patientsWithConsultation.length}</p>
                  <p className="text-xs sm:text-sm text-gray-600">Total Eligible</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-3 sm:p-6">
              <div className="flex items-center">
                <div className="p-1.5 sm:p-2 bg-purple-100 rounded-lg mr-2 sm:mr-4 flex-shrink-0">
                  <Heart className="h-4 w-4 sm:h-6 sm:w-6 text-purple-600" />
                </div>
                <div className="min-w-0">
                  <p className="text-lg sm:text-2xl font-bold text-gray-900">
                    {Array.isArray(laboratoryRecords) ? laboratoryRecords.filter((r: any) => {
                      const today = new Date().toDateString();
                      return new Date(r.test_date || r.created_at).toDateString() === today;
                    }).length : 0}
                  </p>
                  <p className="text-xs sm:text-sm text-gray-600">Today's Tests</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Tabs for From Consultation and Test Complete */}
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg w-full sm:w-fit">
          <Button
            variant={activeTab === 'unedited' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('unedited')}
            className="relative flex-1 sm:flex-none text-xs sm:text-sm"
          >
            <span className="hidden sm:inline">From Consultation</span>
            <span className="sm:hidden">From Consult</span>
            {filteredUneditedPatients.length > 0 && (
              <Badge className="ml-1 sm:ml-2 bg-orange-500 text-white text-xs">
                {filteredUneditedPatients.length}
              </Badge>
            )}
          </Button>
          <Button
            variant={activeTab === 'edited' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('edited')}
            className="relative flex-1 sm:flex-none text-xs sm:text-sm"
          >
            <span className="hidden sm:inline">Test Complete</span>
            <span className="sm:hidden">Complete</span>
            {filteredEditedPatients.length > 0 && (
              <Badge className="ml-1 sm:ml-2 bg-green-500 text-white text-xs">
                {filteredEditedPatients.length}
              </Badge>
            )}
          </Button>
        </div>

        {/* Patient Lists */}
        <Card>
          <CardHeader className="pb-3 sm:pb-6">
            <CardTitle className="flex items-center text-sm sm:text-base">
              {activeTab === 'unedited' ? (
                <>
                  <Clock className="mr-2 h-4 w-4 sm:h-5 sm:w-5 text-orange-600" />
                  <span className="hidden sm:inline">Unedited Patient Records ({filteredUneditedPatients.length})</span>
                  <span className="sm:hidden">Unedited ({filteredUneditedPatients.length})</span>
                </>
              ) : (
                <>
                  <TestTube className="mr-2 h-4 w-4 sm:h-5 sm:w-5 text-green-600" />
                  <span className="hidden sm:inline">Edited Patient Records ({filteredEditedPatients.length})</span>
                  <span className="sm:hidden">Edited ({filteredEditedPatients.length})</span>
                </>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="p-3 sm:p-6">
            {patientsLoading ? (
              <div className="text-center py-8">
                <div className="text-gray-500">Loading patients...</div>
              </div>
            ) : patientsWithConsultation.length === 0 ? (
              <div className="text-center py-8">
                <User className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <p className="text-gray-500">No patients with completed consultations found</p>
                <p className="text-sm text-gray-400 mt-2">
                  Patients need to complete consultation first
                </p>
                <Button
                  onClick={() => navigate('/consultation-department')}
                  className="mt-3"
                >
                  Go to Consultation Department
                </Button>
              </div>
            ) : (
              <>
                {/* From Consultation Tab */}
                {activeTab === 'unedited' && (
                  <>
                    {filteredUneditedPatients.length === 0 ? (
                      <div className="text-center py-8">
                        <Clock className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                        <p className="text-gray-500">
                          {searchTerm ? 'No patients found matching your search' : 'No unedited patient records'}
                        </p>
                      </div>
                    ) : (
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-4">
                        {filteredUneditedPatients.map((patient) => (
                          <Card key={patient.id} className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-orange-500">
                            <CardContent className="p-4">
                              {/* Header Section */}
                              <div className="flex items-start justify-between mb-4">
                                <div className="flex items-center space-x-3 min-w-0 flex-1">
                                  <div className="p-2 bg-orange-100 rounded-lg flex-shrink-0">
                                    <User className="h-5 w-5 text-orange-600" />
                                  </div>
                                  <div className="min-w-0 flex-1">
                                    <h3 className="font-semibold text-gray-900 text-base truncate">
                                      {patient.patient_name}
                                    </h3>
                                    <p className="text-sm text-gray-500">
                                      Age: {getPatientAge(patient.date_of_birth)} years
                                    </p>
                                  </div>
                                </div>
                                <Badge className="bg-orange-100 text-orange-800 border-orange-200 flex-shrink-0">
                                  <TestTube className="h-3 w-3 mr-1" />
                                  <span className="hidden sm:inline">Awaiting Tests</span>
                                  <span className="sm:hidden">Pending</span>
                                </Badge>
                              </div>

                              {/* Patient Details Section */}
                              <div className="bg-gray-50 rounded-lg p-3 mb-4">
                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                                  <div className="flex items-center text-sm text-gray-600">
                                    <Phone className="h-4 w-4 mr-2 text-gray-400 flex-shrink-0" />
                                    <span className="truncate">{patient.phone_number}</span>
                                  </div>
                                  <div className="flex items-center text-sm text-gray-600">
                                    <Mail className="h-4 w-4 mr-2 text-gray-400 flex-shrink-0" />
                                    <span className="truncate">{patient.email}</span>
                                  </div>
                                  {patient.blood_type && (
                                    <div className="flex items-center text-sm text-gray-600">
                                      <Heart className="h-4 w-4 mr-2 text-red-400 flex-shrink-0" />
                                      <span>Blood Type: {patient.blood_type}</span>
                                    </div>
                                  )}
                                </div>
                              </div>

                              {/* Status and Department Information */}
                              <div className="flex items-center justify-between mb-4">
                                <div className="flex items-center space-x-2">
                                  <div className="flex items-center text-sm text-green-600 bg-green-50 px-2 py-1 rounded-full">
                                    <Stethoscope className="h-4 w-4 mr-1" />
                                    <span className="hidden sm:inline">Consultation completed</span>
                                    <span className="sm:hidden">Consulted</span>
                                  </div>
                                </div>
                                {(() => {
                                  const deptInfo = getDepartmentInfo(patient);
                                  return deptInfo && (
                                    <Badge
                                      className="text-xs text-white"
                                      style={{ backgroundColor: deptInfo.color || '#3B82F6' }}
                                    >
                                      {deptInfo.name}
                                    </Badge>
                                  );
                                })()}
                              </div>

                              {/* Action Section */}
                              <div className="flex items-center justify-between pt-3 border-t border-gray-100">
                                <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                                  ID: {patient.id.slice(0, 8)}...
                                </Badge>
                                <Button
                                  size="sm"
                                  className="bg-orange-600 hover:bg-orange-700 text-white shadow-sm"
                                  onClick={() => handleStartTest(patient)}
                                >
                                  <TestTube className="h-4 w-4 mr-2" />
                                  <span className="hidden sm:inline">Start Lab Tests</span>
                                  <span className="sm:hidden">Start Tests</span>
                                </Button>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    )}
                  </>
                )}

                {/* Test Complete Tab */}
                {activeTab === 'edited' && (
                  <>
                    {filteredEditedPatients.length === 0 ? (
                      <div className="text-center py-8">
                        <TestTube className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                        <p className="text-gray-500">
                          {searchTerm ? 'No patients found matching your search' : 'No edited patient records yet'}
                        </p>
                      </div>
                    ) : (
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {filteredEditedPatients.map((patient) => {
                          const labRecord = Array.isArray(laboratoryRecords) ? laboratoryRecords.find((record: any) => record.patient_id === patient.id) : null;
                          return (
                            <Card key={patient.id} className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-green-500">
                              <CardContent className="p-4">
                                {/* Header Section */}
                                <div className="flex items-start justify-between mb-4">
                                  <div className="flex items-center space-x-3 min-w-0 flex-1">
                                    <div className="p-2 bg-green-100 rounded-lg flex-shrink-0">
                                      <User className="h-5 w-5 text-green-600" />
                                    </div>
                                    <div className="min-w-0 flex-1">
                                      <h3 className="font-semibold text-gray-900 text-base truncate">
                                        {patient.patient_name}
                                      </h3>
                                      <p className="text-sm text-gray-500">
                                        Age: {getPatientAge(patient.date_of_birth)} years
                                      </p>
                                    </div>
                                  </div>
                                  <Badge className="bg-green-100 text-green-800 border-green-200 flex-shrink-0">
                                    <TestTube className="h-3 w-3 mr-1" />
                                    <span className="hidden sm:inline">Tests Complete</span>
                                    <span className="sm:hidden">Complete</span>
                                  </Badge>
                                </div>

                                {/* Patient Details Section */}
                                <div className="bg-gray-50 rounded-lg p-3 mb-4">
                                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 mb-3">
                                    <div className="flex items-center text-sm text-gray-600">
                                      <Phone className="h-4 w-4 mr-2 text-gray-400 flex-shrink-0" />
                                      <span className="truncate">{patient.phone_number}</span>
                                    </div>
                                    <div className="flex items-center text-sm text-gray-600">
                                      <Mail className="h-4 w-4 mr-2 text-gray-400 flex-shrink-0" />
                                      <span className="truncate">{patient.email}</span>
                                    </div>
                                  </div>

                                  {/* Department Information */}
                                  {(() => {
                                    const deptInfo = getDepartmentInfo(patient);
                                    return deptInfo && (
                                      <div className="flex items-center justify-between mb-3">
                                        <div className="flex items-center text-sm text-gray-600">
                                          <Stethoscope className="h-4 w-4 mr-2 text-gray-400" />
                                          <span>Department:</span>
                                        </div>
                                        <Badge
                                          className="text-xs text-white"
                                          style={{ backgroundColor: deptInfo.color || '#3B82F6' }}
                                        >
                                          {deptInfo.name}
                                        </Badge>
                                      </div>
                                    );
                                  })()}

                                  {/* Lab Results Section */}
                                  {labRecord && (
                                    <div className="border-t border-gray-200 pt-3">
                                      <h4 className="text-sm font-medium text-gray-900 mb-2">Lab Results</h4>
                                      <div className="space-y-2">
                                        <div className="flex items-center text-sm text-gray-600">
                                          <TestTube className="h-4 w-4 mr-2 text-blue-500" />
                                          <span className="font-medium">Test:</span>
                                          <span className="ml-1">{(labRecord as any)?.test_type}</span>
                                        </div>
                                        <div className="flex items-center text-sm text-gray-600">
                                          <Clock className="h-4 w-4 mr-2 text-green-500" />
                                          <span className="font-medium">Date:</span>
                                          <span className="ml-1">{(labRecord as any)?.test_date ? new Date((labRecord as any).test_date).toLocaleDateString() : 'N/A'}</span>
                                        </div>
                                        {/* Lab Test Times */}
                                        {((labRecord as any)?.start_time || (labRecord as any)?.end_time) && (
                                          <div className="bg-blue-50 p-2 rounded border border-blue-200">
                                            <div className="text-xs font-medium text-blue-800 mb-1">⏰ Test Times</div>
                                            <div className="space-y-1">
                                              {(labRecord as any)?.start_time && (
                                                <div className="text-xs text-blue-700">
                                                  <span className="font-medium">Start:</span> {(labRecord as any).start_time}
                                                </div>
                                              )}
                                              {(labRecord as any)?.end_time && (
                                                <div className="text-xs text-blue-700">
                                                  <span className="font-medium">End:</span> {(labRecord as any).end_time}
                                                </div>
                                              )}
                                              {(labRecord as any)?.start_time && (labRecord as any)?.end_time && (
                                                <div className="text-xs text-blue-700">
                                                  <span className="font-medium">Duration:</span>
                                                  {(() => {
                                                    const start = new Date(`2000-01-01T${(labRecord as any).start_time}`);
                                                    const end = new Date(`2000-01-01T${(labRecord as any).end_time}`);
                                                    const diffMs = end.getTime() - start.getTime();
                                                    const diffMins = Math.round(diffMs / (1000 * 60));
                                                    return ` ${Math.floor(diffMins / 60)}h ${diffMins % 60}m`;
                                                  })()}
                                                </div>
                                              )}
                                            </div>
                                          </div>
                                        )}
                                        {/* Fee Information */}
                                        <div className="bg-gradient-to-r from-yellow-50 to-orange-50 p-3 rounded-lg border border-yellow-200 mt-2">
                                          <div className="text-xs font-bold text-yellow-800 mb-2 flex items-center">
                                            💰 Laboratory Fee Status
                                          </div>
                                          <div className="grid grid-cols-2 gap-2">
                                            <div className="text-xs">
                                              <span className="font-medium text-yellow-700">Amount:</span>
                                              <div className="font-bold text-yellow-900">
                                                {formatCurrency((labRecord as any)?.fee_amount || 0)}
                                              </div>
                                            </div>
                                            <div className="text-xs">
                                              <span className="font-medium text-yellow-700">Status:</span>
                                              <div className={`font-bold ${(labRecord as any)?.fee_paid ? 'text-green-700' : 'text-red-700'}`}>
                                                {(labRecord as any)?.fee_paid ? '✅ PAID' : '❌ NOT PAID'}
                                              </div>
                                            </div>
                                          </div>
                                          {!((labRecord as any)?.fee_paid) && (labRecord as any)?.fee_amount > 0 && (
                                            <div className="mt-2 p-1 bg-red-100 border border-red-300 rounded text-xs text-red-700 font-medium">
                                              ⚠️ Payment Required
                                            </div>
                                          )}
                                        </div>
                                        {(labRecord as any)?.results && (
                                          <div className="bg-white p-2 rounded border border-gray-200">
                                            <span className="text-sm font-medium text-gray-900">Results: </span>
                                            <span className="text-sm text-gray-700">{(labRecord as any).results}</span>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  )}
                                </div>

                                {/* Action Section */}
                                <div className="flex items-center justify-between pt-3 border-t border-gray-100">
                                  <Badge className="bg-blue-100 text-blue-800 border-blue-200">
                                    <Stethoscope className="h-3 w-3 mr-1" />
                                    Ready for Follow-up
                                  </Badge>
                                  <Button
                                    size="sm"
                                    onClick={() => handleSendToFollowUp(patient)}
                                    className="bg-green-600 hover:bg-green-700 text-white shadow-sm"
                                  >
                                    <Stethoscope className="h-4 w-4 mr-2" />
                                    <span className="hidden sm:inline">Edit and send to consultation</span>
                                    <span className="sm:hidden">Send to Consult</span>
                                  </Button>
                                </div>
                              </CardContent>
                            </Card>
                          );
                        })}
                      </div>
                    )}
                  </>
                )}
              </>
            )}
          </CardContent>
        </Card>

        {/* Laboratory Form Dialog */}
        <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            {selectedPatient && (
              <>
                <DialogHeader>
                  <DialogTitle>
                    {Array.isArray(laboratoryRecords) && laboratoryRecords.some((record: any) => record.patient_id === selectedPatient.id)
                      ? 'Edit Patient Record'
                      : 'Edit Patient Record'} - {selectedPatient.patient_name}
                  </DialogTitle>
                  <DialogDescription>
                    Age: {getPatientAge(selectedPatient.date_of_birth)} years •
                    Blood Type: {selectedPatient.blood_type || 'Unknown'} •
                    Patient ID: {selectedPatient.id}
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <LaboratoryForm
                    patientId={selectedPatient.id}
                    workflowId={selectedPatient.id}
                    onSuccess={handleLaboratoryComplete}
                    autoAdvance={true}
                    existingData={Array.isArray(laboratoryRecords) ? laboratoryRecords.find((record: any) => record.patient_id === selectedPatient.id) : null}
                    consultationData={Array.isArray(consultationRecords) ? consultationRecords.find((record: any) => record.patient_id === selectedPatient.id) : null}
                  />
                </div>
              </>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </Layout>
  );
};

export default LaboratoryDepartment;
