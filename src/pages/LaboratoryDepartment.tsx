import { useState } from 'react';
import { Layout } from '../components/Layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  TestTube,
  Clock,
  Activity,
  Heart,
  Search,
  User,
  Phone,
  Mail,
  Stethoscope,
  Filter,
  ArrowLeft,
  BarChart3,
  FileText,
  Calendar,
  MapPin,
  Shield,
  Eye,
  Edit,
  Trash2,
  Plus,
  Microscope,
  FlaskConical,
  Timer,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { useWorkflowData } from '@/hooks/useWorkflowData';
import { useSupabaseData } from '@/hooks/useSupabaseData';
import { useCurrency } from '@/contexts/CurrencyContext';
import { LaboratoryForm } from '@/components/workflow/LaboratoryForm';
import { useNavigate } from 'react-router-dom';
import { toast } from '@/hooks/use-toast';

const LaboratoryDepartment = () => {
  const navigate = useNavigate();
  const { formatCurrency } = useCurrency();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPatient, setSelectedPatient] = useState<any>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('unedited'); // 'unedited' or 'edited'

  // Helper function to get department information
  const getDepartmentInfo = (patient: any) => {
    // Handle both flat structure (from reception function) and nested structure (from regular patients query)
    if (patient.department_name) {
      return {
        name: patient.department_name,
        color: patient.department_color,
        icon: patient.department_icon,
        notes: patient.department_notes
      };
    } else if (patient.medical_departments) {
      return {
        name: patient.medical_departments.name,
        color: patient.medical_departments.color,
        icon: patient.medical_departments.icon,
        notes: patient.department_notes
      };
    }
    return null;
  };
  
  const {
    useWorkflowStats,
    useLaboratoryRecords,
    createLaboratoryRecord,
    updateLaboratoryRecord,
    useConsultationRecords
  } = useWorkflowData();
  
  const { usePatients } = useSupabaseData();
  
  const { data: allPatients = [], isLoading: patientsLoading } = usePatients();
  const { data: laboratoryRecords = [], error: labRecordsError } = useLaboratoryRecords();
  const { data: consultationRecords = [] } = useConsultationRecords();

  // Handle laboratory records error
  if (labRecordsError) {
    console.error('Laboratory records error:', labRecordsError);
  }

  // Debug logging (only show errors)
  if (labRecordsError) {
    console.error('Laboratory Records Error:', labRecordsError);
  }

  // Get patients who have completed consultation (eligible for lab tests)
  const patientsWithConsultation = allPatients.filter(patient =>
    Array.isArray(consultationRecords) && consultationRecords.some((record: any) => record.patient_id === patient.id)
  );

  // ALL patients with consultation are shown, but categorized by lab test status
  // Unedited: Patients without lab records (need lab tests)
  const uneditedPatients = patientsWithConsultation.filter(patient =>
    !Array.isArray(laboratoryRecords) || !laboratoryRecords.some((record: any) => record.patient_id === patient.id)
  );

  // Edited: Patients with lab records (lab tests completed)
  const editedPatients = patientsWithConsultation.filter(patient =>
    Array.isArray(laboratoryRecords) && laboratoryRecords.some((record: any) => record.patient_id === patient.id)
  );

  // Filter based on search term
  const filteredUneditedPatients = uneditedPatients.filter(patient =>
    patient.patient_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.phone_number.includes(searchTerm)
  );

  const filteredEditedPatients = editedPatients.filter(patient =>
    patient.patient_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.phone_number.includes(searchTerm)
  );

  const handleStartTest = (patient: any) => {
    setSelectedPatient(patient);
    setIsFormOpen(true);
  };

  const handleSendToFollowUp = (patient: any) => {
    toast({
      title: "Patient Sent to Follow-up",
      description: `${patient.patient_name} has been sent to consultation for follow-up review of lab results.`,
    });

    // Navigate to consultation department to show the patient is ready for follow-up
    setTimeout(() => {
      navigate('/consultation-department');
    }, 1500);
  };

  const handleLaboratoryComplete = async (laboratoryData: any) => {
    if (!selectedPatient) return;

    try {
      // Check if patient already has a laboratory record
      const existingRecord = Array.isArray(laboratoryRecords) ? laboratoryRecords.find((record: any) => record.patient_id === selectedPatient.id) : null;

      console.log('🔍 LABORATORY DEPARTMENT DEBUG:', {
        selectedPatientId: selectedPatient.id,
        existingRecord: existingRecord ? 'found' : 'not found',
        existingRecordId: existingRecord?.id,
        laboratoryRecordsCount: laboratoryRecords.length,
        laboratoryDataKeys: Object.keys(laboratoryData)
      });

      if (existingRecord) {
        // Patient already has laboratory record - update it instead of creating new one
        console.log('🔄 Updating existing laboratory record for patient:', selectedPatient.id);

        await updateLaboratoryRecord.mutateAsync({
          recordId: existingRecord.id,
          labData: {
            ...laboratoryData,
            test_date: new Date().toISOString().split('T')[0],
            updated_at: new Date().toISOString()
          },
          showToast: false // We'll show our own toast
        });

        toast({
          title: "Laboratory Record Updated",
          description: "Laboratory record updated successfully. Redirecting to consultation for follow-up.",
        });

        // Close dialog and navigate to patients
        setIsFormOpen(false);
        setSelectedPatient(null);

        // Auto-navigate to consultation department for follow-up after update
        setTimeout(() => {
          navigate('/consultation-department');
        }, 1500);
        return; // Exit early after successful update
      }

      // Create new laboratory record only if none exists
      console.log('🆕 Creating new laboratory record for patient:', selectedPatient.id);
      await createLaboratoryRecord.mutateAsync({
        patient_id: selectedPatient.id,
        ...laboratoryData,
        test_date: new Date().toISOString().split('T')[0]
      });

      toast({
        title: "Success",
        description: "Laboratory record created successfully! Redirecting to consultation for follow-up.",
      });

      setIsFormOpen(false);
      setSelectedPatient(null);

      // Auto-navigate to consultation department for follow-up after new lab test completion
      setTimeout(() => {
        navigate('/consultation-department');
      }, 1500);

    } catch (error) {
      console.error('Error completing laboratory test:', error);

      // Don't show error toast here since the LaboratoryForm handles its own errors
      // and the operation might actually be succeeding despite the caught error
      // The success toasts above will show if the operation completed successfully
    }
  };

  const getPatientAge = (dateOfBirth: string) => {
    if (!dateOfBirth) return 'Unknown';
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  // Debug logging (only show errors)
  if (labRecordsError) {
    console.error('Laboratory Records Error:', labRecordsError);
  }

  return (
    <Layout>
      <div className="space-y-8">
        {/* Enhanced Header */}
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 border border-green-100">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex items-center space-x-4">
              <div className="bg-green-600 p-3 rounded-lg">
                <Microscope className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Laboratory Department</h1>
                <p className="text-gray-600 mt-1 text-sm sm:text-base flex items-center">
                  <FlaskConical className="h-4 w-4 mr-2" />
                  Medical tests and laboratory analysis → Final records
                </p>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
              <Button
                variant="outline"
                onClick={() => navigate('/consultation-department')}
                className="w-full sm:w-auto text-sm border-2 border-gray-200 hover:border-green-300 hover:bg-green-50 transition-all duration-200"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Back to Consultation</span>
                <span className="sm:hidden">Consultation</span>
              </Button>
              <Button
                variant="outline"
                onClick={() => navigate('/workflow')}
                className="w-full sm:w-auto text-sm border-2 border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-all duration-200"
              >
                <BarChart3 className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Workflow Dashboard</span>
                <span className="sm:hidden">Workflow</span>
              </Button>
              <Button
                variant="outline"
                onClick={() => navigate('/patients')}
                className="w-full sm:w-auto text-sm border-2 border-gray-200 hover:border-purple-300 hover:bg-purple-50 transition-all duration-200"
              >
                <FileText className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Final Records</span>
                <span className="sm:hidden">Records</span>
              </Button>
            </div>
          </div>

          {/* Enhanced Stats Row */}
          <div className="mt-6 grid grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-white rounded-lg p-4 border border-orange-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Awaiting Tests</p>
                  <p className="text-2xl font-bold text-orange-600">{filteredUneditedPatients.length}</p>
                </div>
                <Clock className="h-8 w-8 text-orange-500" />
              </div>
            </div>
            <div className="bg-white rounded-lg p-4 border border-green-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Tests Complete</p>
                  <p className="text-2xl font-bold text-green-600">{filteredEditedPatients.length}</p>
                </div>
                <TestTube className="h-8 w-8 text-green-500" />
              </div>
            </div>
            <div className="bg-white rounded-lg p-4 border border-blue-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Eligible</p>
                  <p className="text-2xl font-bold text-blue-600">{patientsWithConsultation.length}</p>
                </div>
                <Activity className="h-8 w-8 text-blue-500" />
              </div>
            </div>
            <div className="bg-white rounded-lg p-4 border border-purple-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Today's Tests</p>
                  <p className="text-2xl font-bold text-purple-600">
                    {Array.isArray(laboratoryRecords) ? laboratoryRecords.filter((r: any) => {
                      const today = new Date().toDateString();
                      return new Date(r.test_date || r.created_at).toDateString() === today;
                    }).length : 0}
                  </p>
                </div>
                <Heart className="h-8 w-8 text-purple-500" />
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Search and Filter */}
        <Card className="p-6 bg-white shadow-sm border border-gray-200">
          <div className="flex flex-col sm:flex-row gap-4 sm:gap-6">
            <div className="flex-1 relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
              <Input
                type="text"
                placeholder="Search by name, email, phone, or test type..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-12 pr-4 py-3 text-sm sm:text-base border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200"
              />
            </div>
            <div className="flex gap-3">
              <Button variant="outline" className="flex items-center gap-2 px-4 py-3 text-sm border-2 border-gray-200 hover:border-green-300 hover:bg-green-50 transition-all duration-200">
                <Filter size={16} />
                <span className="hidden sm:inline">Filter</span>
              </Button>
              <div className="flex items-center text-sm text-gray-600 bg-gray-50 px-4 py-3 rounded-xl">
                <span className="font-medium">{patientsWithConsultation.length}</span>
                <span className="ml-1">eligible patients</span>
              </div>
            </div>
          </div>
        </Card>

        {/* Enhanced Tabs for From Consultation and Test Complete */}
        <Card className="p-2 bg-white shadow-sm border border-gray-200">
          <div className="flex space-x-2 bg-gray-50 p-2 rounded-lg w-full">
            <Button
              variant={activeTab === 'unedited' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setActiveTab('unedited')}
              className={`relative flex-1 text-sm font-medium transition-all duration-200 ${
                activeTab === 'unedited'
                  ? 'bg-orange-500 hover:bg-orange-600 text-white shadow-md'
                  : 'hover:bg-orange-50 hover:text-orange-700'
              }`}
            >
              <Clock className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">From Consultation</span>
              <span className="sm:hidden">From Consult</span>
              {filteredUneditedPatients.length > 0 && (
                <Badge className={`ml-2 text-xs ${
                  activeTab === 'unedited'
                    ? 'bg-orange-600 text-white'
                    : 'bg-orange-100 text-orange-800'
                }`}>
                  {filteredUneditedPatients.length}
                </Badge>
              )}
            </Button>
            <Button
              variant={activeTab === 'edited' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setActiveTab('edited')}
              className={`relative flex-1 text-sm font-medium transition-all duration-200 ${
                activeTab === 'edited'
                  ? 'bg-green-500 hover:bg-green-600 text-white shadow-md'
                  : 'hover:bg-green-50 hover:text-green-700'
              }`}
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Test Complete</span>
              <span className="sm:hidden">Complete</span>
              {filteredEditedPatients.length > 0 && (
                <Badge className={`ml-2 text-xs ${
                  activeTab === 'edited'
                    ? 'bg-green-600 text-white'
                    : 'bg-green-100 text-green-800'
                }`}>
                  {filteredEditedPatients.length}
                </Badge>
              )}
            </Button>
          </div>
        </Card>

        {/* Patient Lists */}
        <Card>
          <CardHeader className="pb-3 sm:pb-6">
            <CardTitle className="flex items-center text-sm sm:text-base">
              {activeTab === 'unedited' ? (
                <>
                  <Clock className="mr-2 h-4 w-4 sm:h-5 sm:w-5 text-orange-600" />
                  <span className="hidden sm:inline">Unedited Patient Records ({filteredUneditedPatients.length})</span>
                  <span className="sm:hidden">Unedited ({filteredUneditedPatients.length})</span>
                </>
              ) : (
                <>
                  <TestTube className="mr-2 h-4 w-4 sm:h-5 sm:w-5 text-green-600" />
                  <span className="hidden sm:inline">Edited Patient Records ({filteredEditedPatients.length})</span>
                  <span className="sm:hidden">Edited ({filteredEditedPatients.length})</span>
                </>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="p-3 sm:p-6">
            {patientsLoading ? (
              <div className="text-center py-8">
                <div className="text-gray-500">Loading patients...</div>
              </div>
            ) : patientsWithConsultation.length === 0 ? (
              <div className="text-center py-8">
                <User className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <p className="text-gray-500">No patients with completed consultations found</p>
                <p className="text-sm text-gray-400 mt-2">
                  Patients need to complete consultation first
                </p>
                <Button
                  onClick={() => navigate('/consultation-department')}
                  className="mt-3"
                >
                  Go to Consultation Department
                </Button>
              </div>
            ) : (
              <>
                {/* From Consultation Tab */}
                {activeTab === 'unedited' && (
                  <>
                    {filteredUneditedPatients.length === 0 ? (
                      <div className="text-center py-12">
                        <div className="bg-orange-100 rounded-full p-4 w-16 h-16 mx-auto mb-4">
                          <Clock className="h-8 w-8 text-orange-500 mx-auto" />
                        </div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">No patients awaiting tests</h3>
                        <p className="text-gray-500 mb-6">
                          {searchTerm ? 'Try adjusting your search terms or clear the search to see all patients.' : 'All patients have completed their laboratory tests or no patients have been sent from consultation yet.'}
                        </p>
                        <Button onClick={() => navigate('/consultation-department')} className="bg-orange-600 hover:bg-orange-700">
                          <ArrowLeft className="mr-2 h-4 w-4" />
                          Go to Consultation
                        </Button>
                      </div>
                    ) : (
                      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                        {filteredUneditedPatients.map((patient) => (
                          <Card key={patient.id} className="group overflow-hidden hover:shadow-xl transition-all duration-300 border-2 border-orange-100 hover:border-orange-200 bg-white">
                            <CardContent className="p-6">
                              {/* Header with Avatar and Status */}
                              <div className="flex items-start space-x-4 mb-6">
                                <div className="relative">
                                  <Avatar className="h-14 w-14 ring-2 ring-orange-100 group-hover:ring-orange-200 transition-all duration-200">
                                    <AvatarImage src="" alt={patient.patient_name} />
                                    <AvatarFallback className="text-lg font-semibold bg-gradient-to-br from-orange-500 to-red-600 text-white">
                                      {patient.patient_name?.split(' ').map((n: string) => n[0]).join('') || 'P'}
                                    </AvatarFallback>
                                  </Avatar>
                                  {/* Status Indicator */}
                                  <div className="absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white bg-orange-500" />
                                </div>
                                <div className="flex-1 min-w-0">
                                  <h3 className="font-bold text-lg text-gray-900 truncate group-hover:text-orange-600 transition-colors duration-200">
                                    {patient.patient_name}
                                  </h3>
                                  <p className="text-sm text-gray-500 truncate font-mono">
                                    ID: {patient.id.slice(0, 8)}...
                                  </p>
                                  <div className="flex items-center mt-1">
                                    <Calendar className="h-3 w-3 text-gray-400 mr-1" />
                                    <span className="text-xs text-gray-500">
                                      Age: {getPatientAge(patient.date_of_birth)} years
                                    </span>
                                  </div>
                                </div>
                                <Badge className="bg-orange-100 text-orange-800 border-orange-200 flex-shrink-0 px-3 py-1">
                                  <Timer className="h-3 w-3 mr-1" />
                                  <span className="hidden sm:inline">Awaiting Tests</span>
                                  <span className="sm:hidden">Pending</span>
                                </Badge>
                              </div>

                              {/* Contact Information */}
                              <div className="space-y-4 mb-6">
                                <div className="grid grid-cols-1 gap-3">
                                  <div className="flex items-center text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
                                    <Mail className="h-4 w-4 mr-3 text-blue-500 flex-shrink-0" />
                                    <span className="truncate font-medium">{patient.email || 'No email provided'}</span>
                                  </div>
                                  <div className="flex items-center text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
                                    <Phone className="h-4 w-4 mr-3 text-green-500 flex-shrink-0" />
                                    <span className="truncate font-medium">{patient.phone_number || 'No phone provided'}</span>
                                  </div>
                                </div>
                              </div>

                              {/* Medical Information */}
                              <div className="space-y-3 mb-6">
                                <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-100">
                                  <div className="flex items-center text-sm text-gray-700">
                                    <Heart className="h-4 w-4 mr-2 text-red-500" />
                                    <span className="font-medium">Blood Type:</span>
                                  </div>
                                  <Badge variant="outline" className="text-sm font-semibold text-red-700 border-red-200">
                                    {patient.blood_type || 'Unknown'}
                                  </Badge>
                                </div>

                                {/* Department Information */}
                                {(() => {
                                  const deptInfo = getDepartmentInfo(patient);
                                  return deptInfo && (
                                    <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-100">
                                      <div className="flex items-center text-sm text-gray-700">
                                        <Stethoscope className="h-4 w-4 mr-2 text-blue-500" />
                                        <span className="font-medium">Department:</span>
                                      </div>
                                      <Badge
                                        className="text-sm font-semibold text-white"
                                        style={{ backgroundColor: deptInfo.color || '#3B82F6' }}
                                      >
                                        {deptInfo.name}
                                      </Badge>
                                    </div>
                                  );
                                })()}

                                {/* Consultation Status */}
                                <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-100">
                                  <div className="flex items-center text-sm text-gray-700">
                                    <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                                    <span className="font-medium">Status:</span>
                                  </div>
                                  <Badge className="bg-green-100 text-green-800 text-sm font-semibold">
                                    ✓ Consultation Complete
                                  </Badge>
                                </div>
                              </div>

                              {/* Action Section */}
                              <div className="pt-4 border-t border-gray-200">
                                <Button
                                  className="w-full bg-orange-600 hover:bg-orange-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                                  onClick={() => handleStartTest(patient)}
                                >
                                  <TestTube className="h-4 w-4 mr-2" />
                                  <span>Start Laboratory Tests</span>
                                </Button>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    )}
                  </>
                )}

                {/* Test Complete Tab */}
                {activeTab === 'edited' && (
                  <>
                    {filteredEditedPatients.length === 0 ? (
                      <div className="text-center py-12">
                        <div className="bg-green-100 rounded-full p-4 w-16 h-16 mx-auto mb-4">
                          <TestTube className="h-8 w-8 text-green-500 mx-auto" />
                        </div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">No completed tests</h3>
                        <p className="text-gray-500 mb-6">
                          {searchTerm ? 'Try adjusting your search terms or clear the search to see all patients.' : 'No laboratory tests have been completed yet. Start testing patients from the consultation queue.'}
                        </p>
                        <Button onClick={() => setActiveTab('unedited')} className="bg-green-600 hover:bg-green-700">
                          <Timer className="mr-2 h-4 w-4" />
                          View Pending Tests
                        </Button>
                      </div>
                    ) : (
                      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                        {filteredEditedPatients.map((patient) => {
                          const labRecord = Array.isArray(laboratoryRecords) ? laboratoryRecords.find((record: any) => record.patient_id === patient.id) : null;
                          return (
                            <Card key={patient.id} className="group overflow-hidden hover:shadow-xl transition-all duration-300 border-2 border-green-100 hover:border-green-200 bg-white">
                              <CardContent className="p-6">
                                {/* Header with Avatar and Status */}
                                <div className="flex items-start space-x-4 mb-6">
                                  <div className="relative">
                                    <Avatar className="h-14 w-14 ring-2 ring-green-100 group-hover:ring-green-200 transition-all duration-200">
                                      <AvatarImage src="" alt={patient.patient_name} />
                                      <AvatarFallback className="text-lg font-semibold bg-gradient-to-br from-green-500 to-emerald-600 text-white">
                                        {patient.patient_name?.split(' ').map((n: string) => n[0]).join('') || 'P'}
                                      </AvatarFallback>
                                    </Avatar>
                                    {/* Status Indicator */}
                                    <div className="absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white bg-green-500" />
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <h3 className="font-bold text-lg text-gray-900 truncate group-hover:text-green-600 transition-colors duration-200">
                                      {patient.patient_name}
                                    </h3>
                                    <p className="text-sm text-gray-500 truncate font-mono">
                                      ID: {patient.id.slice(0, 8)}...
                                    </p>
                                    <div className="flex items-center mt-1">
                                      <Calendar className="h-3 w-3 text-gray-400 mr-1" />
                                      <span className="text-xs text-gray-500">
                                        Age: {getPatientAge(patient.date_of_birth)} years
                                      </span>
                                    </div>
                                  </div>
                                  <Badge className="bg-green-100 text-green-800 border-green-200 flex-shrink-0 px-3 py-1">
                                    <CheckCircle className="h-3 w-3 mr-1" />
                                    <span className="hidden sm:inline">Tests Complete</span>
                                    <span className="sm:hidden">Complete</span>
                                  </Badge>
                                </div>

                                {/* Contact Information */}
                                <div className="space-y-3 mb-6">
                                  <div className="flex items-center text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
                                    <Mail className="h-4 w-4 mr-3 text-blue-500 flex-shrink-0" />
                                    <span className="truncate font-medium">{patient.email || 'No email provided'}</span>
                                  </div>
                                  <div className="flex items-center text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
                                    <Phone className="h-4 w-4 mr-3 text-green-500 flex-shrink-0" />
                                    <span className="truncate font-medium">{patient.phone_number || 'No phone provided'}</span>
                                  </div>
                                </div>

                                {/* Medical Information */}
                                <div className="space-y-3 mb-6">
                                  <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-100">
                                    <div className="flex items-center text-sm text-gray-700">
                                      <Heart className="h-4 w-4 mr-2 text-red-500" />
                                      <span className="font-medium">Blood Type:</span>
                                    </div>
                                    <Badge variant="outline" className="text-sm font-semibold text-red-700 border-red-200">
                                      {patient.blood_type || 'Unknown'}
                                    </Badge>
                                  </div>

                                  {/* Department Information */}
                                  {(() => {
                                    const deptInfo = getDepartmentInfo(patient);
                                    return deptInfo && (
                                      <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-100">
                                        <div className="flex items-center text-sm text-gray-700">
                                          <Stethoscope className="h-4 w-4 mr-2 text-blue-500" />
                                          <span className="font-medium">Department:</span>
                                        </div>
                                        <Badge
                                          className="text-sm font-semibold text-white"
                                          style={{ backgroundColor: deptInfo.color || '#3B82F6' }}
                                        >
                                          {deptInfo.name}
                                        </Badge>
                                      </div>
                                    );
                                  })()}
                                </div>

                                {/* Lab Results Section */}
                                {labRecord && (
                                  <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100">
                                    <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                      <FlaskConical className="h-5 w-5 mr-2 text-blue-600" />
                                      Laboratory Results
                                    </h4>
                                    <div className="space-y-4">
                                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                        <div className="bg-white p-3 rounded-lg border border-blue-200">
                                          <div className="flex items-center text-sm text-gray-700 mb-1">
                                            <TestTube className="h-4 w-4 mr-2 text-blue-500" />
                                            <span className="font-medium">Test Type:</span>
                                          </div>
                                          <span className="text-sm font-semibold text-gray-900">{(labRecord as any)?.test_type || 'N/A'}</span>
                                        </div>
                                        <div className="bg-white p-3 rounded-lg border border-blue-200">
                                          <div className="flex items-center text-sm text-gray-700 mb-1">
                                            <Calendar className="h-4 w-4 mr-2 text-green-500" />
                                            <span className="font-medium">Test Date:</span>
                                          </div>
                                          <span className="text-sm font-semibold text-gray-900">
                                            {(labRecord as any)?.test_date ? new Date((labRecord as any).test_date).toLocaleDateString() : 'N/A'}
                                          </span>
                                        </div>
                                      </div>

                                      {/* Lab Test Times */}
                                      {((labRecord as any)?.start_time || (labRecord as any)?.end_time) && (
                                        <div className="bg-white p-4 rounded-lg border border-blue-200">
                                          <div className="text-sm font-semibold text-blue-800 mb-3 flex items-center">
                                            <Timer className="h-4 w-4 mr-2" />
                                            Test Duration
                                          </div>
                                          <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
                                            {(labRecord as any)?.start_time && (
                                              <div className="text-center p-2 bg-blue-50 rounded">
                                                <div className="text-xs text-blue-600 font-medium">Start Time</div>
                                                <div className="text-sm font-bold text-blue-800">{(labRecord as any).start_time}</div>
                                              </div>
                                            )}
                                            {(labRecord as any)?.end_time && (
                                              <div className="text-center p-2 bg-blue-50 rounded">
                                                <div className="text-xs text-blue-600 font-medium">End Time</div>
                                                <div className="text-sm font-bold text-blue-800">{(labRecord as any).end_time}</div>
                                              </div>
                                            )}
                                            {(labRecord as any)?.start_time && (labRecord as any)?.end_time && (
                                              <div className="text-center p-2 bg-green-50 rounded">
                                                <div className="text-xs text-green-600 font-medium">Duration</div>
                                                <div className="text-sm font-bold text-green-800">
                                                  {(() => {
                                                    const start = new Date(`2000-01-01T${(labRecord as any).start_time}`);
                                                    const end = new Date(`2000-01-01T${(labRecord as any).end_time}`);
                                                    const diffMs = end.getTime() - start.getTime();
                                                    const diffMins = Math.round(diffMs / (1000 * 60));
                                                    return `${Math.floor(diffMins / 60)}h ${diffMins % 60}m`;
                                                  })()}
                                                </div>
                                              </div>
                                            )}
                                          </div>
                                        </div>
                                      )}
                                      {/* Fee Information */}
                                      <div className="bg-white p-4 rounded-lg border border-yellow-200">
                                        <div className="text-sm font-semibold text-yellow-800 mb-3 flex items-center">
                                          <span className="mr-2">💰</span>
                                          Laboratory Fee Status
                                        </div>
                                        <div className="grid grid-cols-2 gap-4">
                                          <div className="text-center p-3 bg-yellow-50 rounded-lg">
                                            <div className="text-xs text-yellow-600 font-medium mb-1">Fee Amount</div>
                                            <div className="text-lg font-bold text-yellow-900">
                                              {formatCurrency((labRecord as any)?.fee_amount || 0)}
                                            </div>
                                          </div>
                                          <div className="text-center p-3 bg-yellow-50 rounded-lg">
                                            <div className="text-xs text-yellow-600 font-medium mb-1">Payment Status</div>
                                            <div className={`text-sm font-bold ${(labRecord as any)?.fee_paid ? 'text-green-700' : 'text-red-700'}`}>
                                              {(labRecord as any)?.fee_paid ? '✅ PAID' : '❌ PENDING'}
                                            </div>
                                          </div>
                                        </div>
                                        {!((labRecord as any)?.fee_paid) && (labRecord as any)?.fee_amount > 0 && (
                                          <div className="mt-3 p-2 bg-red-100 border border-red-300 rounded-lg text-center">
                                            <div className="text-sm text-red-700 font-medium flex items-center justify-center">
                                              <AlertCircle className="h-4 w-4 mr-2" />
                                              Payment Required Before Discharge
                                            </div>
                                          </div>
                                        )}
                                      </div>

                                      {/* Test Results */}
                                      {(labRecord as any)?.test_results && (
                                        <div className="bg-white p-4 rounded-lg border border-gray-200">
                                          <div className="text-sm font-semibold text-gray-900 mb-2 flex items-center">
                                            <Eye className="h-4 w-4 mr-2 text-blue-500" />
                                            Test Results
                                          </div>
                                          <div className="text-sm text-gray-700 bg-gray-50 p-3 rounded-lg">
                                            {(labRecord as any).test_results}
                                          </div>
                                        </div>
                                      )}

                                      {/* Reference Ranges */}
                                      {(labRecord as any)?.reference_ranges && (
                                        <div className="bg-white p-4 rounded-lg border border-gray-200">
                                          <div className="text-sm font-semibold text-gray-900 mb-2 flex items-center">
                                            <BarChart3 className="h-4 w-4 mr-2 text-purple-500" />
                                            Reference Ranges
                                          </div>
                                          <div className="text-sm text-gray-700 bg-purple-50 p-3 rounded-lg">
                                            {(labRecord as any).reference_ranges}
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                )}

                                {/* Action Section */}
                                <div className="pt-4 border-t border-gray-200">
                                  <div className="flex items-center justify-between mb-4">
                                    <Badge className="bg-blue-100 text-blue-800 border-blue-200 px-3 py-1">
                                      <CheckCircle className="h-3 w-3 mr-1" />
                                      Ready for Follow-up
                                    </Badge>
                                  </div>
                                  <Button
                                    className="w-full bg-green-600 hover:bg-green-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                                    onClick={() => handleSendToFollowUp(patient)}
                                  >
                                    <Stethoscope className="h-4 w-4 mr-2" />
                                    <span>Send to Consultation Follow-up</span>
                                  </Button>
                                </div>
                              </CardContent>
                            </Card>
                          );
                        })}
                      </div>
                    )}
                  </>
                )}
              </>
            )}
          </CardContent>
        </Card>

        {/* Laboratory Form Dialog */}
        <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            {selectedPatient && (
              <>
                <DialogHeader>
                  <DialogTitle>
                    {Array.isArray(laboratoryRecords) && laboratoryRecords.some((record: any) => record.patient_id === selectedPatient.id)
                      ? 'Edit Patient Record'
                      : 'Edit Patient Record'} - {selectedPatient.patient_name}
                  </DialogTitle>
                  <DialogDescription>
                    Age: {getPatientAge(selectedPatient.date_of_birth)} years •
                    Blood Type: {selectedPatient.blood_type || 'Unknown'} •
                    Patient ID: {selectedPatient.id}
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <LaboratoryForm
                    patientId={selectedPatient.id}
                    workflowId={selectedPatient.id}
                    onSuccess={handleLaboratoryComplete}
                    autoAdvance={true}
                    existingData={Array.isArray(laboratoryRecords) ? laboratoryRecords.find((record: any) => record.patient_id === selectedPatient.id) : null}
                    consultationData={Array.isArray(consultationRecords) ? consultationRecords.find((record: any) => record.patient_id === selectedPatient.id) : null}
                  />
                </div>
              </>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </Layout>
  );
};

export default LaboratoryDepartment;
