
import React, { useState } from 'react';
import { Layout } from '../components/Layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Plus, TrendingUp, TrendingDown, DollarSign, Receipt } from 'lucide-react';
import { RevenueForm } from '@/components/billingAccounting/RevenueForm';
import { ExpenseForm } from '@/components/billingAccounting/ExpenseForm';
import InvoiceForm from '@/components/billing/InvoiceForm';
import { useSupabaseData } from '@/hooks/useSupabaseData';
import { useCurrency } from '@/contexts/CurrencyContext';

const BillingAccounting = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [showRevenueForm, setShowRevenueForm] = useState(false);
  const [showExpenseForm, setShowExpenseForm] = useState(false);
  const [showInvoiceForm, setShowInvoiceForm] = useState(false);

  const { 
    useRevenueEntries, 
    useExpenseEntries, 
    useInvoices,
    createRevenueEntry,
    createExpenseEntry,
    createBillingInvoice
  } = useSupabaseData();

  const { formatCurrency } = useCurrency();
  const { data: revenueEntries = [] } = useRevenueEntries();
  const { data: expenseEntries = [] } = useExpenseEntries();
  const { data: invoices = [] } = useInvoices();

  // Calculate totals
  const totalRevenue = revenueEntries.reduce((sum, entry) => sum + Number(entry.amount), 0);
  const totalExpenses = expenseEntries.reduce((sum, entry) => sum + Number(entry.amount), 0);
  const netProfit = totalRevenue - totalExpenses;

  const recentRevenueEntries = revenueEntries.slice(0, 5);
  const recentExpenseEntries = expenseEntries.slice(0, 5);
  const recentInvoices = invoices.slice(0, 5);



  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  // Handle revenue form submission
  const handleRevenueSubmit = async (data: any) => {
    try {
      await createRevenueEntry.mutateAsync({
        description: data.description,
        amount: parseFloat(data.amount),
        date: data.date.toISOString().split('T')[0],
        category: data.category,
        payment_method: data.paymentMethod,
        notes: data.notes || null
      });
      setShowRevenueForm(false);
    } catch (error) {
      console.error('Error creating revenue entry:', error);
    }
  };

  // Handle expense form submission
  const handleExpenseSubmit = async (data: any) => {
    try {
      await createExpenseEntry.mutateAsync({
        description: data.description,
        amount: parseFloat(data.amount),
        date: data.date.toISOString().split('T')[0],
        category: data.category,
        vendor: data.vendor || 'Not specified',
        payment_method: data.paymentMethod,
        notes: data.notes || null
      });
      setShowExpenseForm(false);
    } catch (error) {
      console.error('Error creating expense entry:', error);
    }
  };

  // Handle invoice form submission
  const handleInvoiceSubmit = async (data: any) => {
    try {
      await createBillingInvoice.mutateAsync({
        patient: data.patientName,
        invoice_date: data.date,
        due_date: data.dueDate,
        status: data.status,
        invoice_items: data.items,
        total_revenue: data.amount,
        total_expenses: 0,
        net_profit_loss: data.amount,
        notes: data.notes || null
      });
      setShowInvoiceForm(false);
    } catch (error) {
      console.error('Error creating invoice:', error);
    }
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Billing & Accounting</h1>
            <p className="text-gray-600 mt-1">Manage revenue, expenses, and financial reports</p>
          </div>
          <div className="flex gap-3">
            <Button
              onClick={() => setShowRevenueForm(true)}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Sales
            </Button>
            <Button
              onClick={() => setShowExpenseForm(true)}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Expense
            </Button>
          </div>
        </div>

        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
              <TrendingUp className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{formatCurrency(totalRevenue)}</div>
              <p className="text-xs text-muted-foreground">All time revenue</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Expenses</CardTitle>
              <TrendingDown className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{formatCurrency(totalExpenses)}</div>
              <p className="text-xs text-muted-foreground">All time expenses</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Net Profit</CardTitle>
              <DollarSign className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${netProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {formatCurrency(netProfit)}
              </div>
              <p className="text-xs text-muted-foreground">Revenue - Expenses</p>
            </CardContent>
          </Card>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="revenue">Revenue</TabsTrigger>
            <TabsTrigger value="expenses">Expenses</TabsTrigger>
            <TabsTrigger value="invoices">Invoices</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="mt-4">
            {/* Quick Actions */}
            <Card className="mb-6">
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-4">
                  <Button
                    onClick={() => setShowRevenueForm(true)}
                    className="bg-green-600 hover:bg-green-700 text-white"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Record Sales/Revenue
                  </Button>
                  <Button
                    onClick={() => setShowExpenseForm(true)}
                    className="bg-red-600 hover:bg-red-700 text-white"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Record Expense
                  </Button>
                  <Button
                    onClick={() => setShowInvoiceForm(true)}
                    variant="outline"
                  >
                    <Receipt className="h-4 w-4 mr-2" />
                    Create Invoice
                  </Button>
                </div>
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Recent Revenue */}
              <Card>
                <CardHeader>
                  <CardTitle>Recent Revenue</CardTitle>
                </CardHeader>
                <CardContent>
                  {recentRevenueEntries.length === 0 ? (
                    <p className="text-gray-500 text-center py-4">No revenue entries yet</p>
                  ) : (
                    <div className="space-y-3">
                      {recentRevenueEntries.map((entry) => (
                        <div key={entry.id} className="flex justify-between items-center">
                          <div>
                            <p className="font-medium">{entry.description}</p>
                            <p className="text-sm text-gray-500">{formatDate(entry.date)}</p>
                          </div>
                          <span className="text-green-600 font-medium">
                            {formatCurrency(Number(entry.amount))}
                          </span>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Recent Expenses */}
              <Card>
                <CardHeader>
                  <CardTitle>Recent Expenses</CardTitle>
                </CardHeader>
                <CardContent>
                  {recentExpenseEntries.length === 0 ? (
                    <p className="text-gray-500 text-center py-4">No expense entries yet</p>
                  ) : (
                    <div className="space-y-3">
                      {recentExpenseEntries.map((entry) => (
                        <div key={entry.id} className="flex justify-between items-center">
                          <div>
                            <p className="font-medium">{entry.description}</p>
                            <p className="text-sm text-gray-500">{formatDate(entry.date)}</p>
                          </div>
                          <span className="text-red-600 font-medium">
                            -{formatCurrency(Number(entry.amount))}
                          </span>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="revenue" className="mt-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Revenue Management</CardTitle>
                <Button 
                  onClick={() => setShowRevenueForm(true)}
                  variant="default"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Revenue
                </Button>
              </CardHeader>
              <CardContent>
                {revenueEntries.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-gray-500 mb-4">No revenue entries yet</p>
                    <Button 
                      onClick={() => setShowRevenueForm(true)}
                      variant="outline"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add Your First Revenue Entry
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {revenueEntries.map((entry) => (
                      <div key={entry.id} className="border rounded-lg p-4">
                        <div className="flex justify-between items-start">
                          <div>
                            <h3 className="font-medium">{entry.description}</h3>
                            <p className="text-sm text-gray-600 mt-1">Category: {entry.category}</p>
                            <p className="text-sm text-gray-600">Date: {formatDate(entry.date)}</p>
                            <p className="text-sm text-gray-600">Payment Method: {entry.payment_method}</p>
                            {entry.notes && (
                              <p className="text-sm text-gray-600 mt-2">Notes: {entry.notes}</p>
                            )}
                          </div>
                          <span className="text-lg font-semibold text-green-600">
                            {formatCurrency(Number(entry.amount))}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="expenses" className="mt-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Expense Management</CardTitle>
                <Button 
                  onClick={() => setShowExpenseForm(true)}
                  variant="default"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Expense
                </Button>
              </CardHeader>
              <CardContent>
                {expenseEntries.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-gray-500 mb-4">No expense entries yet</p>
                    <Button 
                      onClick={() => setShowExpenseForm(true)}
                      variant="outline"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add Your First Expense Entry
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {expenseEntries.map((entry) => (
                      <div key={entry.id} className="border rounded-lg p-4">
                        <div className="flex justify-between items-start">
                          <div>
                            <h3 className="font-medium">{entry.description}</h3>
                            <p className="text-sm text-gray-600 mt-1">Category: {entry.category}</p>
                            {entry.vendor && entry.vendor !== 'Not specified' && (
                              <p className="text-sm text-gray-600">Vendor: {entry.vendor}</p>
                            )}
                            <p className="text-sm text-gray-600">Date: {formatDate(entry.date)}</p>
                            <p className="text-sm text-gray-600">Payment Method: {entry.payment_method}</p>
                            {entry.notes && (
                              <p className="text-sm text-gray-600 mt-2">Notes: {entry.notes}</p>
                            )}
                          </div>
                          <span className="text-lg font-semibold text-red-600">
                            -{formatCurrency(Number(entry.amount))}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="invoices" className="mt-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Invoice Management</CardTitle>
                <Button 
                  onClick={() => setShowInvoiceForm(true)}
                  variant="default"
                >
                  <Receipt className="h-4 w-4 mr-2" />
                  Create Invoice
                </Button>
              </CardHeader>
              <CardContent>
                {invoices.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-gray-500 mb-4">No invoices yet</p>
                    <Button 
                      onClick={() => setShowInvoiceForm(true)}
                      variant="outline"
                    >
                      <Receipt className="h-4 w-4 mr-2" />
                      Create Your First Invoice
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {invoices.map((invoice) => (
                      <div key={invoice.id} className="border rounded-lg p-4">
                        <div className="flex justify-between items-start">
                          <div>
                            <h3 className="font-medium">Invoice for {invoice.patient}</h3>
                            <p className="text-sm text-gray-600 mt-1">Date: {formatDate(invoice.invoice_date)}</p>
                            <p className="text-sm text-gray-600">Due: {formatDate(invoice.due_date)}</p>
                            <p className="text-sm text-gray-600">Status: {invoice.status}</p>
                            {invoice.notes && (
                              <p className="text-sm text-gray-600 mt-2">Notes: {invoice.notes}</p>
                            )}
                          </div>
                          <div className="text-right">
                            <span className="text-lg font-semibold">
                              {formatCurrency(Number(invoice.net_profit_loss))}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Modals */}
        {showRevenueForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <h2 className="text-xl font-semibold mb-4">Add Revenue Entry</h2>
                <RevenueForm 
                  onClose={() => setShowRevenueForm(false)} 
                  onSubmit={handleRevenueSubmit}
                />
              </div>
            </div>
          </div>
        )}

        {showExpenseForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <h2 className="text-xl font-semibold mb-4">Add Expense Entry</h2>
                <ExpenseForm 
                  onClose={() => setShowExpenseForm(false)} 
                  onSubmit={handleExpenseSubmit}
                />
              </div>
            </div>
          </div>
        )}

        {showInvoiceForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <InvoiceForm 
                  onClose={() => setShowInvoiceForm(false)}
                  onSubmit={handleInvoiceSubmit}
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default BillingAccounting;
