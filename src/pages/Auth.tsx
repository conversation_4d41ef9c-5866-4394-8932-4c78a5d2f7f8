
import React, { useState, useEffect } from 'react';
import { LandingHeader } from '../components/landing/LandingHeader';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';

const Auth = () => {
  const location = useLocation();
  const [isRegistering, setIsRegistering] = useState(location.pathname === '/register');
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { signIn, signUp, user, loading: authLoading } = useAuth();
  const { toast } = useToast();

  // Update form mode based on route
  useEffect(() => {
    setIsRegistering(location.pathname === '/register');
  }, [location.pathname]);

  // Redirect if already logged in
  useEffect(() => {
    if (user) {
      navigate('/dashboard');
    }
  }, [user, navigate]);

  const toggleForm = () => {
    if (isRegistering) {
      navigate('/login');
    } else {
      navigate('/register');
    }
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    const formData = new FormData(e.target as HTMLFormElement);
    const email = formData.get('email') as string;
    const password = formData.get('password') as string;

    try {
      const { error } = await signIn(email, password);

      if (error) {
        let errorMessage = "Invalid email or password";

        // Handle specific error types
        if (error.message?.includes('timeout') || error.message?.includes('network')) {
          errorMessage = "Connection timeout. Please check your internet connection and try again.";
        } else if (error.message?.includes('Invalid login credentials')) {
          errorMessage = "Invalid email or password. Please check your credentials.";
        } else if (error.message) {
          errorMessage = error.message;
        }

        toast({
          title: "Login Failed",
          description: errorMessage,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Welcome back!",
          description: "You have been logged in successfully.",
        });
        navigate('/dashboard');
      }
    } catch (error) {
      toast({
        title: "Login Failed",
        description: "Connection error. Please check your internet connection and try again.",
        variant: "destructive",
      });
    }
    
    setLoading(false);
  };

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    const formData = new FormData(e.target as HTMLFormElement);
    const hospitalName = formData.get('hospital-name') as string;
    const hospitalType = formData.get('hospital-type') as string;
    const contactName = formData.get('contact-name') as string;
    const email = formData.get('hospital-email') as string;
    const phone = formData.get('phone-number') as string;
    const address = formData.get('hospital-address') as string;
    const password = formData.get('password') as string;
    const confirmPassword = formData.get('confirm-password') as string;

    if (password !== confirmPassword) {
      toast({
        title: "Registration Failed",
        description: "Passwords do not match",
        variant: "destructive",
      });
      setLoading(false);
      return;
    }

    const { error } = await signUp(email, password, {
      hospital_name: hospitalName,
      hospital_type: hospitalType,
      contact_name: contactName,
      phone_number: phone,
      address: address
    });
    
    if (error) {
      toast({
        title: "Registration Failed",
        description: error.message,
        variant: "destructive",
      });
    } else {
      toast({
        title: "Registration Successful!",
        description: "Please check your email to confirm your account.",
      });
    }
    
    setLoading(false);
  };

  // Show loading spinner while auth context is initializing
  if (authLoading) {
    return (
      <div className="min-h-screen bg-gray-100">
        <LandingHeader />
        <div className="flex items-center justify-center min-h-[calc(100vh-64px)] py-8 px-4">
          <Card className="w-full max-w-sm">
            <CardContent className="flex items-center justify-center py-8">
              <div className="flex flex-col items-center space-y-4">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <p className="text-sm text-gray-600">Initializing authentication...</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <LandingHeader />
      <div className="flex items-center justify-center min-h-[calc(100vh-64px)] py-8 px-4">
        <Card className="w-full max-w-sm">
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl text-center">
              {isRegistering ? 'Register Your Hospital' : 'Login to your account'}
            </CardTitle>
            <CardDescription className="text-center">
              {isRegistering ? 'Enter your hospital details to create an account' : 'Enter your email and password to login'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isRegistering ? (
              <form onSubmit={handleRegister} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="hospital-name">Hospital Name</Label>
                  <Input id="hospital-name" name="hospital-name" type="text" placeholder="Enter hospital name" required />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="hospital-type">Hospital Type</Label>
                  <Input id="hospital-type" name="hospital-type" type="text" placeholder="e.g., General, Specialty, Teaching" required />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="contact-name">Contact Person Name</Label>
                  <Input id="contact-name" name="contact-name" type="text" placeholder="Enter your name" required />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="hospital-email">Hospital Email</Label>
                  <Input id="hospital-email" name="hospital-email" type="email" placeholder="Enter hospital email" required />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone-number">Phone Number</Label>
                  <Input id="phone-number" name="phone-number" type="tel" placeholder="Enter phone number" required />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="hospital-address">Hospital Address</Label>
                  <Input id="hospital-address" name="hospital-address" type="text" placeholder="Enter hospital address" required />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="password">Password</Label>
                  <Input id="password" name="password" type="password" required />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="confirm-password">Confirm Password</Label>
                  <Input id="confirm-password" name="confirm-password" type="password" required />
                </div>
                <Button type="submit" className="w-full" disabled={loading}>
                  {loading ? 'Registering...' : 'Register'}
                </Button>
              </form>
            ) : (
              <form onSubmit={handleLogin} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email-login">Email</Label>
                  <Input id="email-login" name="email" type="email" placeholder="<EMAIL>" required />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="password-login">Password</Label>
                  <Input id="password-login" name="password" type="password" required />
                </div>
                <Button type="submit" className="w-full" disabled={loading}>
                  {loading ? 'Logging in...' : 'Login'}
                </Button>
              </form>
            )}
          </CardContent>
          <CardFooter className="flex justify-center">
            <Button variant="link" onClick={toggleForm} disabled={loading}>
              {isRegistering ? 'Already have an account? Login' : 'Don\'t have an account? Sign Up'}
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default Auth;
