import React, { useState } from 'react';
import { Layout } from '../components/Layout';
import { 
  Search, 
  Filter, 
  Plus, 
  BarChart2, 
  Package, 
  AlertTriangle,
  Download,
  Upload
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { InventoryTable } from '@/components/inventory/InventoryTable';
import { InventoryForm } from '@/components/inventory/InventoryForm';

const Inventory = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [activeCategory, setActiveCategory] = useState('all');

  // Sample data for inventory items
  const inventoryItems = [
    {
      id: 'INV-001',
      name: 'Surgical Gloves',
      category: 'Surgical Supplies',
      quantity: 500,
      unit: 'pairs',
      status: 'in-stock' as const,
      lastUpdated: '2025-05-28',
      expiryDate: '2026-05-28',
      location: 'Main Storage',
      supplier: 'MedSupply Co.',
      reorderLevel: 100
    },
    {
      id: 'INV-002',
      name: 'Paracetamol 500mg',
      category: 'Medications',
      quantity: 200,
      unit: 'boxes',
      status: 'low-stock' as const,
      lastUpdated: '2025-05-30',
      expiryDate: '2026-01-15',
      location: 'Pharmacy',
      supplier: 'PharmaMed Inc.',
      reorderLevel: 250
    },
    {
      id: 'INV-003',
      name: 'Blood Pressure Monitor',
      category: 'Diagnostic Equipment',
      quantity: 15,
      unit: 'pcs',
      status: 'in-stock' as const,
      lastUpdated: '2025-05-20',
      expiryDate: null,
      location: 'General Ward',
      supplier: 'Medical Devices Ltd.',
      reorderLevel: 5
    },
    {
      id: 'INV-004',
      name: 'Disposable Syringes',
      category: 'Surgical Supplies',
      quantity: 1000,
      unit: 'pcs',
      status: 'in-stock' as const,
      lastUpdated: '2025-06-01',
      expiryDate: '2027-06-01',
      location: 'Main Storage',
      supplier: 'MedSupply Co.',
      reorderLevel: 200
    },
    {
      id: 'INV-005',
      name: 'Antibacterial Soap',
      category: 'Cleaning Supplies',
      quantity: 50,
      unit: 'bottles',
      status: 'in-stock' as const,
      lastUpdated: '2025-05-25',
      expiryDate: '2026-05-25',
      location: 'Main Storage',
      supplier: 'Healthcare Essentials',
      reorderLevel: 20
    },
    {
      id: 'INV-006',
      name: 'N95 Respirator Masks',
      category: 'Personal Protective Equipment',
      quantity: 100,
      unit: 'boxes',
      status: 'low-stock' as const,
      lastUpdated: '2025-05-29',
      expiryDate: '2027-05-29',
      location: 'Emergency Room',
      supplier: 'Healthcare Essentials',
      reorderLevel: 150
    },
    {
      id: 'INV-007',
      name: 'Blood Collection Tubes',
      category: 'Laboratory Supplies',
      quantity: 0,
      unit: 'boxes',
      status: 'out-of-stock' as const,
      lastUpdated: '2025-05-15',
      expiryDate: null,
      location: 'Laboratory',
      supplier: 'LabTech Solutions',
      reorderLevel: 30
    }
  ];

  // Filter inventory items based on search term and active category
  const filteredItems = inventoryItems.filter(item => {
    const matchesSearch = 
      item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.category.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = activeCategory === 'all' || item.category === activeCategory;
    
    return matchesSearch && matchesCategory;
  });

  // Get unique categories for filter
  const categories = ['all', ...new Set(inventoryItems.map(item => item.category))];

  // Get low stock and out of stock items
  const lowStockItems = inventoryItems.filter(item => item.status === 'low-stock');
  const outOfStockItems = inventoryItems.filter(item => item.status === 'out-of-stock');

  const handleEditItem = (item) => {
    setSelectedItem(item);
    setIsFormOpen(true);
  };

  const handleDeleteItem = (id) => {
    // In a real app, you would call an API to delete the item
    console.log(`Delete item with ID: ${id}`);
  };

  const handleCloseForm = () => {
    setIsFormOpen(false);
    setSelectedItem(null);
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Inventory Management</h1>
            <p className="text-gray-600 mt-1">Manage hospital supplies and equipment</p>
          </div>
          <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
            <DialogTrigger asChild>
              <Button className="bg-purple-600 hover:bg-purple-700 text-white">
                <Plus className="mr-2 h-4 w-4" />
                Add Item
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <InventoryForm existingItem={selectedItem} onClose={handleCloseForm} />
            </DialogContent>
          </Dialog>
        </div>

        {/* Dashboard Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Items</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{inventoryItems.length}</div>
              <p className="text-xs text-muted-foreground">
                Across {categories.length - 1} categories
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Low Stock Items</CardTitle>
              <AlertTriangle className="h-4 w-4 text-yellow-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{lowStockItems.length}</div>
              <p className="text-xs text-muted-foreground">
                Items below reorder level
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Out of Stock</CardTitle>
              <AlertTriangle className="h-4 w-4 text-red-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{outOfStockItems.length}</div>
              <p className="text-xs text-muted-foreground">
                Items requiring immediate reorder
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filter */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <input
              type="text"
              placeholder="Search inventory by name, ID, or category..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>
          <div className="flex gap-2">
            <Button variant="outline" className="flex items-center gap-2">
              <Filter size={16} />
              <span>Filter</span>
            </Button>
            <Button variant="outline" className="flex items-center gap-2">
              <Download size={16} />
              <span>Export</span>
            </Button>
            <Button variant="outline" className="flex items-center gap-2">
              <Upload size={16} />
              <span>Import</span>
            </Button>
          </div>
        </div>

        {/* Category Tabs */}
        <Tabs defaultValue="all" value={activeCategory} onValueChange={setActiveCategory}>
          <TabsList className="overflow-x-auto flex flex-nowrap">
            {categories.map((category) => (
              <TabsTrigger 
                key={category} 
                value={category}
                className="capitalize"
              >
                {category === 'all' ? 'All Items' : category}
              </TabsTrigger>
            ))}
          </TabsList>
          
          {/* Inventory Table */}
          <TabsContent value={activeCategory} className="mt-4">
            <InventoryTable 
              items={filteredItems} 
              onEdit={handleEditItem} 
              onDelete={handleDeleteItem} 
            />
          </TabsContent>
        </Tabs>

        {/* Pagination */}
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-600">Showing {filteredItems.length} of {inventoryItems.length} items</p>
          <div className="flex space-x-2">
            <Button variant="outline" size="sm">Previous</Button>
            <Button variant="outline" size="sm">Next</Button>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Inventory;
