import React, { useState, useEffect } from 'react';
import { Layout } from '../components/Layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import {
  Stethoscope,
  Search,
  ArrowRight,
  Clock,
  Phone,
  Mail,
  User,
  Heart,
  Activity,
  RefreshCw
} from 'lucide-react';
import { useWorkflowData } from '@/hooks/useWorkflowData';
import { useSupabaseData } from '@/hooks/useSupabaseData';
import { useCurrency } from '@/contexts/CurrencyContext';
import { ConsultationForm } from '@/components/workflow/ConsultationForm';
import { CurrentWorkflowStatus } from '@/components/workflow/WorkflowStatusIndicator';
import { useNavigate } from 'react-router-dom';
import { toast } from '@/hooks/use-toast';
import { useQueryClient } from '@tanstack/react-query';

const ConsultationDepartment = () => {
  const navigate = useNavigate();
  const { formatCurrency } = useCurrency();
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPatient, setSelectedPatient] = useState<any>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('initial'); // 'initial', 'followup', or 'completed'
  const [isRefreshing, setIsRefreshing] = useState(false);

  const {
    useWorkflowStats,
    useConsultationRecords,
    useLaboratoryRecords,
    completeStageAndAdvance,
    createConsultationRecord
  } = useWorkflowData();

  const { usePatients, createPatient } = useSupabaseData();

  const { data: allPatients = [], isLoading: patientsLoading } = usePatients();
  const { data: stats } = useWorkflowStats();
  const { data: consultationRecords = [], isLoading: consultationLoading, error: consultationError } = useConsultationRecords();
  const { data: laboratoryRecords = [], isLoading: laboratoryLoading, error: laboratoryError } = useLaboratoryRecords();

  // Debug logging (can be removed in production)
  if (process.env.NODE_ENV === 'development') {
    console.log('Consultation Department - Patients:', allPatients.length, 'Consultation Records:', consultationRecords.length, 'Laboratory Records:', laboratoryRecords.length);
    if (consultationError) console.warn('Consultation error:', consultationError);
    if (laboratoryError) console.warn('Laboratory error:', laboratoryError);
  }

  // Helper function to check if patient needs follow-up consultation
  const needsFollowUpConsultation = (patient: any) => {
    try {
      if (!Array.isArray(laboratoryRecords) || !Array.isArray(consultationRecords)) {
        return false;
      }
      const hasLabRecord = laboratoryRecords.some(record => record.patient_id === patient.id);
      const hasFollowUpConsultation = Array.isArray(consultationRecords) && consultationRecords.some((record: any) =>
        record.patient_id === patient.id && record.consultation_type === 'follow_up'
      );
      return hasLabRecord && !hasFollowUpConsultation;
    } catch (error) {
      console.error('Error in needsFollowUpConsultation:', error);
      return false;
    }
  };

  // Helper function to get patient's current workflow status
  const getPatientWorkflowStatus = (patient: any) => {
    if (!Array.isArray(laboratoryRecords) || !Array.isArray(consultationRecords)) {
      return 'reception';
    }
    const hasInitialConsultation = Array.isArray(consultationRecords) && consultationRecords.some((record: any) =>
      record.patient_id === patient.id && (record.consultation_type === 'initial' || !record.consultation_type)
    );
    const hasLabRecord = Array.isArray(laboratoryRecords) && laboratoryRecords.some((record: any) => record.patient_id === patient.id);
    const hasFollowUpConsultation = Array.isArray(consultationRecords) && consultationRecords.some((record: any) =>
      record.patient_id === patient.id && record.consultation_type === 'follow_up'
    );

    if (hasFollowUpConsultation) return 'completed';
    if (needsFollowUpConsultation(patient)) return 'follow_up_consultation';
    if (hasLabRecord) return 'laboratory';
    if (hasInitialConsultation) return 'consultation';
    return 'reception';
  };

  // Categorize patients into three groups

  // Initial Consultations: Patients who need their first consultation
  const initialConsultationPatients = allPatients.filter(patient => {
    if (consultationError || consultationLoading || !Array.isArray(consultationRecords)) {
      return true; // Show all patients if data failed to load or is still loading
    }

    const hasInitialConsultation = Array.isArray(consultationRecords) && consultationRecords.some((record: any) =>
      record.patient_id === patient.id && (record.consultation_type === 'initial' || !record.consultation_type)
    );

    // Show patients who haven't had initial consultation yet
    return !hasInitialConsultation;
  });

  // Follow-up Consultations: Patients who completed lab tests and need follow-up
  const followUpConsultationPatients = allPatients.filter(patient => {
    if (consultationError || laboratoryError || consultationLoading || laboratoryLoading ||
        !Array.isArray(consultationRecords) || !Array.isArray(laboratoryRecords)) {
      return false;
    }

    // Must have initial consultation and lab record, but no follow-up consultation yet
    const hasInitialConsultation = Array.isArray(consultationRecords) && consultationRecords.some((record: any) =>
      record.patient_id === patient.id && (record.consultation_type === 'initial' || !record.consultation_type)
    );

    return hasInitialConsultation && needsFollowUpConsultation(patient);
  });

  // Completed Consultations: Patients who have finished all required consultations
  const completedConsultationPatients = (consultationError || !Array.isArray(consultationRecords)) ? [] : allPatients.filter(patient => {
    const hasInitialConsultation = Array.isArray(consultationRecords) && consultationRecords.some((record: any) =>
      record.patient_id === patient.id && (record.consultation_type === 'initial' || !record.consultation_type)
    );
    const hasFollowUpConsultation = Array.isArray(consultationRecords) && consultationRecords.some((record: any) =>
      record.patient_id === patient.id && record.consultation_type === 'follow_up'
    );

    // Show patients who have completed all required consultations
    return hasInitialConsultation && (!needsFollowUpConsultation(patient) || hasFollowUpConsultation);
  });

  // Filter based on search term
  const filteredInitialPatients = initialConsultationPatients.filter(patient =>
    patient.patient_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.phone_number.includes(searchTerm)
  );

  const filteredFollowUpPatients = followUpConsultationPatients.filter(patient =>
    patient.patient_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.phone_number.includes(searchTerm)
  );

  const filteredCompletedPatients = completedConsultationPatients.filter(patient =>
    patient.patient_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.phone_number.includes(searchTerm)
  );

  // Auto-switch to follow-up tab when patients are waiting for follow-up
  useEffect(() => {
    if (followUpConsultationPatients.length > 0 && initialConsultationPatients.length === 0 && activeTab === 'initial') {
      setActiveTab('followup');
    }
  }, [followUpConsultationPatients.length, initialConsultationPatients.length, activeTab]);

  const handleStartConsultation = (patient: any, consultationType: 'initial' | 'follow_up' = 'initial') => {
    setSelectedPatient({
      ...patient,
      consultationType: consultationType
    });
    setIsFormOpen(true);
  };

  const handleConsultationComplete = async (consultationData: any) => {
    if (!selectedPatient) return;

    try {
      // The ConsultationForm already created the record, so we just need to handle the success
      console.log('Consultation completed for patient:', selectedPatient.id);

      // Show success message
      toast({
        title: "Success",
        description: "Consultation record created successfully! Patient moved to edited records.",
      });

      // Close dialog and clear selection
      setIsFormOpen(false);
      setSelectedPatient(null);

      // Switch to appropriate tab based on consultation type
      const consultationType = selectedPatient?.consultationType;
      if (consultationType === 'follow_up') {
        setActiveTab('completed'); // Follow-up consultation completes the workflow
      } else {
        // Initial consultation - patient will go to lab, then return for follow-up
        // Don't switch tabs immediately, let the patient flow naturally
        setActiveTab('initial'); // Stay on initial tab to see remaining patients
      }

    } catch (error) {
      console.error('Error completing consultation:', error);
      toast({
        title: "Error",
        description: "Failed to save consultation record. Please try again.",
        variant: "destructive"
      });
    }
  };

  const createTestPatient = async () => {
    try {
      await createPatient.mutateAsync({
        patient_name: 'Test Patient',
        email: '<EMAIL>',
        phone_number: '+**********',
        date_of_birth: '1990-01-01',
        gender: 'male',
        blood_type: 'O+'
      });
      console.log('Test patient created');
    } catch (error) {
      console.error('Error creating test patient:', error);
    }
  };

  // Refresh data function
  const handleRefreshData = async () => {
    setIsRefreshing(true);
    try {
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: ['patients'] }),
        queryClient.invalidateQueries({ queryKey: ['consultation_records'] }),
        queryClient.invalidateQueries({ queryKey: ['laboratory_records'] }),
        queryClient.invalidateQueries({ queryKey: ['workflow_stats'] })
      ]);

      toast({
        title: "Data Refreshed",
        description: "All patient data has been refreshed successfully.",
      });
    } catch (error) {
      console.error('Error refreshing data:', error);
      toast({
        title: "Refresh Failed",
        description: "Failed to refresh data. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  const getPatientAge = (dateOfBirth: string) => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return age;
  };



  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Consultation Department</h1>
            <p className="text-gray-600 mt-1">Patient consultations and medical examinations</p>
          </div>
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={handleRefreshData}
              disabled={isRefreshing}
            >
              <RefreshCw className={`mr-2 h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              {isRefreshing ? 'Refreshing...' : 'Refresh Data'}
            </Button>
            <Button
              variant="outline"
              onClick={() => navigate('/medical-records')}
            >
              View Medical Records
            </Button>
            <Button
              variant="outline"
              onClick={() => navigate('/workflow')}
            >
              View Workflow Dashboard
            </Button>
          </div>
        </div>

        {/* Error Handling */}
        {consultationError && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  Error loading consultation records
                </h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>There was an issue loading consultation records. The page will still function, but some data may not be available.</p>
                  <p className="mt-1 text-xs">Error: {consultationError?.message || 'Unknown error'}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {laboratoryError && (
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-orange-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-orange-800">
                  Error loading laboratory records
                </h3>
                <div className="mt-2 text-sm text-orange-700">
                  <p>There was an issue loading laboratory records. Follow-up consultations may not display correctly.</p>
                  <p className="mt-1 text-xs">Error: {laboratoryError?.message || 'Unknown error'}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Loading State */}
        {(consultationLoading || laboratoryLoading) && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="animate-spin h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-blue-800">
                  Loading patient data...
                </h3>
                <div className="mt-1 text-sm text-blue-700">
                  <p>Please wait while we fetch the latest consultation and laboratory records.</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Search Bar */}
        <div className="flex justify-between items-center">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search patients by name, email, or phone..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-80"
            />
          </div>
        </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-orange-100 rounded-lg mr-4">
                <Clock className="h-6 w-6 text-orange-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900">{filteredInitialPatients.length}</p>
                <p className="text-sm text-gray-600">From Reception</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg mr-4">
                <Activity className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900">{filteredFollowUpPatients.length}</p>
                <p className="text-sm text-gray-600">Follow-up Consultations</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg mr-4">
                <Stethoscope className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900">{filteredCompletedPatients.length}</p>
                <p className="text-sm text-gray-600">Completed Consultations</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg mr-4">
                <Activity className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900">{allPatients.length}</p>
                <p className="text-sm text-gray-600">Total Patients</p>
                {patientsLoading && <p className="text-xs text-blue-600">Loading...</p>}
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg mr-4">
                <Heart className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900">
                  {Array.isArray(consultationRecords) ? consultationRecords.filter((r: any) => {
                    const today = new Date().toDateString();
                    return new Date(r.consultation_date).toDateString() === today;
                  }).length : 0}
                </p>
                <p className="text-sm text-gray-600">Today's Consultations</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs for Reception, Follow-up, and Completed Consultations */}
      <div className="flex flex-wrap space-x-1 bg-gray-100 p-1 rounded-lg w-full md:w-fit">
        <Button
          variant={activeTab === 'initial' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setActiveTab('initial')}
          className="relative"
        >
          From Reception
          <Badge className="ml-2 bg-orange-500 text-white">
            {filteredInitialPatients.length}
          </Badge>
        </Button>
        <Button
          variant={activeTab === 'followup' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setActiveTab('followup')}
          className="relative flex-shrink-0"
        >
          Follow-up Consultations
          <Badge className="ml-2 bg-blue-500 text-white">
            {filteredFollowUpPatients.length}
          </Badge>
        </Button>
        <Button
          variant={activeTab === 'completed' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setActiveTab('completed')}
          className="relative"
        >
          Completed Consultations
          <Badge className="ml-2 bg-green-500 text-white">
            {filteredCompletedPatients.length}
          </Badge>
        </Button>
      </div>

      {/* Patient Lists */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            {activeTab === 'initial' ? (
              <>
                <Clock className="mr-2 h-5 w-5 text-orange-600" />
                Patients From Reception ({filteredInitialPatients.length})
              </>
            ) : activeTab === 'followup' ? (
              <>
                <Activity className="mr-2 h-5 w-5 text-blue-600" />
                Follow-up Consultation Patients ({filteredFollowUpPatients.length})
              </>
            ) : (
              <>
                <Stethoscope className="mr-2 h-5 w-5 text-green-600" />
                Completed Consultation Patients ({filteredCompletedPatients.length})
              </>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {patientsLoading ? (
            <div className="text-center py-8">
              <div className="text-gray-500">Loading patients...</div>
            </div>
          ) : allPatients.length === 0 ? (
            <div className="text-center py-8">
              <User className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <p className="text-gray-500">No patients found in the system</p>
              <p className="text-sm text-gray-400 mt-2">
                Please add patients first in the Patients page
              </p>
              <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                <p className="text-sm text-blue-800">
                  <strong>Debug Info:</strong><br/>
                  Total Patients: {allPatients.length}<br/>
                  Loading: {patientsLoading ? 'Yes' : 'No'}<br/>
                  Consultation Records: {consultationRecords.length}
                </p>
                <div className="flex gap-2 mt-3">
                  <Button
                    onClick={() => navigate('/patients')}
                  >
                    Go to Patients Page to Add Patients
                  </Button>
                  <Button
                    onClick={createTestPatient}
                    variant="outline"
                  >
                    Create Test Patient
                  </Button>
                </div>
              </div>
            </div>
          ) : (
            <>
              {/* From Reception Tab */}
              {activeTab === 'initial' && (
                <>
                  {filteredInitialPatients.length === 0 ? (
                    <div className="text-center py-8">
                      <Clock className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                      <p className="text-gray-500">
                        {searchTerm ? 'No patients found matching your search' : 'No patients from reception'}
                      </p>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {filteredInitialPatients.map((patient) => (
                        <Card key={patient.id} className="hover:shadow-md transition-shadow">
                          <CardContent className="p-4">
                            <div className="flex items-start justify-between mb-3">
                              <div className="flex items-center space-x-2">
                                <div className="p-1 bg-orange-100 rounded">
                                  <User className="h-4 w-4 text-orange-600" />
                                </div>
                                <div>
                                  <h3 className="font-medium text-gray-900">
                                    {patient.patient_name}
                                  </h3>
                                  <p className="text-xs text-gray-500">
                                    Age: {getPatientAge(patient.date_of_birth)} years
                                  </p>
                                </div>
                              </div>
                              <Badge variant="outline" className="text-xs bg-orange-50 text-orange-700">
                                From Reception
                              </Badge>
                            </div>
                    
                            <div className="space-y-2 mb-4">
                              <div className="flex items-center text-sm text-gray-600">
                                <Phone className="h-3 w-3 mr-2" />
                                {patient.phone_number}
                              </div>
                              <div className="flex items-center text-sm text-gray-600">
                                <Mail className="h-3 w-3 mr-2" />
                                {patient.email}
                              </div>
                              {patient.blood_type && (
                                <div className="flex items-center text-sm text-gray-600">
                                  <Heart className="h-3 w-3 mr-2" />
                                  Blood Type: {patient.blood_type}
                                </div>
                              )}
                              <div className="flex items-center text-sm text-gray-500">
                                <Clock className="h-3 w-3 mr-2" />
                                Registered: {new Date(patient.created_at).toLocaleDateString()}
                              </div>

                              {/* Show lab information for follow-up patients */}
                              {(() => {
                                const labRecord = laboratoryRecords.find(record => record.patient_id === patient.id);
                                return labRecord && (
                                  <div className="mt-2 p-2 bg-blue-100 rounded-lg">
                                    <div className="flex items-center text-sm text-blue-800 mb-1">
                                      <Activity className="h-3 w-3 mr-2" />
                                      <strong>Lab Results Available</strong>
                                    </div>
                                    <div className="text-xs text-blue-700">
                                      Test: {labRecord.test_type} • Date: {new Date(labRecord.test_date).toLocaleDateString()}
                                    </div>
                                  </div>
                                );
                              })()}
                            </div>
                    
                            <div className="flex items-center justify-between">
                              <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700">
                                Patient ID: {patient.id.slice(0, 8)}...
                              </Badge>
                              <Button
                                size="sm"
                                className="bg-orange-600 hover:bg-orange-700"
                                onClick={() => handleStartConsultation(patient, 'initial')}
                              >
                                <Stethoscope className="h-4 w-4 mr-1" />
                                Start Consultation
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  )}
                </>
              )}

              {/* Follow-up Consultations Tab */}
              {activeTab === 'followup' && (
                <>
                  {filteredFollowUpPatients.length === 0 ? (
                    <div className="text-center py-8">
                      <Activity className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                      <p className="text-gray-500">
                        {searchTerm ? 'No patients found matching your search' : 'No patients need follow-up consultation'}
                      </p>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {filteredFollowUpPatients.map((patient) => (
                        <Card key={patient.id} className="hover:shadow-md transition-shadow border-blue-200 bg-blue-50">
                          <CardContent className="p-4">
                            <div className="flex items-start justify-between mb-3">
                              <div className="flex items-center space-x-2">
                                <div className="p-1 bg-blue-100 rounded">
                                  <Activity className="h-4 w-4 text-blue-600" />
                                </div>
                                <div>
                                  <h3 className="font-medium text-gray-900">
                                    {patient.patient_name}
                                  </h3>
                                  <p className="text-xs text-gray-500">
                                    Age: {getPatientAge(patient.date_of_birth)} years
                                  </p>
                                </div>
                              </div>
                              <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700">
                                Needs Follow-up
                              </Badge>
                            </div>

                            <div className="space-y-2 text-sm text-gray-600 mb-3">
                              <div className="flex items-center text-sm text-gray-500">
                                <Clock className="h-3 w-3 mr-2" />
                                Registered: {new Date(patient.created_at).toLocaleDateString()}
                              </div>

                              {/* Show lab information for follow-up patients */}
                              {(() => {
                                const labRecord = laboratoryRecords.find(record => record.patient_id === patient.id);
                                return labRecord && (
                                  <div className="mt-2 p-2 bg-blue-100 rounded-lg">
                                    <div className="flex items-center text-sm text-blue-800 mb-1">
                                      <Activity className="h-3 w-3 mr-2" />
                                      <strong>Lab Results Available</strong>
                                    </div>
                                    <div className="text-xs text-blue-700">
                                      Test: {labRecord.test_type} • Date: {new Date(labRecord.test_date).toLocaleDateString()}
                                    </div>
                                  </div>
                                );
                              })()}
                            </div>

                            <div className="flex items-center justify-between">
                              <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700">
                                Patient ID: {patient.id.slice(0, 8)}...
                              </Badge>
                              <Button
                                size="sm"
                                className="bg-blue-600 hover:bg-blue-700"
                                onClick={() => handleStartConsultation(patient, 'follow_up')}
                              >
                                <Activity className="h-4 w-4 mr-1" />
                                Start Follow-up
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  )}
                </>
              )}

              {/* Completed Consultations Tab */}
              {activeTab === 'completed' && (
                <>
                  {filteredCompletedPatients.length === 0 ? (
                    <div className="text-center py-8">
                      <Stethoscope className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                      <p className="text-gray-500">
                        {searchTerm ? 'No patients found matching your search' : 'No completed consultations yet'}
                      </p>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {filteredCompletedPatients.map((patient) => {
                        const consultationRecord = Array.isArray(consultationRecords) ? consultationRecords.find((record: any) => record.patient_id === patient.id) : null;
                        return (
                          <Card key={patient.id} className="hover:shadow-md transition-shadow border-green-200">
                            <CardContent className="p-4">
                              <div className="flex items-start justify-between mb-3">
                                <div className="flex items-center space-x-2">
                                  <div className="p-1 bg-green-100 rounded">
                                    <User className="h-4 w-4 text-green-600" />
                                  </div>
                                  <div>
                                    <h3 className="font-medium text-gray-900">
                                      {patient.patient_name}
                                    </h3>
                                    <p className="text-xs text-gray-500">
                                      Age: {getPatientAge(patient.date_of_birth)} years
                                    </p>
                                  </div>
                                </div>
                                <Badge className="text-xs bg-green-100 text-green-800">
                                  All Consultations Complete
                                </Badge>
                              </div>

                              <div className="space-y-2 mb-4">
                                <div className="flex items-center text-sm text-gray-600">
                                  <Phone className="h-3 w-3 mr-2" />
                                  {patient.phone_number}
                                </div>
                                <div className="flex items-center text-sm text-gray-600">
                                  <Mail className="h-3 w-3 mr-2" />
                                  {patient.email}
                                </div>
                                {consultationRecord && (
                                  <>
                                    <div className="flex items-center text-sm text-gray-600">
                                      <Stethoscope className="h-3 w-3 mr-2" />
                                      Doctor: {(consultationRecord as any)?.doctor_name}
                                    </div>
                                    <div className="flex items-center text-sm text-gray-600">
                                      <Clock className="h-3 w-3 mr-2" />
                                      Consulted: {(consultationRecord as any)?.consultation_date ? new Date((consultationRecord as any).consultation_date).toLocaleDateString() : 'N/A'}
                                    </div>
                                    {/* Consultation Times */}
                                    {((consultationRecord as any)?.start_time || (consultationRecord as any)?.end_time) && (
                                      <div className="bg-blue-50 p-2 rounded border border-blue-200 mt-2">
                                        <div className="text-xs font-medium text-blue-800 mb-1">⏰ Consultation Times</div>
                                        <div className="space-y-1">
                                          {(consultationRecord as any)?.start_time && (
                                            <div className="text-xs text-blue-700">
                                              <span className="font-medium">Start:</span> {(consultationRecord as any).start_time}
                                            </div>
                                          )}
                                          {(consultationRecord as any)?.end_time && (
                                            <div className="text-xs text-blue-700">
                                              <span className="font-medium">End:</span> {(consultationRecord as any).end_time}
                                            </div>
                                          )}
                                          {(consultationRecord as any)?.start_time && (consultationRecord as any)?.end_time && (
                                            <div className="text-xs text-blue-700">
                                              <span className="font-medium">Duration:</span>
                                              {(() => {
                                                const start = new Date(`2000-01-01T${(consultationRecord as any).start_time}`);
                                                const end = new Date(`2000-01-01T${(consultationRecord as any).end_time}`);
                                                const diffMs = end.getTime() - start.getTime();
                                                const diffMins = Math.round(diffMs / (1000 * 60));
                                                return ` ${Math.floor(diffMins / 60)}h ${diffMins % 60}m`;
                                              })()}
                                            </div>
                                          )}
                                        </div>
                                      </div>
                                    )}
                                    {(consultationRecord as any)?.diagnosis && (
                                      <div className="text-sm text-gray-700 bg-gray-50 p-2 rounded">
                                        <strong>Diagnosis:</strong> {(consultationRecord as any).diagnosis}
                                      </div>
                                    )}
                                    {/* Fee Information */}
                                    <div className="bg-gradient-to-r from-green-50 to-blue-50 p-3 rounded-lg border border-green-200 mt-2">
                                      <div className="text-xs font-bold text-green-800 mb-2 flex items-center">
                                        💰 Consultation Fee Status
                                      </div>
                                      <div className="grid grid-cols-2 gap-2">
                                        <div className="text-xs">
                                          <span className="font-medium text-green-700">Amount:</span>
                                          <div className="font-bold text-green-900">
                                            {formatCurrency((consultationRecord as any)?.fee_amount || 0)}
                                          </div>
                                        </div>
                                        <div className="text-xs">
                                          <span className="font-medium text-green-700">Status:</span>
                                          <div className={`font-bold ${(consultationRecord as any)?.fee_paid ? 'text-green-700' : 'text-red-700'}`}>
                                            {(consultationRecord as any)?.fee_paid ? '✅ PAID' : '❌ NOT PAID'}
                                          </div>
                                        </div>
                                      </div>
                                      {!((consultationRecord as any)?.fee_paid) && (consultationRecord as any)?.fee_amount > 0 && (
                                        <div className="mt-2 p-1 bg-red-100 border border-red-300 rounded text-xs text-red-700 font-medium">
                                          ⚠️ Payment Required
                                        </div>
                                      )}
                                    </div>
                                  </>
                                )}
                              </div>

                              <div className="flex items-center justify-between">
                                <Badge variant="outline" className="text-xs bg-green-50 text-green-700">
                                  Ready for Next Stage
                                </Badge>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleStartConsultation(patient)}
                                >
                                  <Stethoscope className="h-4 w-4 mr-1" />
                                  View/Edit Record
                                </Button>
                              </div>
                            </CardContent>
                          </Card>
                        );
                      })}
                    </div>
                  )}
                </>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Consultation Form Dialog */}
      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <DialogContent className="w-[95vw] max-w-[900px] max-h-[90vh] overflow-y-auto mx-2 sm:mx-auto">
          {selectedPatient && (
            <>
              <DialogHeader>
                <DialogTitle>
                  {Array.isArray(consultationRecords) && consultationRecords.some((record: any) => record.patient_id === selectedPatient.id)
                    ? 'Edit Patient Record'
                    : 'Edit Patient Record'} - {selectedPatient.patient_name}
                </DialogTitle>
                <DialogDescription>
                  Age: {getPatientAge(selectedPatient.date_of_birth)} years •
                  Blood Type: {selectedPatient.blood_type || 'Unknown'} •
                  Patient ID: {selectedPatient.id}
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
              <ConsultationForm
                patientId={selectedPatient.id}
                workflowId={selectedPatient.id} // Use patient ID as temporary workflow ID
                onSuccess={handleConsultationComplete}
                autoAdvance={true}
                existingData={Array.isArray(consultationRecords) ? consultationRecords.find((record: any) => record.patient_id === selectedPatient.id) : null}
                patientData={selectedPatient}
                consultationType={selectedPatient.consultationType || 'initial'}
              />
              </div>
            </>
          )}
        </DialogContent>
      </Dialog>
      </div>
    </Layout>
  );
};

export default ConsultationDepartment;
