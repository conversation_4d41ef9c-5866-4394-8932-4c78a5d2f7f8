import React, { useState } from 'react';
import { Layout } from '../components/Layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Pill, 
  Search, 
  Plus,
  Minus,
  ShoppingCart,
  Package,
  DollarSign,
  AlertTriangle,
  TrendingUp,
  Calculator,
  Receipt,
  Eye,
  Edit,
  Trash2
} from 'lucide-react';
import { useWorkflowData } from '@/hooks/useWorkflowData';
import { useSupabaseData } from '@/hooks/useSupabaseData';
import { useNavigate } from 'react-router-dom';
import { toast } from '@/hooks/use-toast';

interface Medicine {
  id: string;
  name: string;
  generic_name?: string;
  manufacturer: string;
  category: string;
  dosage_form: string;
  strength: string;
  unit_price: number;
  stock_quantity: number;
  reorder_level: number;
  expiry_date: string;
  batch_number: string;
  description?: string;
}

interface SaleItem {
  medicine: Medicine;
  quantity: number;
  unit_price: number;
  total_price: number;
}

interface Sale {
  id: string;
  customer_name?: string;
  customer_phone?: string;
  patient_id?: string;
  items: SaleItem[];
  total_amount: number;
  payment_method: 'cash' | 'card' | 'insurance';
  pharmacist_name: string;
  sale_date: string;
  notes?: string;
}

const PharmacyDepartment = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('pos');
  
  // POS State
  const [cart, setCart] = useState<SaleItem[]>([]);
  const [customerName, setCustomerName] = useState('');
  const [customerPhone, setCustomerPhone] = useState('');
  const [paymentMethod, setPaymentMethod] = useState<'cash' | 'card' | 'insurance'>('cash');
  const [pharmacistName, setPharmacistName] = useState('');
  const [saleNotes, setSaleNotes] = useState('');
  
  // Inventory State
  const [selectedMedicine, setSelectedMedicine] = useState<Medicine | null>(null);
  const [isInventoryDialogOpen, setIsInventoryDialogOpen] = useState(false);
  const [inventoryAction, setInventoryAction] = useState<'add' | 'edit' | 'view'>('view');

  // Mock medicine inventory data
  const [medicines, setMedicines] = useState<Medicine[]>([
    {
      id: '1',
      name: 'Paracetamol',
      generic_name: 'Acetaminophen',
      manufacturer: 'PharmaCorp',
      category: 'Analgesic',
      dosage_form: 'Tablet',
      strength: '500mg',
      unit_price: 2.50,
      stock_quantity: 500,
      reorder_level: 50,
      expiry_date: '2025-12-31',
      batch_number: 'PAR001',
      description: 'Pain relief and fever reducer'
    },
    {
      id: '2',
      name: 'Amoxicillin',
      generic_name: 'Amoxicillin',
      manufacturer: 'MediLab',
      category: 'Antibiotic',
      dosage_form: 'Capsule',
      strength: '250mg',
      unit_price: 5.00,
      stock_quantity: 200,
      reorder_level: 30,
      expiry_date: '2025-08-15',
      batch_number: 'AMX002',
      description: 'Broad-spectrum antibiotic'
    },
    {
      id: '3',
      name: 'Ibuprofen',
      generic_name: 'Ibuprofen',
      manufacturer: 'HealthPlus',
      category: 'NSAID',
      dosage_form: 'Tablet',
      strength: '400mg',
      unit_price: 3.00,
      stock_quantity: 150,
      reorder_level: 25,
      expiry_date: '2025-10-20',
      batch_number: 'IBU003',
      description: 'Anti-inflammatory pain reliever'
    },
    {
      id: '4',
      name: 'Cough Syrup',
      generic_name: 'Dextromethorphan',
      manufacturer: 'CoughCare',
      category: 'Antitussive',
      dosage_form: 'Syrup',
      strength: '100ml',
      unit_price: 8.50,
      stock_quantity: 75,
      reorder_level: 15,
      expiry_date: '2025-06-30',
      batch_number: 'CS004',
      description: 'Cough suppressant syrup'
    },
    {
      id: '5',
      name: 'Vitamin C',
      generic_name: 'Ascorbic Acid',
      manufacturer: 'VitaHealth',
      category: 'Vitamin',
      dosage_form: 'Tablet',
      strength: '1000mg',
      unit_price: 1.50,
      stock_quantity: 300,
      reorder_level: 40,
      expiry_date: '2026-03-15',
      batch_number: 'VTC005',
      description: 'Vitamin C supplement'
    }
  ]);

  const [sales, setSales] = useState<Sale[]>([]);

  // Filter medicines based on search
  const filteredMedicines = medicines.filter(medicine =>
    medicine.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    medicine.generic_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    medicine.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
    medicine.manufacturer.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Low stock medicines
  const lowStockMedicines = medicines.filter(medicine => 
    medicine.stock_quantity <= medicine.reorder_level
  );

  // POS Functions
  const addToCart = (medicine: Medicine, quantity: number = 1) => {
    if (medicine.stock_quantity < quantity) {
      toast({
        title: "Insufficient Stock",
        description: `Only ${medicine.stock_quantity} units available`,
        variant: "destructive"
      });
      return;
    }

    const existingItem = cart.find(item => item.medicine.id === medicine.id);
    if (existingItem) {
      const newQuantity = existingItem.quantity + quantity;
      if (newQuantity > medicine.stock_quantity) {
        toast({
          title: "Insufficient Stock",
          description: `Only ${medicine.stock_quantity} units available`,
          variant: "destructive"
        });
        return;
      }
      setCart(cart.map(item =>
        item.medicine.id === medicine.id
          ? { ...item, quantity: newQuantity, total_price: newQuantity * medicine.unit_price }
          : item
      ));
    } else {
      setCart([...cart, {
        medicine,
        quantity,
        unit_price: medicine.unit_price,
        total_price: quantity * medicine.unit_price
      }]);
    }
  };

  const removeFromCart = (medicineId: string) => {
    setCart(cart.filter(item => item.medicine.id !== medicineId));
  };

  const updateCartQuantity = (medicineId: string, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(medicineId);
      return;
    }

    const medicine = medicines.find(m => m.id === medicineId);
    if (!medicine || quantity > medicine.stock_quantity) {
      toast({
        title: "Insufficient Stock",
        description: `Only ${medicine?.stock_quantity || 0} units available`,
        variant: "destructive"
      });
      return;
    }

    setCart(cart.map(item =>
      item.medicine.id === medicineId
        ? { ...item, quantity, total_price: quantity * item.unit_price }
        : item
    ));
  };

  const getCartTotal = () => {
    return cart.reduce((total, item) => total + item.total_price, 0);
  };

  const processSale = async () => {
    if (cart.length === 0) {
      toast({
        title: "Empty Cart",
        description: "Please add items to cart before processing sale",
        variant: "destructive"
      });
      return;
    }

    if (!pharmacistName.trim()) {
      toast({
        title: "Missing Information",
        description: "Please enter pharmacist name",
        variant: "destructive"
      });
      return;
    }

    try {
      // Create sale record
      const newSale: Sale = {
        id: Date.now().toString(),
        customer_name: customerName || undefined,
        customer_phone: customerPhone || undefined,
        items: cart,
        total_amount: getCartTotal(),
        payment_method: paymentMethod,
        pharmacist_name: pharmacistName,
        sale_date: new Date().toISOString(),
        notes: saleNotes || undefined
      };

      // Update medicine stock
      const updatedMedicines = medicines.map(medicine => {
        const cartItem = cart.find(item => item.medicine.id === medicine.id);
        if (cartItem) {
          return {
            ...medicine,
            stock_quantity: medicine.stock_quantity - cartItem.quantity
          };
        }
        return medicine;
      });

      setMedicines(updatedMedicines);
      setSales([...sales, newSale]);

      // Clear cart and form
      setCart([]);
      setCustomerName('');
      setCustomerPhone('');
      setPaymentMethod('cash');
      setSaleNotes('');

      toast({
        title: "Sale Completed",
        description: `Sale of $${getCartTotal().toFixed(2)} processed successfully`,
      });

    } catch (error) {
      console.error('Error processing sale:', error);
      toast({
        title: "Error",
        description: "Failed to process sale. Please try again.",
        variant: "destructive"
      });
    }
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Pharmacy Department</h1>
            <p className="text-gray-600 mt-1">Inventory Management & Point of Sale System</p>
          </div>
          <div className="flex space-x-3">
            <Button 
              variant="outline"
              onClick={() => navigate('/laboratory-department')}
            >
              Back to Laboratory
            </Button>
            <Button
              variant="outline"
              onClick={() => navigate('/medical-records')}
            >
              View Medical Records
            </Button>
          </div>
        </div>

        {/* Search Bar */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search medicines..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 w-80"
          />
        </div>

        {/* Main Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="pos">Point of Sale (POS)</TabsTrigger>
            <TabsTrigger value="inventory">Inventory Management</TabsTrigger>
            <TabsTrigger value="sales">Sales Reports</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>
    </Layout>
  );
};

export default PharmacyDepartment;
