
import React from 'react';
import { Layout } from '../components/Layout';
import { MetricCard } from '../components/MetricCard';
import { ActivityItem } from '../components/ActivityItem';
import { QuickActionCard } from '../components/QuickActionCard';
import { useSupabaseData } from '@/hooks/useSupabaseData';
import { 
  Users, 
  Calendar, 
  UserPlus, 
  FileText, 
  DollarSign,
  Stethoscope,
  Building2,
  FlaskConical
} from 'lucide-react';

const Index = () => {
  const { useDashboardStats, usePatients, useAppointments, useDoctors, useDepartments } = useSupabaseData();
  
  const { data: stats, isLoading: statsLoading } = useDashboardStats();
  const { data: patients = [] } = usePatients();
  const { data: appointments = [] } = useAppointments();
  const { data: doctors = [] } = useDoctors();
  const { data: departments = [] } = useDepartments();

  // Calculate recent activity
  const recentActivity = [
    ...patients.slice(0, 3).map(patient => ({
      type: 'patient_added',
      title: 'Patient Added',
      description: `New patient registered: ${patient.patient_name}`,
      time: new Date(patient.created_at).toLocaleTimeString(),
      icon: UserPlus,
      color: 'blue' as const
    })),
    ...appointments.slice(0, 2).map(appointment => ({
      type: 'appointment_scheduled',
      title: 'Appointment Scheduled',
      description: `Appointment scheduled for ${appointment.patient_name}`,
      time: new Date(appointment.created_at).toLocaleTimeString(),
      icon: Calendar,
      color: 'green' as const
    }))
  ].sort((a, b) => new Date(b.time).getTime() - new Date(a.time).getTime()).slice(0, 5);

  const quickActions = [
    {
      title: 'Add Patient',
      description: 'Register a new patient in the system',
      icon: UserPlus,
      color: 'blue' as const,
      href: '/reception'
    },
    {
      title: 'Schedule Appointment',
      description: 'Book a new appointment',
      icon: Calendar,
      color: 'green' as const,
      href: '/appointments'
    },
    {
      title: 'Add Medical Record',
      description: 'Create a new medical record',
      icon: FileText,
      color: 'purple' as const,
      href: '/medical-records'
    },
    {
      title: 'Generate Bill',
      description: 'Create a new invoice',
      icon: DollarSign,
      color: 'orange' as const,
      href: '/billing-accounting'
    }
  ];

  if (statsLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg text-gray-600">Loading dashboard...</div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-8">
        {/* Welcome Section */}
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-2">Welcome back! Here's what's happening at your hospital today.</p>
        </div>

        {/* Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <MetricCard
            title="Total Patients"
            value={stats?.total_patients || patients.length}
            icon={Users}
            color="blue"
          />
          <MetricCard
            title="Today's Appointments"
            value={stats?.today_appointments || 0}
            icon={Calendar}
            color="green"
          />
          <MetricCard
            title="Total Doctors"
            value={stats?.total_doctors || doctors.length}
            icon={Stethoscope}
            color="purple"
          />
          <MetricCard
            title="Departments"
            value={stats?.total_departments || departments.length}
            icon={Building2}
            color="cyan"
          />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Quick Actions */}
          <div className="lg:col-span-2">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Quick Actions</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {quickActions.map((action, index) => (
                <QuickActionCard key={index} {...action} />
              ))}
            </div>
          </div>

          {/* Recent Activity */}
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Recent Activity</h2>
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 divide-y divide-gray-200">
              {recentActivity.length > 0 ? (
                recentActivity.map((activity, index) => (
                  <ActivityItem key={index} {...activity} />
                ))
              ) : (
                <div className="p-6 text-center text-gray-500">
                  <p>No recent activity</p>
                  <p className="text-sm mt-1">Start by adding patients, doctors, or appointments</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Additional Stats */}
        {(patients.length > 0 || doctors.length > 0) && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Hospital Overview</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mx-auto mb-3">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="text-lg font-medium text-gray-900">Patient Management</h3>
                <p className="text-gray-600 text-sm mt-1">
                  {patients.length} registered patients with comprehensive medical records
                </p>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg mx-auto mb-3">
                  <Stethoscope className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="text-lg font-medium text-gray-900">Medical Staff</h3>
                <p className="text-gray-600 text-sm mt-1">
                  {doctors.length} qualified doctors across {departments.length} departments
                </p>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mx-auto mb-3">
                  <Calendar className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="text-lg font-medium text-gray-900">Appointments</h3>
                <p className="text-gray-600 text-sm mt-1">
                  {appointments.length} total appointments scheduled
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default Index;
