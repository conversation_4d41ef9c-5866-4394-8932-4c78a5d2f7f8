import React, { useState } from 'react';
import { Layout } from '../components/Layout';
import { Search, Plus, Filter, MoreVertical, User, Calendar, Phone, Mail, Stethoscope } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { PatientForm } from '@/components/PatientForm';
import { PatientDetailsDialog } from '@/components/PatientDetailsDialog';
import { useSupabaseData } from '@/hooks/useSupabaseData';
import { useWorkflowData } from '@/hooks/useWorkflowData';
import { WorkflowIntegration } from '@/components/WorkflowIntegration';

const Reception = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [selectedPatient, setSelectedPatient] = useState(null);
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const [detailsPatient, setDetailsPatient] = useState(null);

  const { useReceptionPatients } = useSupabaseData();
  const { useConsultationRecords, useLaboratoryRecords } = useWorkflowData();
  const { data: patients = [], isLoading, error } = useReceptionPatients();
  const { data: consultationRecords = [] } = useConsultationRecords();
  const { data: laboratoryRecords = [] } = useLaboratoryRecords();

  // Filter patients based on search term
  const filteredPatients = patients.filter(patient =>
    patient.patient_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.phone_number?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.id?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleEditPatient = (patient: any) => {
    setSelectedPatient(patient);
    setIsFormOpen(true);
  };

  const handleViewRecords = (patient: any) => {
    setDetailsPatient(patient);
    setIsDetailsOpen(true);
  };

  const handleCloseForm = () => {
    setIsFormOpen(false);
    setSelectedPatient(null);
  };

  const calculateAge = (dateOfBirth: string) => {
    if (!dateOfBirth) return 'N/A';
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg text-gray-600">Loading patients...</div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg text-red-600">Error loading patients: {error.message}</div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Reception Department</h1>
            <p className="text-gray-600 mt-1 text-sm sm:text-base">Patient registration and initial intake</p>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
            <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
              <DialogTrigger asChild>
                <Button className="bg-blue-600 hover:bg-blue-700 text-white w-full sm:w-auto">
                  <Plus className="mr-2 h-4 w-4" />
                  New Patient
                </Button>
              </DialogTrigger>
              <DialogContent className="w-[95vw] max-w-[600px] max-h-[90vh] overflow-y-auto mx-auto">
                <PatientForm
                  onClose={handleCloseForm}
                  useWorkflowIntegration={true}
                  autoRedirectToConsultation={true}
                  existingPatient={selectedPatient}
                  useWorkflowIntegration={true}
                  autoRedirectToConsultation={false}
                />
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Search and Filter */}
        <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
            <Input
              type="text"
              placeholder="Search patients..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 text-sm sm:text-base border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <div className="flex gap-2">
            <Button variant="outline" className="flex items-center gap-2 px-3 py-2 text-sm">
              <Filter size={16} />
              <span className="hidden sm:inline">Filter</span>
            </Button>
          </div>
        </div>

        {/* No patients message */}
        {filteredPatients.length === 0 && !isLoading && (
          <Card className="p-8 text-center">
            <h3 className="text-lg font-medium text-gray-900 mb-2">No patients found</h3>
            <p className="text-gray-500 mb-4">
              {searchTerm ? 'Try adjusting your search terms.' : 'Get started by adding your first patient.'}
            </p>
            <Button onClick={() => setIsFormOpen(true)} className="bg-blue-600 hover:bg-blue-700">
              <Plus className="mr-2 h-4 w-4" />
              Add Patient
            </Button>
          </Card>
        )}

        {/* Patients Grid */}
        {filteredPatients.length > 0 && (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
            {filteredPatients.map((patient) => (
              <Card key={patient.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                <div className="p-4 sm:p-6">
                  <div className="flex items-start space-x-3 sm:space-x-4 mb-4">
                    <Avatar className="h-10 w-10 sm:h-12 sm:w-12 flex-shrink-0">
                      <AvatarImage src="" alt={patient.patient_name} />
                      <AvatarFallback className="text-xs sm:text-sm">
                        {patient.patient_name?.split(' ').map((n: string) => n[0]).join('') || 'P'}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <h3 className="font-bold text-sm sm:text-lg text-gray-900 truncate">{patient.patient_name}</h3>
                      <p className="text-xs sm:text-sm text-gray-500 truncate">ID: {patient.id.slice(0, 8)}...</p>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-8 w-8 sm:h-10 sm:w-10 flex-shrink-0">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-48">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem onClick={() => handleEditPatient(patient)}>
                          Edit Patient
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleViewRecords(patient)}>
                          View Records
                        </DropdownMenuItem>
                        <DropdownMenuItem>Schedule Appointment</DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className="text-red-600">
                          Delete Patient
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>

                  <div className="space-y-2 sm:space-y-3">
                    <div className="flex items-center text-xs sm:text-sm text-gray-600">
                      <Calendar className="h-3 w-3 sm:h-4 sm:w-4 mr-2 flex-shrink-0" />
                      <span>Age: {calculateAge(patient.date_of_birth)} years</span>
                    </div>
                    <div className="flex items-center text-xs sm:text-sm text-gray-600">
                      <Mail className="h-3 w-3 sm:h-4 sm:w-4 mr-2 flex-shrink-0" />
                      <span className="truncate">{patient.email}</span>
                    </div>
                    <div className="flex items-center text-xs sm:text-sm text-gray-600">
                      <Phone className="h-3 w-3 sm:h-4 sm:w-4 mr-2 flex-shrink-0" />
                      <span className="truncate">{patient.phone_number}</span>
                    </div>
                    {patient.blood_type && (
                      <div className="flex items-center justify-between">
                        <span className="text-xs sm:text-sm text-gray-600">Blood Type:</span>
                        <Badge variant="outline" className="text-xs">{patient.blood_type}</Badge>
                      </div>
                    )}
                    {patient.insurance && (
                      <div className="flex items-center justify-between">
                        <span className="text-xs sm:text-sm text-gray-600">Insurance:</span>
                        <Badge className="bg-green-100 text-green-800 text-xs">Active</Badge>
                      </div>
                    )}

                    {/* Department Information */}
                    {patient.department_name && (
                      <div className="flex items-center justify-between">
                        <div className="flex items-center text-xs sm:text-sm text-gray-600">
                          <Stethoscope className="h-3 w-3 mr-1 flex-shrink-0" />
                          <span>Department:</span>
                        </div>
                        <Badge
                          className="text-xs text-white"
                          style={{ backgroundColor: patient.department_color || '#3B82F6' }}
                        >
                          {patient.department_name}
                        </Badge>
                      </div>
                    )}

                    {/* Workflow Status */}
                    <div className="flex items-center justify-between">
                      <span className="text-xs sm:text-sm text-gray-600">Status:</span>
                      <Badge
                        className={`text-xs ${
                          patient.workflow_status === 'registered'
                            ? 'bg-blue-100 text-blue-800'
                            : patient.workflow_status === 'sent_to_consultation'
                            ? 'bg-orange-100 text-orange-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}
                      >
                        {patient.workflow_status === 'registered' && 'Ready for Consultation'}
                        {patient.workflow_status === 'sent_to_consultation' && 'Sent to Consultation'}
                        {!patient.workflow_status && 'Registered'}
                      </Badge>
                    </div>

                    {/* Show time since sent to consultation if applicable */}
                    {patient.workflow_status === 'sent_to_consultation' && patient.hours_since_sent && (
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-500">Sent:</span>
                        <span className="text-xs text-gray-500">
                          {Math.floor(patient.hours_since_sent)}h ago
                        </span>
                      </div>
                    )}
                  </div>

                  <div className="mt-4 sm:mt-6 pt-3 sm:pt-4 border-t border-gray-100">
                    <WorkflowIntegration patient={patient} />
                  </div>

                  <div className="mt-3 sm:mt-4 pt-3 sm:pt-4 border-t border-gray-100">
                    <div className="flex justify-center sm:justify-between text-xs text-gray-500">
                      <span className="truncate">
                        Registered: {new Date(patient.registration_date).toLocaleDateString()}
                        {patient.registration_time && (
                          <span className="ml-2 text-blue-600 font-medium">
                            at {patient.registration_time}
                          </span>
                        )}
                      </span>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        )}

        {/* Pagination */}
        {filteredPatients.length > 0 && (
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
            <p className="text-xs sm:text-sm text-gray-600 text-center sm:text-left">
              Showing {filteredPatients.length} of {patients.length} patients
            </p>
            <div className="flex justify-center sm:justify-end space-x-2">
              <Button variant="outline" size="sm" className="text-xs sm:text-sm">Previous</Button>
              <Button variant="outline" size="sm" className="text-xs sm:text-sm">Next</Button>
            </div>
          </div>
        )}
      </div>

      {/* Patient Details Dialog */}
      <PatientDetailsDialog
        patient={detailsPatient}
        consultationRecords={consultationRecords}
        laboratoryRecords={laboratoryRecords}
        isOpen={isDetailsOpen}
        onClose={() => setIsDetailsOpen(false)}
      />
    </Layout>
  );
};

export default Reception;
