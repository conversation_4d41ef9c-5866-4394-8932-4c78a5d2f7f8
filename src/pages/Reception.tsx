import React, { useState } from 'react';
import { Layout } from '../components/Layout';
import {
  Search,
  Plus,
  Filter,
  MoreVertical,
  User,
  Calendar,
  Phone,
  Mail,
  Stethoscope,
  Clock,
  MapPin,
  Heart,
  Shield,
  Activity,
  Eye,
  Edit,
  UserPlus,
  Trash2
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { PatientForm } from '@/components/PatientForm';
import { PatientDetailsDialog } from '@/components/PatientDetailsDialog';
import { useSupabaseData } from '@/hooks/useSupabaseData';
import { useWorkflowData } from '@/hooks/useWorkflowData';
import { WorkflowIntegration } from '@/components/WorkflowIntegration';

const Reception = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [selectedPatient, setSelectedPatient] = useState(null);
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const [detailsPatient, setDetailsPatient] = useState(null);

  const { useReceptionPatients } = useSupabaseData();
  const { useConsultationRecords, useLaboratoryRecords } = useWorkflowData();
  const { data: patients = [], isLoading, error } = useReceptionPatients();
  const { data: consultationRecords = [] } = useConsultationRecords();
  const { data: laboratoryRecords = [] } = useLaboratoryRecords();

  // Filter patients based on search term
  const filteredPatients = patients.filter(patient =>
    patient.patient_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.phone_number?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.id?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleEditPatient = (patient: any) => {
    setSelectedPatient(patient);
    setIsFormOpen(true);
  };

  const handleViewRecords = (patient: any) => {
    setDetailsPatient(patient);
    setIsDetailsOpen(true);
  };

  const handleCloseForm = () => {
    setIsFormOpen(false);
    setSelectedPatient(null);
  };

  const calculateAge = (dateOfBirth: string) => {
    if (!dateOfBirth) return 'N/A';
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg text-gray-600">Loading patients...</div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg text-red-600">Error loading patients: {error.message}</div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-8">
        {/* Enhanced Header */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex items-center space-x-4">
              <div className="bg-blue-600 p-3 rounded-lg">
                <UserPlus className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Reception Department</h1>
                <p className="text-gray-600 mt-1 text-sm sm:text-base flex items-center">
                  <Activity className="h-4 w-4 mr-2" />
                  Patient registration and initial intake
                </p>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
              <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
                <DialogTrigger asChild>
                  <Button className="bg-blue-600 hover:bg-blue-700 text-white w-full sm:w-auto shadow-lg hover:shadow-xl transition-all duration-200">
                    <Plus className="mr-2 h-4 w-4" />
                    New Patient
                  </Button>
                </DialogTrigger>
                <DialogContent className="w-[95vw] max-w-[600px] max-h-[90vh] overflow-y-auto mx-auto">
                  <PatientForm
                    onClose={handleCloseForm}
                    useWorkflowIntegration={true}
                    autoRedirectToConsultation={true}
                    existingPatient={selectedPatient}
                    useWorkflowIntegration={true}
                    autoRedirectToConsultation={false}
                  />
                </DialogContent>
              </Dialog>
            </div>
          </div>

          {/* Stats Row */}
          <div className="mt-6 grid grid-cols-1 sm:grid-cols-3 gap-4">
            <div className="bg-white rounded-lg p-4 border border-blue-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Patients</p>
                  <p className="text-2xl font-bold text-blue-600">{patients.length}</p>
                </div>
                <User className="h-8 w-8 text-blue-500" />
              </div>
            </div>
            <div className="bg-white rounded-lg p-4 border border-green-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Ready for Consultation</p>
                  <p className="text-2xl font-bold text-green-600">
                    {patients.filter(p => p.workflow_status === 'registered').length}
                  </p>
                </div>
                <Stethoscope className="h-8 w-8 text-green-500" />
              </div>
            </div>
            <div className="bg-white rounded-lg p-4 border border-orange-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">In Consultation</p>
                  <p className="text-2xl font-bold text-orange-600">
                    {patients.filter(p => p.workflow_status === 'sent_to_consultation').length}
                  </p>
                </div>
                <Clock className="h-8 w-8 text-orange-500" />
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Search and Filter */}
        <Card className="p-6 bg-white shadow-sm border border-gray-200">
          <div className="flex flex-col sm:flex-row gap-4 sm:gap-6">
            <div className="flex-1 relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
              <Input
                type="text"
                placeholder="Search by name, email, phone, or ID..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-12 pr-4 py-3 text-sm sm:text-base border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
              />
            </div>
            <div className="flex gap-3">
              <Button variant="outline" className="flex items-center gap-2 px-4 py-3 text-sm border-2 border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-all duration-200">
                <Filter size={16} />
                <span className="hidden sm:inline">Filter</span>
              </Button>
              <div className="flex items-center text-sm text-gray-600 bg-gray-50 px-4 py-3 rounded-xl">
                <span className="font-medium">{filteredPatients.length}</span>
                <span className="ml-1">patients</span>
              </div>
            </div>
          </div>
        </Card>

        {/* No patients message */}
        {filteredPatients.length === 0 && !isLoading && (
          <Card className="p-12 text-center bg-gradient-to-br from-gray-50 to-gray-100 border-2 border-dashed border-gray-300">
            <div className="max-w-md mx-auto">
              <div className="bg-gray-200 rounded-full p-4 w-16 h-16 mx-auto mb-4">
                <User className="h-8 w-8 text-gray-400 mx-auto" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No patients found</h3>
              <p className="text-gray-500 mb-6">
                {searchTerm ? 'Try adjusting your search terms or clear the search to see all patients.' : 'Get started by registering your first patient to begin managing their healthcare journey.'}
              </p>
              <Button onClick={() => setIsFormOpen(true)} className="bg-blue-600 hover:bg-blue-700 shadow-lg hover:shadow-xl transition-all duration-200">
                <Plus className="mr-2 h-4 w-4" />
                Register New Patient
              </Button>
            </div>
          </Card>
        )}

        {/* Enhanced Patients Grid - Exactly 3 Columns */}
        {filteredPatients.length > 0 && (
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredPatients.map((patient) => (
              <Card key={patient.id} className="group overflow-hidden hover:shadow-xl transition-all duration-300 border-2 border-gray-100 hover:border-blue-200 bg-white">
                <div className="p-6">
                  {/* Header with Avatar and Actions */}
                  <div className="flex items-start space-x-4 mb-6">
                    <div className="relative">
                      <Avatar className="h-14 w-14 ring-2 ring-blue-100 group-hover:ring-blue-200 transition-all duration-200">
                        <AvatarImage src="" alt={patient.patient_name} />
                        <AvatarFallback className="text-lg font-semibold bg-gradient-to-br from-blue-500 to-indigo-600 text-white">
                          {patient.patient_name?.split(' ').map((n: string) => n[0]).join('') || 'P'}
                        </AvatarFallback>
                      </Avatar>
                      {/* Status Indicator */}
                      <div className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white ${
                        patient.workflow_status === 'registered' ? 'bg-green-500' :
                        patient.workflow_status === 'sent_to_consultation' ? 'bg-orange-500' : 'bg-gray-400'
                      }`} />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="font-bold text-lg text-gray-900 truncate group-hover:text-blue-600 transition-colors duration-200">
                        {patient.patient_name}
                      </h3>
                      <p className="text-sm text-gray-500 truncate font-mono">
                        ID: {patient.id.slice(0, 8)}...
                      </p>
                      <div className="flex items-center mt-1">
                        <Calendar className="h-3 w-3 text-gray-400 mr-1" />
                        <span className="text-xs text-gray-500">
                          Age: {calculateAge(patient.date_of_birth)} years
                        </span>
                      </div>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-10 w-10 opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-blue-50">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-52">
                        <DropdownMenuLabel className="font-semibold">Patient Actions</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => handleViewRecords(patient)} className="flex items-center">
                          <Eye className="h-4 w-4 mr-2" />
                          View Records
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEditPatient(patient)} className="flex items-center">
                          <Edit className="h-4 w-4 mr-2" />
                          Edit Patient
                        </DropdownMenuItem>
                        <DropdownMenuItem className="flex items-center">
                          <Calendar className="h-4 w-4 mr-2" />
                          Schedule Appointment
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className="text-red-600 flex items-center">
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete Patient
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>

                  {/* Contact Information */}
                  <div className="space-y-4 mb-6">
                    <div className="grid grid-cols-1 gap-3">
                      <div className="flex items-center text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
                        <Mail className="h-4 w-4 mr-3 text-blue-500 flex-shrink-0" />
                        <span className="truncate font-medium">{patient.email || 'No email provided'}</span>
                      </div>
                      <div className="flex items-center text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
                        <Phone className="h-4 w-4 mr-3 text-green-500 flex-shrink-0" />
                        <span className="truncate font-medium">{patient.phone_number || 'No phone provided'}</span>
                      </div>
                    </div>
                  </div>

                  {/* Medical Information */}
                  <div className="space-y-3 mb-6">
                    <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-100">
                      <div className="flex items-center text-sm text-gray-700">
                        <Heart className="h-4 w-4 mr-2 text-red-500" />
                        <span className="font-medium">Blood Type:</span>
                      </div>
                      <Badge variant="outline" className="text-sm font-semibold text-red-700 border-red-200">
                        {patient.blood_type || 'Unknown'}
                      </Badge>
                    </div>

                    {patient.insurance && (
                      <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-100">
                        <div className="flex items-center text-sm text-gray-700">
                          <Shield className="h-4 w-4 mr-2 text-green-500" />
                          <span className="font-medium">Insurance:</span>
                        </div>
                        <Badge className="bg-green-100 text-green-800 text-sm font-semibold">Active</Badge>
                      </div>
                    )}

                    {/* Department Information */}
                    {patient.department_name && (
                      <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-100">
                        <div className="flex items-center text-sm text-gray-700">
                          <Stethoscope className="h-4 w-4 mr-2 text-blue-500" />
                          <span className="font-medium">Department:</span>
                        </div>
                        <Badge
                          className="text-sm font-semibold text-white"
                          style={{ backgroundColor: patient.department_color || '#3B82F6' }}
                        >
                          {patient.department_name}
                        </Badge>
                      </div>
                    )}
                  </div>

                  {/* Workflow Status */}
                  <div className="mb-6">
                    <div className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg border border-gray-200">
                      <div className="flex items-center">
                        <Activity className="h-5 w-5 mr-3 text-gray-600" />
                        <span className="text-sm font-medium text-gray-700">Current Status:</span>
                      </div>
                      <Badge
                        className={`text-sm font-semibold px-3 py-1 ${
                          patient.workflow_status === 'registered'
                            ? 'bg-green-100 text-green-800 border border-green-200'
                            : patient.workflow_status === 'sent_to_consultation'
                            ? 'bg-orange-100 text-orange-800 border border-orange-200'
                            : 'bg-gray-100 text-gray-800 border border-gray-200'
                        }`}
                      >
                        {patient.workflow_status === 'registered' && '✓ Ready for Consultation'}
                        {patient.workflow_status === 'sent_to_consultation' && '⏳ In Consultation'}
                        {!patient.workflow_status && '📝 Registered'}
                      </Badge>
                    </div>

                    {/* Show time since sent to consultation if applicable */}
                    {patient.workflow_status === 'sent_to_consultation' && patient.hours_since_sent && (
                      <div className="mt-2 flex items-center justify-center text-xs text-orange-600 bg-orange-50 p-2 rounded-lg">
                        <Clock className="h-3 w-3 mr-1" />
                        <span className="font-medium">
                          Sent {Math.floor(patient.hours_since_sent)} hours ago
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Fee Information */}
                  {(patient.fee_amount > 0 || patient.fee_paid) && (
                    <div className="mb-6 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center text-sm text-gray-700">
                          <span className="font-medium">Registration Fee:</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-semibold text-gray-900">
                            ${patient.fee_amount?.toFixed(2) || '0.00'}
                          </span>
                          <Badge className={`text-xs ${
                            patient.fee_paid
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {patient.fee_paid ? 'PAID' : 'PENDING'}
                          </Badge>
                        </div>
                      </div>
                      {patient.fee_notes && (
                        <p className="text-xs text-gray-600 mt-2 italic">{patient.fee_notes}</p>
                      )}
                    </div>
                  )}

                  {/* Workflow Integration */}
                  <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100">
                    <WorkflowIntegration patient={patient} />
                  </div>

                  {/* Registration Information Footer */}
                  <div className="pt-4 border-t border-gray-200">
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <div className="flex items-center">
                        <Calendar className="h-3 w-3 mr-1" />
                        <span className="font-medium">
                          Registered: {new Date(patient.registration_date).toLocaleDateString()}
                        </span>
                      </div>
                      {patient.registration_time && (
                        <div className="flex items-center text-blue-600">
                          <Clock className="h-3 w-3 mr-1" />
                          <span className="font-medium">
                            {patient.registration_time}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        )}

        {/* Enhanced Pagination */}
        {filteredPatients.length > 0 && (
          <Card className="p-4 bg-white border border-gray-200">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <div className="flex items-center space-x-2">
                  <User className="h-4 w-4" />
                  <span className="font-medium">
                    Showing {filteredPatients.length} of {patients.length} patients
                  </span>
                </div>
                {searchTerm && (
                  <div className="flex items-center space-x-2 text-blue-600">
                    <Search className="h-4 w-4" />
                    <span className="font-medium">Filtered results</span>
                  </div>
                )}
              </div>
              <div className="flex justify-center sm:justify-end space-x-2">
                <Button variant="outline" size="sm" className="text-sm border-2 border-gray-200 hover:border-blue-300 hover:bg-blue-50">
                  Previous
                </Button>
                <Button variant="outline" size="sm" className="text-sm border-2 border-gray-200 hover:border-blue-300 hover:bg-blue-50">
                  Next
                </Button>
              </div>
            </div>
          </Card>
        )}
      </div>

      {/* Patient Details Dialog */}
      <PatientDetailsDialog
        patient={detailsPatient}
        consultationRecords={consultationRecords}
        laboratoryRecords={laboratoryRecords}
        isOpen={isDetailsOpen}
        onClose={() => setIsDetailsOpen(false)}
      />
    </Layout>
  );
};

export default Reception;
