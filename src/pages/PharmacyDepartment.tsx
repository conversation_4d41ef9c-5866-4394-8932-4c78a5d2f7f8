import { useState, useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { Layout } from '../components/Layout';
import { useCurrency } from '../contexts/CurrencyContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Pill,
  Search,
  Plus,
  Minus,
  Clock,
  Phone,
  Mail,
  User,
  Users,
  Package,
  CheckCircle,
  AlertTriangle,
  DollarSign,
  ShoppingCart,
  Eye,
  Edit,
  Trash2,
  Calculator,
  Receipt
} from 'lucide-react';
import { useWorkflowData } from '@/hooks/useWorkflowData';
import { useSupabaseData } from '@/hooks/useSupabaseData';
import { PharmacyForm } from '@/components/workflow/PharmacyForm';
import { PharmacyInventoryForm } from '@/components/inventory/PharmacyInventoryForm';
import ErrorBoundary from '@/components/ErrorBoundary';
import { useNavigate } from 'react-router-dom';
import { toast } from '@/hooks/use-toast';

interface InventoryItem {
  id: string;
  name: string;
  generic_name?: string;
  manufacturer?: string;
  category: string;
  type: 'medicine' | 'medical_supply' | 'equipment' | 'consumable' | 'other';
  dosage_form?: string; // tablet, capsule, syrup, injection, etc. (for medicines)
  strength?: string; // 500mg, 10ml, etc. (for medicines)
  unit: string; // pieces, boxes, bottles, kg, etc.
  unit_price: number;
  stock_quantity: number;
  reorder_level: number;
  expiry_date?: string;
  batch_number?: string;
  supplier?: string;
  location?: string; // storage location
  description?: string;
  created_at: string;
  updated_at: string;
}

interface SaleItem {
  item: InventoryItem;
  quantity: number;
  unit_price: number;
  total_price: number;
}

interface Sale {
  id: string;
  customer_name?: string;
  customer_phone?: string;
  patient_id?: string; // For workflow sales
  items: SaleItem[];
  total_amount: number;
  payment_method: 'cash' | 'card' | 'insurance';
  pharmacist_name: string;
  sale_date: string;
  notes?: string;
}

const PharmacyDepartment = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPatient, setSelectedPatient] = useState<any>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('inventory'); // 'inventory', 'pos'

  // POS State
  const [cart, setCart] = useState<SaleItem[]>([]);
  const [customerName, setCustomerName] = useState('');
  const [customerPhone, setCustomerPhone] = useState('');
  const [paymentMethod, setPaymentMethod] = useState<'cash' | 'card' | 'insurance'>('cash');
  const [pharmacistName, setPharmacistName] = useState('');
  const [saleNotes, setSaleNotes] = useState('');

  // Inventory State
  const [selectedItem, setSelectedItem] = useState<InventoryItem | null>(null);
  const [isInventoryDialogOpen, setIsInventoryDialogOpen] = useState(false);
  const [inventoryAction, setInventoryAction] = useState<'add' | 'edit' | 'view'>('view');
  const [inventoryFilter, setInventoryFilter] = useState<'all' | 'medicine' | 'medical_supply' | 'equipment' | 'consumable' | 'other'>('all');

  const {
    useWorkflowStats,
    usePharmacyRecords,
    useLaboratoryRecords,
    useConsultationRecords
  } = useWorkflowData();

  const {
    usePatients,
    usePharmacyInventory,
    updatePharmacyInventory,
    createRevenueEntry
  } = useSupabaseData();

  const { formatCurrency } = useCurrency();

  const queryClient = useQueryClient();

  const { data: allPatients = [], isLoading: patientsLoading } = usePatients();
  const { data: inventoryItems = [], isLoading: inventoryLoading } = usePharmacyInventory();
  const { data: stats } = useWorkflowStats();
  const { data: pharmacyRecords = [] } = usePharmacyRecords();
  const { data: laboratoryRecords = [] } = useLaboratoryRecords();
  const { data: consultationRecords = [] } = useConsultationRecords();

  // Helper functions to extract information from medication name
  const detectProductType = (name: string): 'medicine' | 'medical_supply' | 'equipment' | 'consumable' | 'other' => {
    const lowerName = name.toLowerCase();
    if (lowerName.includes('gloves') || lowerName.includes('syringe') || lowerName.includes('mask') || lowerName.includes('ppe')) {
      return 'medical_supply';
    }
    if (lowerName.includes('thermometer') || lowerName.includes('monitor') || lowerName.includes('equipment')) {
      return 'equipment';
    }
    if (lowerName.includes('swab') || lowerName.includes('cleaning') || lowerName.includes('alcohol')) {
      return 'consumable';
    }
    return 'medicine'; // Default to medicine
  };

  const extractGenericName = (name: string) => {
    const match = name.match(/\(([^)]+)\)/);
    return match ? match[1] : null;
  };

  const extractManufacturer = (name: string) => {
    const match = name.match(/by\s+([^-]+)$/);
    return match ? match[1].trim() : null;
  };

  const extractCategory = (name: string) => {
    // Simple category detection based on common medicine names
    const lowerName = name.toLowerCase();
    if (lowerName.includes('paracetamol') || lowerName.includes('ibuprofen')) return 'Pain Relief';
    if (lowerName.includes('amoxicillin') || lowerName.includes('antibiotic')) return 'Antibiotics';
    if (lowerName.includes('vaccine')) return 'Vaccines';
    if (lowerName.includes('gloves') || lowerName.includes('ppe')) return 'PPE';
    if (lowerName.includes('syringe') || lowerName.includes('injection')) return 'Injection Supplies';
    return 'General';
  };

  const extractDosageForm = (name: string) => {
    const lowerName = name.toLowerCase();
    if (lowerName.includes('tablet')) return 'tablet';
    if (lowerName.includes('capsule')) return 'capsule';
    if (lowerName.includes('syrup')) return 'syrup';
    if (lowerName.includes('injection')) return 'injection';
    return null;
  };

  const extractStrength = (name: string) => {
    const match = name.match(/(\d+(?:\.\d+)?(?:mg|ml|g|%|units?))/i);
    return match ? match[1] : null;
  };

  const extractSupplier = (name: string) => {
    return extractManufacturer(name); // Same as manufacturer for now
  };

  // Enhanced mock inventory data with different product types
  // Note: This is demo data. The actual database has a simpler schema.
  const mockInventoryItems: InventoryItem[] = [
    // Medicines
    {
      id: '1',
      name: 'Paracetamol - (Acetaminophen) 500mg tablet by PharmaCorp', // Combined name to match DB schema
      generic_name: 'Acetaminophen',
      manufacturer: 'PharmaCorp',
      category: 'Pain Relief',
      type: 'medicine',
      dosage_form: 'tablet',
      strength: '500mg',
      unit: 'tablets',
      unit_price: 0.50,
      stock_quantity: 500,
      reorder_level: 100,
      expiry_date: '2025-12-31',
      batch_number: 'PC2024001',
      supplier: 'MedSupply Co.',
      location: 'Shelf A1',
      description: 'Pain relief and fever reducer',
      created_at: '2024-01-01',
      updated_at: '2024-01-15'
    },
    {
      id: '2',
      name: 'Amoxicillin - 250mg capsule by AntibioTech',
      generic_name: 'Amoxicillin',
      manufacturer: 'AntibioTech',
      category: 'Antibiotics',
      type: 'medicine',
      dosage_form: 'capsule',
      strength: '250mg',
      unit: 'capsules',
      unit_price: 1.20,
      stock_quantity: 200,
      reorder_level: 50,
      expiry_date: '2025-08-15',
      batch_number: 'AT2024002',
      supplier: 'MedSupply Co.',
      location: 'Shelf A2',
      description: 'Broad-spectrum antibiotic',
      created_at: '2024-01-01',
      updated_at: '2024-01-15'
    },
    // Medical Supplies
    {
      id: '3',
      name: 'Surgical Gloves - Latex-free size M by MedEquip Ltd.',
      category: 'PPE',
      type: 'medical_supply',
      unit: 'boxes',
      unit_price: 15.00,
      stock_quantity: 50,
      reorder_level: 10,
      supplier: 'MedEquip Ltd.',
      location: 'Storage Room B',
      description: 'Latex-free surgical gloves, size M',
      created_at: '2024-01-01',
      updated_at: '2024-01-15'
    },
    {
      id: '4',
      name: 'Disposable Syringes - 5ml by MedEquip Ltd.',
      category: 'Injection Supplies',
      type: 'medical_supply',
      unit: 'pieces',
      unit_price: 0.25,
      stock_quantity: 1000,
      reorder_level: 200,
      supplier: 'MedEquip Ltd.',
      location: 'Storage Room B',
      description: '5ml disposable syringes',
      created_at: '2024-01-01',
      updated_at: '2024-01-15'
    },
    // Equipment
    {
      id: '5',
      name: 'Digital Thermometer - Infrared by TechMed Solutions',
      category: 'Diagnostic Equipment',
      type: 'equipment',
      unit: 'pieces',
      unit_price: 25.00,
      stock_quantity: 15,
      reorder_level: 5,
      supplier: 'TechMed Solutions',
      location: 'Equipment Room',
      description: 'Digital infrared thermometer',
      created_at: '2024-01-01',
      updated_at: '2024-01-15'
    },
    // Consumables
    {
      id: '6',
      name: 'Alcohol Swabs - 70% isopropyl by CleanMed Supplies',
      category: 'Cleaning Supplies',
      type: 'consumable',
      unit: 'boxes',
      unit_price: 8.00,
      stock_quantity: 30,
      reorder_level: 10,
      supplier: 'CleanMed Supplies',
      location: 'Storage Room C',
      description: '70% isopropyl alcohol swabs',
      created_at: '2024-01-01',
      updated_at: '2024-01-15'
    }
  ];

  // Transform real inventory data to match the interface
  const transformedInventoryItems: InventoryItem[] = (inventoryItems || []).map((item: any) => ({
    id: item.id,
    name: item.medication_name,
    generic_name: extractGenericName(item.medication_name),
    manufacturer: extractManufacturer(item.medication_name),
    category: extractCategory(item.medication_name),
    type: detectProductType(item.medication_name), // Detect product type from name
    dosage_form: extractDosageForm(item.medication_name),
    strength: extractStrength(item.medication_name),
    unit: 'tablets', // Default unit
    unit_price: Number(item.price),
    stock_quantity: item.stock_quantity,
    reorder_level: 10, // Default reorder level
    expiry_date: item.expiry_date,
    batch_number: null,
    supplier: extractSupplier(item.medication_name),
    location: null,
    description: null,
    created_at: item.created_at,
    updated_at: item.updated_at
  }));



  // Combine transformed real inventory data with mock data for demo
  const allInventoryItems = [...transformedInventoryItems, ...mockInventoryItems];



  // Get low stock items
  const lowStockItems = allInventoryItems.filter(item =>
    item && typeof item.stock_quantity === 'number' && typeof item.reorder_level === 'number' &&
    item.stock_quantity <= item.reorder_level
  );

  // Get items by category for statistics
  const getItemsByType = (type: string) => allInventoryItems.filter(item => item?.type === type);

  // Calculate total inventory value
  const totalInventoryValue = allInventoryItems.reduce((total, item) => {
    if (!item || typeof item.stock_quantity !== 'number' || typeof item.unit_price !== 'number') {
      return total;
    }
    return total + (item.stock_quantity * item.unit_price);
  }, 0);



  // Mock sales data
  const [sales, setSales] = useState<Sale[]>([]);

  // Workflow patients - those who have completed consultation and lab tests
  const patientsReadyForPharmacy = allPatients.filter(patient => {
    const hasConsultation = consultationRecords.some(record => record.patient_id === patient.id);
    const hasLabTests = laboratoryRecords.some(record => record.patient_id === patient.id);
    return hasConsultation && hasLabTests;
  });

  const patientsWithPharmacy = patientsReadyForPharmacy.filter(patient =>
    pharmacyRecords.some(record => record.patient_id === patient.id)
  );

  const patientsWithoutPharmacy = patientsReadyForPharmacy.filter(patient =>
    !pharmacyRecords.some(record => record.patient_id === patient.id)
  );

  // Filter inventory items based on search
  const searchFilteredItems = allInventoryItems.filter(item => {
    if (!item) return false;

    const searchLower = searchTerm.toLowerCase();
    return (
      (item.name?.toLowerCase().includes(searchLower)) ||
      (item.generic_name?.toLowerCase().includes(searchLower)) ||
      (item.category?.toLowerCase().includes(searchLower)) ||
      (item.manufacturer?.toLowerCase().includes(searchLower)) ||
      (item.supplier?.toLowerCase().includes(searchLower))
    );
  });

  // Apply both search and type filters
  const finalFilteredItems = inventoryFilter === 'all'
    ? searchFilteredItems
    : searchFilteredItems.filter(item => item?.type === inventoryFilter);

  // POS Functions
  const addToCart = (item: InventoryItem, quantity: number = 1) => {
    if (item.stock_quantity < quantity) {
      toast({
        title: "Insufficient Stock",
        description: `Only ${item.stock_quantity} ${item.unit} available`,
        variant: "destructive"
      });
      return;
    }

    const existingItem = cart.find(cartItem => cartItem.item.id === item.id);
    if (existingItem) {
      const newQuantity = existingItem.quantity + quantity;
      if (newQuantity > item.stock_quantity) {
        toast({
          title: "Insufficient Stock",
          description: `Only ${item.stock_quantity} ${item.unit} available`,
          variant: "destructive"
        });
        return;
      }
      setCart(cart.map(cartItem =>
        cartItem.item.id === item.id
          ? { ...cartItem, quantity: newQuantity, total_price: newQuantity * item.unit_price }
          : cartItem
      ));
    } else {
      setCart([...cart, {
        item,
        quantity,
        unit_price: item.unit_price,
        total_price: quantity * item.unit_price
      }]);
    }
  };

  const removeFromCart = (itemId: string) => {
    setCart(cart.filter(cartItem => cartItem.item.id !== itemId));
  };

  const updateCartQuantity = (itemId: string, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(itemId);
      return;
    }

    const inventoryItem = allInventoryItems.find(item => item.id === itemId);
    if (!inventoryItem || quantity > inventoryItem.stock_quantity) {
      toast({
        title: "Insufficient Stock",
        description: `Only ${inventoryItem?.stock_quantity || 0} ${inventoryItem?.unit || 'units'} available`,
        variant: "destructive"
      });
      return;
    }

    setCart(cart.map(cartItem =>
      cartItem.item.id === itemId
        ? { ...cartItem, quantity, total_price: quantity * cartItem.unit_price }
        : cartItem
    ));
  };

  const getCartTotal = () => {
    return cart.reduce((total, cartItem) => total + cartItem.total_price, 0);
  };

  const processSale = async () => {
    if (cart.length === 0) {
      toast({
        title: "Empty Cart",
        description: "Please add items to cart before processing sale",
        variant: "destructive"
      });
      return;
    }

    if (!pharmacistName.trim()) {
      toast({
        title: "Missing Information",
        description: "Please enter pharmacist name",
        variant: "destructive"
      });
      return;
    }

    try {
      // Check stock availability before processing
      const stockErrors = [];
      for (const cartItem of cart) {
        const inventoryItem = allInventoryItems.find(item => item.id === cartItem.item.id);
        if (!inventoryItem) {
          stockErrors.push(`${cartItem.item.name} not found in inventory`);
        } else if (inventoryItem.stock_quantity < cartItem.quantity) {
          stockErrors.push(`Insufficient stock for ${cartItem.item.name}. Available: ${inventoryItem.stock_quantity}, Requested: ${cartItem.quantity}`);
        }
      }

      if (stockErrors.length > 0) {
        toast({
          title: "Stock Error",
          description: stockErrors.join('. '),
          variant: "destructive"
        });
        return;
      }

      // Update inventory stock for each item in cart
      const inventoryUpdates = [];
      for (const cartItem of cart) {
        const inventoryItem = allInventoryItems.find(item => item.id === cartItem.item.id);
        if (inventoryItem) {
          const newQuantity = Math.max(0, inventoryItem.stock_quantity - cartItem.quantity);
          inventoryUpdates.push(
            updatePharmacyInventory.mutateAsync({
              id: inventoryItem.id,
              inventoryData: {
                stock_quantity: newQuantity,
                updated_at: new Date().toISOString()
              }
            })
          );
        }
      }

      // Execute all inventory updates
      await Promise.all(inventoryUpdates);

      // Store the cart total before clearing cart
      const saleTotal = getCartTotal();

      // Create sale record
      const newSale: Sale = {
        id: Date.now().toString(),
        customer_name: customerName || undefined,
        customer_phone: customerPhone || undefined,
        items: cart,
        total_amount: saleTotal,
        payment_method: paymentMethod,
        pharmacist_name: pharmacistName,
        sale_date: new Date().toISOString(),
        notes: saleNotes || undefined
      };

      // Add sale to local state
      setSales([...sales, newSale]);

      // Create revenue entry for billing
      try {
        await createRevenueEntry.mutateAsync({
          description: `Pharmacy Sale - ${customerName || 'Walk-in Customer'}`,
          amount: saleTotal.toString(),
          date: new Date(),
          category: 'Pharmacy Sales',
          paymentMethod: paymentMethod === 'cash' ? 'Cash' :
                        paymentMethod === 'card' ? 'Credit Card' : 'Insurance',
          notes: `Items: ${cart.map(item => `${item.medicine.name} (${item.quantity})`).join(', ')}${saleNotes ? ` | ${saleNotes}` : ''}`
        });
      } catch (revenueError) {
        console.warn('Failed to create revenue entry:', revenueError);
        // Don't fail the sale if revenue entry fails
      }

      // Clear cart and form
      setCart([]);
      setCustomerName('');
      setCustomerPhone('');
      setPaymentMethod('cash');
      setSaleNotes('');

      toast({
        title: "Sale Completed Successfully",
        description: `Sale of ${formatCurrency(saleTotal)} processed and revenue recorded`,
      });

    } catch (error) {
      console.error('Error processing sale:', error);
      toast({
        title: "Error",
        description: "Failed to process sale. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleStartDispensing = (patient: any) => {
    setSelectedPatient(patient);
    setIsFormOpen(true);
  };

  const handleDispensingComplete = async (pharmacyData: any) => {
    if (!selectedPatient) return;

    try {
      // In real app, update inventory stock based on dispensed medications
      // This would be handled by the backend API

      setIsFormOpen(false);
      setSelectedPatient(null);

      toast({
        title: "Medication Dispensed",
        description: "Patient medication dispensed and inventory updated",
      });
    } catch (error) {
      console.error('Error completing pharmacy dispensing:', error);
    }
  };

  const getPatientAge = (dateOfBirth: string) => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return age;
  };

  const getTotalRevenue = () => {
    return pharmacyRecords.reduce((total, record) => total + (record.total_cost || 0), 0);
  };

  const getTodayRevenue = () => {
    const today = new Date().toDateString();
    return sales
      .filter(sale => new Date(sale.sale_date).toDateString() === today)
      .reduce((total, sale) => total + sale.total_amount, 0);
  };





  return (
    <ErrorBoundary>
      <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Pharmacy Department</h1>
            <p className="text-gray-600 mt-1 text-sm sm:text-base">Medication dispensing, inventory management & POS system</p>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
            <Button
              variant="outline"
              onClick={() => navigate('/laboratory-department')}
              className="w-full sm:w-auto text-sm"
            >
              <span className="hidden sm:inline">Back to Laboratory</span>
              <span className="sm:hidden">Laboratory</span>
            </Button>
            <Button
              variant="outline"
              onClick={() => navigate('/patients')}
              className="w-full sm:w-auto text-sm"
            >
              <span className="hidden sm:inline">View Final Records</span>
              <span className="sm:hidden">Records</span>
            </Button>
          </div>
        </div>
        
        {/* Search Bar */}
        <div className="relative w-full sm:w-80">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder={activeTab === 'inventory' || activeTab === 'pos' ? "Search products..." : "Search patients..."}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 w-full text-sm sm:text-base"
          />
        </div>

        {/* Main Tabs */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-1 sm:p-2 mb-6 backdrop-blur-sm">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 bg-gradient-to-r from-gray-50 to-gray-100 p-1 sm:p-1.5 rounded-xl shadow-md border border-gray-200">
            <TabsTrigger
              value="inventory"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-600 data-[state=active]:to-green-700
                         data-[state=active]:text-white data-[state=active]:shadow-lg data-[state=active]:shadow-green-200
                         data-[state=active]:border-green-500 data-[state=active]:scale-[1.02]
                         text-gray-600 hover:text-gray-800 hover:bg-white hover:shadow-sm
                         transition-all duration-300 font-semibold px-2 sm:px-4 py-2 sm:py-3.5 rounded-lg border border-transparent
                         flex items-center justify-center gap-1 sm:gap-2 relative overflow-hidden text-xs sm:text-sm
                         before:absolute before:inset-0 before:bg-gradient-to-r before:from-green-50 before:to-transparent
                         before:opacity-0 hover:before:opacity-100 before:transition-opacity before:duration-300"
            >
              <Package className="h-3 w-3 sm:h-4 sm:w-4 relative z-10" />
              <span className="relative z-10 hidden sm:inline">Inventory Management</span>
              <span className="relative z-10 sm:hidden">Inventory</span>
              {activeTab === 'inventory' && (
                <div className="absolute -top-1 -right-1 w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              )}
            </TabsTrigger>
            <TabsTrigger
              value="pos"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-600 data-[state=active]:to-purple-700
                         data-[state=active]:text-white data-[state=active]:shadow-lg data-[state=active]:shadow-purple-200
                         data-[state=active]:border-purple-500 data-[state=active]:scale-[1.02]
                         text-gray-600 hover:text-gray-800 hover:bg-white hover:shadow-sm
                         transition-all duration-300 font-semibold px-2 sm:px-4 py-2 sm:py-3.5 rounded-lg border border-transparent
                         flex items-center justify-center gap-1 sm:gap-2 relative overflow-hidden text-xs sm:text-sm
                         before:absolute before:inset-0 before:bg-gradient-to-r before:from-purple-50 before:to-transparent
                         before:opacity-0 hover:before:opacity-100 before:transition-opacity before:duration-300"
            >
              <ShoppingCart className="h-3 w-3 sm:h-4 sm:w-4 relative z-10" />
              <span className="relative z-10 hidden sm:inline">Point of Sale (POS)</span>
              <span className="relative z-10 sm:hidden">POS</span>
              {activeTab === 'pos' && (
                <div className="absolute -top-1 -right-1 w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
              )}
            </TabsTrigger>
          </TabsList>




          {/* Point of Sale Tab */}
          <TabsContent value="pos" className="space-y-4 sm:space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
              {/* Product Selection */}
              <div className="lg:col-span-2">
                <Card>
                  <CardHeader className="pb-3 sm:pb-6">
                    <CardTitle className="flex items-center text-sm sm:text-base">
                      <Package className="mr-2 h-4 w-4 sm:h-5 sm:w-5" />
                      Product Selection
                    </CardTitle>
                    <div className="flex flex-wrap gap-2 mt-2">
                      <Button
                        variant={inventoryFilter === 'all' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setInventoryFilter('all')}
                        className="text-xs sm:text-sm px-2 sm:px-3"
                      >
                        All
                      </Button>
                      <Button
                        variant={inventoryFilter === 'medicine' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setInventoryFilter('medicine')}
                        className="text-xs sm:text-sm px-2 sm:px-3"
                      >
                        <Pill className="mr-1 h-3 w-3" />
                        <span className="hidden sm:inline">Medicines</span>
                        <span className="sm:hidden">Meds</span>
                      </Button>
                      <Button
                        variant={inventoryFilter === 'medical_supply' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setInventoryFilter('medical_supply')}
                        className="text-xs sm:text-sm px-2 sm:px-3"
                      >
                        <Package className="mr-1 h-3 w-3" />
                        <span className="hidden sm:inline">Supplies</span>
                        <span className="sm:hidden">Supply</span>
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent className="p-3 sm:p-6">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 max-h-80 sm:max-h-96 overflow-y-auto">
                      {finalFilteredItems.map((item) => (
                        <Card key={item.id} className="hover:shadow-md transition-shadow">
                          <CardContent className="p-3 sm:p-4">
                            <div className="flex items-start justify-between mb-2">
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center mb-1">
                                  {item.type === 'medicine' && <Pill className="h-3 w-3 mr-1 text-blue-500 flex-shrink-0" />}
                                  {item.type === 'medical_supply' && <Package className="h-3 w-3 mr-1 text-green-500 flex-shrink-0" />}
                                  {item.type === 'equipment' && <Eye className="h-3 w-3 mr-1 text-purple-500 flex-shrink-0" />}
                                  {item.type === 'consumable' && <ShoppingCart className="h-3 w-3 mr-1 text-orange-500 flex-shrink-0" />}
                                  <h3 className="font-medium text-gray-900 text-sm sm:text-base truncate">{item.name}</h3>
                                </div>
                                {item.generic_name && <p className="text-xs sm:text-sm text-gray-500 truncate">{item.generic_name}</p>}
                                {item.strength && item.dosage_form && (
                                  <p className="text-xs text-gray-400 truncate">{item.strength} • {item.dosage_form}</p>
                                )}
                                <p className="text-xs text-gray-400 capitalize">{item.type.replace('_', ' ')}</p>
                              </div>
                              <Badge
                                variant={item.stock_quantity > item.reorder_level ? "default" : "destructive"}
                                className="text-xs ml-2 flex-shrink-0"
                              >
                                {item.stock_quantity} {item.unit}
                              </Badge>
                            </div>

                            <div className="flex items-center justify-between mt-3">
                              <div className="text-sm sm:text-lg font-bold text-green-600">
                                {formatCurrency(item.unit_price)}
                              </div>
                              <Button
                                size="sm"
                                onClick={() => addToCart(item)}
                                disabled={item.stock_quantity === 0}
                                className="bg-green-600 hover:bg-green-700 text-xs sm:text-sm px-2 sm:px-3"
                              >
                                <Plus className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                                <span className="hidden sm:inline">Add to Cart</span>
                                <span className="sm:hidden">Add</span>
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Shopping Cart */}
              <div>
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <ShoppingCart className="mr-2 h-5 w-5" />
                      Shopping Cart ({cart.length})
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {cart.length === 0 ? (
                      <div className="text-center py-8">
                        <ShoppingCart className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                        <p className="text-gray-500">Cart is empty</p>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {cart.map((cartItem) => (
                          <div key={cartItem.item.id} className="flex items-center justify-between p-3 border rounded">
                            <div className="flex-1">
                              <div className="flex items-center mb-1">
                                {cartItem.item.type === 'medicine' && <Pill className="h-3 w-3 mr-1 text-blue-500" />}
                                {cartItem.item.type === 'medical_supply' && <Package className="h-3 w-3 mr-1 text-green-500" />}
                                {cartItem.item.type === 'equipment' && <Eye className="h-3 w-3 mr-1 text-purple-500" />}
                                {cartItem.item.type === 'consumable' && <ShoppingCart className="h-3 w-3 mr-1 text-orange-500" />}
                                <h4 className="font-medium text-sm">{cartItem.item.name}</h4>
                              </div>
                              <p className="text-xs text-gray-500">{formatCurrency(cartItem.unit_price)} per {cartItem.item.unit}</p>
                              <p className="text-xs text-gray-400 capitalize">{cartItem.item.type.replace('_', ' ')}</p>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => updateCartQuantity(cartItem.item.id, cartItem.quantity - 1)}
                              >
                                <Minus className="h-3 w-3" />
                              </Button>
                              <span className="w-8 text-center text-sm">{cartItem.quantity}</span>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => updateCartQuantity(cartItem.item.id, cartItem.quantity + 1)}
                              >
                                <Plus className="h-3 w-3" />
                              </Button>
                              <Button
                                size="sm"
                                variant="destructive"
                                onClick={() => removeFromCart(cartItem.item.id)}
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                        ))}

                        <div className="border-t pt-3 mt-3">
                          <div className="flex justify-between items-center text-lg font-bold">
                            <span>Total:</span>
                            <span className="text-green-600">{formatCurrency(getCartTotal())}</span>
                          </div>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Checkout Form */}
                {cart.length > 0 && (
                  <Card className="mt-4">
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <Calculator className="mr-2 h-5 w-5" />
                        Checkout
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <Label htmlFor="customer-name">Customer Name (Optional)</Label>
                        <Input
                          id="customer-name"
                          value={customerName}
                          onChange={(e) => setCustomerName(e.target.value)}
                          placeholder="Enter customer name"
                        />
                      </div>

                      <div>
                        <Label htmlFor="pharmacist-name">Pharmacist Name *</Label>
                        <Input
                          id="pharmacist-name"
                          value={pharmacistName}
                          onChange={(e) => setPharmacistName(e.target.value)}
                          placeholder="Enter pharmacist name"
                          required
                        />
                      </div>

                      <div>
                        <Label htmlFor="payment-method">Payment Method</Label>
                        <Select value={paymentMethod} onValueChange={(value: 'cash' | 'card' | 'insurance') => setPaymentMethod(value)}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="cash">Cash</SelectItem>
                            <SelectItem value="card">Card</SelectItem>
                            <SelectItem value="insurance">Insurance</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <Button
                        onClick={processSale}
                        className="w-full bg-green-600 hover:bg-green-700"
                        size="lg"
                      >
                        <Receipt className="mr-2 h-5 w-5" />
                        Process Sale ({formatCurrency(getCartTotal())})
                      </Button>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          </TabsContent>

          {/* Inventory Management Tab */}
          <TabsContent value="inventory" className="space-y-6">
            {/* Inventory Header with Actions */}
            <div className="flex justify-between items-center">
              <div>
                <h2 className="text-xl font-semibold">Pharmacy Inventory</h2>
                <p className="text-gray-600">Manage medicines, medical supplies, equipment and consumables</p>
              </div>
              <div className="flex space-x-3">
                <Button
                  variant="outline"
                  onClick={() => {
                    setInventoryAction('add');
                    setSelectedItem(null);
                    setIsInventoryDialogOpen(true);
                  }}
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Add Product
                </Button>
                <Button
                  onClick={() => {
                    // Bulk import functionality
                    toast({
                      title: "Bulk Import",
                      description: "Bulk import functionality coming soon",
                    });
                  }}
                >
                  <Package className="mr-2 h-4 w-4" />
                  Bulk Import
                </Button>
              </div>
            </div>

            {/* Product Type Filter */}
            <div className="flex space-x-2">
              <Button
                variant={inventoryFilter === 'all' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setInventoryFilter('all')}
              >
                All Products ({allInventoryItems.length})
              </Button>
              <Button
                variant={inventoryFilter === 'medicine' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setInventoryFilter('medicine')}
              >
                <Pill className="mr-1 h-4 w-4" />
                Medicines ({getItemsByType('medicine').length})
              </Button>
              <Button
                variant={inventoryFilter === 'medical_supply' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setInventoryFilter('medical_supply')}
              >
                <Package className="mr-1 h-4 w-4" />
                Medical Supplies ({getItemsByType('medical_supply').length})
              </Button>
              <Button
                variant={inventoryFilter === 'equipment' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setInventoryFilter('equipment')}
              >
                <Eye className="mr-1 h-4 w-4" />
                Equipment ({getItemsByType('equipment').length})
              </Button>
              <Button
                variant={inventoryFilter === 'consumable' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setInventoryFilter('consumable')}
              >
                <ShoppingCart className="mr-1 h-4 w-4" />
                Consumables ({getItemsByType('consumable').length})
              </Button>
            </div>

            {/* Inventory Statistics */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className="p-2 bg-blue-100 rounded-lg mr-4">
                      <Package className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-gray-900">{allInventoryItems.length}</p>
                      <p className="text-sm text-gray-600">Total Products</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className="p-2 bg-green-100 rounded-lg mr-4">
                      <CheckCircle className="h-6 w-6 text-green-600" />
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-gray-900">
                        {allInventoryItems.filter(item => item.stock_quantity > item.reorder_level).length}
                      </p>
                      <p className="text-sm text-gray-600">Well Stocked</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className="p-2 bg-red-100 rounded-lg mr-4">
                      <AlertTriangle className="h-6 w-6 text-red-600" />
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-gray-900">{lowStockItems.length}</p>
                      <p className="text-sm text-gray-600">Low Stock</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className="p-2 bg-purple-100 rounded-lg mr-4">
                      <DollarSign className="h-6 w-6 text-purple-600" />
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-gray-900">
                        {formatCurrency(totalInventoryValue)}
                      </p>
                      <p className="text-sm text-gray-600">Inventory Value</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Low Stock Alert */}
            {lowStockItems.length > 0 && (
              <Card className="border-red-200 bg-red-50">
                <CardHeader>
                  <CardTitle className="text-red-800 flex items-center">
                    <AlertTriangle className="mr-2 h-5 w-5" />
                    Low Stock Alert
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-red-700 mb-2">{lowStockItems.length} products are running low on stock:</p>
                  <div className="flex flex-wrap gap-2">
                    {lowStockItems.map(item => (
                      <Badge key={item.id} variant="destructive">
                        {item.name} ({item.stock_quantity} {item.unit} left)
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Inventory Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
              {finalFilteredItems.map((item) => (
                <Card key={item.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <div className="flex items-center mb-1">
                          {item.type === 'medicine' && <Pill className="h-4 w-4 mr-1 text-blue-500" />}
                          {item.type === 'medical_supply' && <Package className="h-4 w-4 mr-1 text-green-500" />}
                          {item.type === 'equipment' && <Eye className="h-4 w-4 mr-1 text-purple-500" />}
                          {item.type === 'consumable' && <ShoppingCart className="h-4 w-4 mr-1 text-orange-500" />}
                          <h3 className="font-medium text-gray-900">{item.name}</h3>
                        </div>
                        {item.generic_name && <p className="text-sm text-gray-500">{item.generic_name}</p>}
                        {item.strength && item.dosage_form && (
                          <p className="text-xs text-gray-400">{item.strength} • {item.dosage_form}</p>
                        )}
                        {item.batch_number && <p className="text-xs text-gray-400">Batch: {item.batch_number}</p>}
                        {item.location && <p className="text-xs text-gray-400">Location: {item.location}</p>}
                      </div>
                      <div className="flex flex-col items-end">
                        <Badge
                          variant={item.stock_quantity > item.reorder_level ? "default" : "destructive"}
                          className="text-xs mb-1"
                        >
                          {item.stock_quantity > item.reorder_level ? 'In Stock' : 'Low Stock'}
                        </Badge>
                        <Badge variant="outline" className="text-xs capitalize">
                          {item.type.replace('_', ' ')}
                        </Badge>
                      </div>
                    </div>

                    <div className="space-y-2 mb-4">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Category:</span>
                        <span className="font-medium">{item.category}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Stock:</span>
                        <span className={`font-medium ${item.stock_quantity <= item.reorder_level ? 'text-red-600' : 'text-green-600'}`}>
                          {item.stock_quantity} {item.unit}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Price:</span>
                        <span className="font-medium text-green-600">{formatCurrency(item.unit_price)}</span>
                      </div>
                      {item.expiry_date && (
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">Expires:</span>
                          <span className="font-medium">{new Date(item.expiry_date).toLocaleDateString()}</span>
                        </div>
                      )}
                      {item.manufacturer && (
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">Manufacturer:</span>
                          <span className="font-medium">{item.manufacturer}</span>
                        </div>
                      )}
                      {item.supplier && (
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">Supplier:</span>
                          <span className="font-medium">{item.supplier}</span>
                        </div>
                      )}
                    </div>

                    <div className="flex space-x-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          setSelectedItem(item);
                          setInventoryAction('view');
                          setIsInventoryDialogOpen(true);
                        }}
                        className="flex-1"
                      >
                        <Eye className="h-3 w-3 mr-1" />
                        View
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          setSelectedItem(item);
                          setInventoryAction('edit');
                          setIsInventoryDialogOpen(true);
                        }}
                        className="flex-1"
                      >
                        <Edit className="h-3 w-3 mr-1" />
                        Edit
                      </Button>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => {
                          if (confirm(`Are you sure you want to delete ${item.name}?`)) {
                            // In real app, call deletePharmacyInventory.mutateAsync(item.id)
                            toast({
                              title: "Item Deleted",
                              description: `${item.name} has been removed from inventory`,
                            });
                          }
                        }}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {finalFilteredItems.length === 0 && (
              <Card>
                <CardContent className="p-8 text-center">
                  <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No products found</h3>
                  <p className="text-gray-500 mb-4">
                    {inventoryFilter === 'all'
                      ? "No products in inventory yet."
                      : `No ${inventoryFilter.replace('_', ' ')} products found.`}
                  </p>
                  <Button
                    onClick={() => {
                      setInventoryAction('add');
                      setSelectedItem(null);
                      setIsInventoryDialogOpen(true);
                    }}
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Add Product
                  </Button>
                </CardContent>
              </Card>
            )}

            {/* Inventory Dialog */}
            <Dialog open={isInventoryDialogOpen} onOpenChange={setIsInventoryDialogOpen}>
              <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                <PharmacyInventoryForm
                  onClose={() => setIsInventoryDialogOpen(false)}
                  onSuccess={() => {
                    // Manually refresh inventory data
                    queryClient.invalidateQueries({ queryKey: ['pharmacy_inventory'] });
                    toast({
                      title: "Success",
                      description: "Product added to inventory successfully",
                    });
                  }}
                  existingItem={selectedItem}
                  action={inventoryAction}
                />
              </DialogContent>
            </Dialog>
          </TabsContent>





        </Tabs>
        </div>
      </div>
    </Layout>
    </ErrorBoundary>
  );
};

export default PharmacyDepartment;
