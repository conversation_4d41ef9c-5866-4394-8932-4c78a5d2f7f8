
import React, { useState } from 'react';
import { Layout } from '../components/Layout';
import { 
  Search, 
  Filter, 
  Plus, 
  Phone, 
  Mail, 
  Calendar, 
  Clock, 
  Star, 
  MoreVertical,
  Stethoscope
} from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { Dialog, DialogContent, DialogTrigger, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { DoctorForm } from '@/components/DoctorForm';
import { useSupabaseData } from '@/hooks/useSupabaseData';

const Doctors = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [selectedDoctor, setSelectedDoctor] = useState(null);

  const { useDoctors } = useSupabaseData();
  const { data: doctors = [], isLoading, error } = useDoctors();

  // Filter doctors based on search term
  const filteredDoctors = doctors.filter(doctor => 
    doctor.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    doctor.specialty?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    doctor.id?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusBadge = (status) => {
    switch(status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-200">Active</Badge>;
      case 'on-leave':
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200">On Leave</Badge>;
      case 'inactive':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-200">Inactive</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  const handleEditDoctor = (doctor) => {
    setSelectedDoctor(doctor);
    setIsFormOpen(true);
  };

  const handleCloseForm = () => {
    setIsFormOpen(false);
    setSelectedDoctor(null);
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg text-gray-600">Loading doctors...</div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg text-red-600">Error loading doctors: {error.message}</div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Doctors</h1>
            <p className="text-gray-600 mt-1">Manage hospital staff and specialists</p>
          </div>
          <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
            <DialogTrigger asChild>
              <Button className="bg-purple-600 hover:bg-purple-700 text-white">
                <Plus className="mr-2 h-4 w-4" />
                Add Doctor
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
              <DialogHeader className="sr-only">
                <DialogTitle>
                  {selectedDoctor ? 'Edit Doctor Profile' : 'Add New Doctor'}
                </DialogTitle>
                <DialogDescription>
                  {selectedDoctor ? 'Update the doctor\'s information and settings' : 'Enter the doctor\'s information to add them to the system'}
                </DialogDescription>
              </DialogHeader>
              <DoctorForm doctor={selectedDoctor} onClose={handleCloseForm} />
            </DialogContent>
          </Dialog>
        </div>

        {/* Search and Filter */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <input
              type="text"
              placeholder="Search doctors by name, specialty, or ID..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>
          <div className="flex gap-2">
            <Button variant="outline" className="flex items-center gap-2">
              <Filter size={16} />
              <span>Filter</span>
            </Button>
            <Button variant="outline" className="flex items-center gap-2">
              <Stethoscope size={16} />
              <span>All Specialties</span>
            </Button>
          </div>
        </div>

        {/* No doctors message */}
        {filteredDoctors.length === 0 && !isLoading && (
          <Card className="p-8 text-center">
            <h3 className="text-lg font-medium text-gray-900 mb-2">No doctors found</h3>
            <p className="text-gray-500 mb-4">
              {searchTerm ? 'Try adjusting your search terms.' : 'Get started by adding your first doctor.'}
            </p>
            <Button onClick={() => setIsFormOpen(true)} className="bg-purple-600 hover:bg-purple-700">
              <Plus className="mr-2 h-4 w-4" />
              Add Doctor
            </Button>
          </Card>
        )}

        {/* Tabs for different views */}
        {filteredDoctors.length > 0 && (
          <Tabs defaultValue="grid" className="w-full">
            <TabsList className="grid w-full max-w-md grid-cols-2">
              <TabsTrigger value="grid">Grid View</TabsTrigger>
              <TabsTrigger value="list">List View</TabsTrigger>
            </TabsList>
            
            {/* Grid View */}
            <TabsContent value="grid" className="mt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredDoctors.map((doctor) => (
                  <Card key={doctor.id} className="overflow-hidden">
                    <div className="p-6 flex flex-col items-center text-center border-b">
                      <Avatar className="h-24 w-24 mb-4">
                        <AvatarImage src={doctor.photo} alt={doctor.name} />
                        <AvatarFallback>{doctor.name?.split(' ').map(n => n[0]).join('') || 'DR'}</AvatarFallback>
                      </Avatar>
                      <h3 className="font-bold text-lg text-gray-900">{doctor.name}</h3>
                      <p className="text-purple-600 font-medium">{doctor.specialty}</p>
                      <p className="text-sm text-gray-500 mt-1">{doctor.qualification}</p>
                      <div className="flex items-center mt-2">
                        <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
                        <span className="ml-1 text-sm font-medium">4.8</span>
                        <span className="mx-2 text-gray-300">•</span>
                        <span className="text-sm text-gray-500">{doctor.experience || 0} years exp</span>
                      </div>
                      <div className="mt-3">
                        {getStatusBadge(doctor.status)}
                      </div>
                    </div>
                    <CardContent className="p-6">
                      <div className="space-y-4">
                        <div className="flex items-center">
                          <Mail className="h-4 w-4 text-gray-500 mr-2" />
                          <span className="text-sm text-gray-600">{doctor.email}</span>
                        </div>
                        <div className="flex items-center">
                          <Phone className="h-4 w-4 text-gray-500 mr-2" />
                          <span className="text-sm text-gray-600">{doctor.phone_number}</span>
                        </div>
                        {doctor.bio && (
                          <div className="text-sm text-gray-600">
                            <p className="line-clamp-3">{doctor.bio}</p>
                          </div>
                        )}
                      </div>
                      <div className="mt-6 flex justify-end">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleEditDoctor(doctor)}
                        >
                          Edit Profile
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
            
            {/* List View */}
            <TabsContent value="list" className="mt-4">
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50 border-b border-gray-200">
                      <tr>
                        <th className="text-left py-4 px-6 font-semibold text-gray-900">DOCTOR</th>
                        <th className="text-left py-4 px-6 font-semibold text-gray-900">SPECIALTY</th>
                        <th className="text-left py-4 px-6 font-semibold text-gray-900">CONTACT</th>
                        <th className="text-left py-4 px-6 font-semibold text-gray-900">EXPERIENCE</th>
                        <th className="text-left py-4 px-6 font-semibold text-gray-900">STATUS</th>
                        <th className="text-left py-4 px-6 font-semibold text-gray-900">ACTIONS</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredDoctors.map((doctor) => (
                        <tr key={doctor.id} className="border-b border-gray-100 hover:bg-gray-50">
                          <td className="py-4 px-6">
                            <div className="flex items-center space-x-3">
                              <Avatar>
                                <AvatarImage src={doctor.photo} alt={doctor.name} />
                                <AvatarFallback>{doctor.name?.split(' ').map(n => n[0]).join('') || 'DR'}</AvatarFallback>
                              </Avatar>
                              <div>
                                <p className="font-medium text-gray-900">{doctor.name}</p>
                                <p className="text-sm text-gray-500">{doctor.id}</p>
                              </div>
                            </div>
                          </td>
                          <td className="py-4 px-6">
                            <p className="text-gray-900">{doctor.specialty}</p>
                            <p className="text-sm text-gray-500">{doctor.qualification}</p>
                          </td>
                          <td className="py-4 px-6">
                            <p className="text-gray-900">{doctor.email}</p>
                            <p className="text-sm text-gray-500">{doctor.phone_number}</p>
                          </td>
                          <td className="py-4 px-6 text-gray-900">{doctor.experience || 0} years</td>
                          <td className="py-4 px-6">
                            {getStatusBadge(doctor.status)}
                          </td>
                          <td className="py-4 px-6">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon">
                                  <MoreVertical className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem onClick={() => handleEditDoctor(doctor)}>
                                  Edit Profile
                                </DropdownMenuItem>
                                <DropdownMenuItem>View Schedule</DropdownMenuItem>
                                <DropdownMenuItem>View Patients</DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem className="text-red-600">
                                  {doctor.status === 'active' ? 'Set as Inactive' : 'Set as Active'}
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        )}

        {/* Pagination */}
        {filteredDoctors.length > 0 && (
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-600">Showing {filteredDoctors.length} of {doctors.length} doctors</p>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm">Previous</Button>
              <Button variant="outline" size="sm">Next</Button>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default Doctors;
