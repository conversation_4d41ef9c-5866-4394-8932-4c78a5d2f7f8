import React, { useState, useRef } from 'react';
import { Layout } from '../components/Layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AppointmentForm, AppointmentFormRef } from '../components/AppointmentForm';
import { Plus, Search, Calendar, Clock, User, Building2 } from 'lucide-react';
import { useSupabaseData } from '@/hooks/useSupabaseData';
import { format } from 'date-fns';

const Appointments = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const formRef = useRef<AppointmentFormRef>(null);

  const { useAppointments, usePatients } = useSupabaseData();
  const { data: appointments, isLoading } = useAppointments();
  const { data: patients } = usePatients();

  // Filter appointments based on search term
  const filteredAppointments = appointments?.filter(appointment =>
    appointment.patient_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    appointment.doctor?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    appointment.department?.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  // Get patient details for appointment
  const getPatientDetails = (patientName: string) => {
    return patients?.find(p => p.patient_name === patientName);
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'scheduled':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'in-progress':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Function to handle button click and trigger form submission
  const handleScheduleAppointment = () => {
    formRef.current?.submitForm();
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg text-gray-600">Loading appointments...</div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Appointments</h1>
            <p className="text-gray-600 mt-1">Manage patient appointments and schedules</p>
          </div>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                New Appointment
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[800px] flex flex-col max-h-[95vh] overflow-hidden p-0">
              <DialogHeader className="px-6 pt-6 pb-4 flex-shrink-0">
                <DialogTitle>Schedule New Appointment</DialogTitle>
              </DialogHeader>
              <div className="flex-1 overflow-y-auto px-6 pb-6">
                <AppointmentForm
                  ref={formRef}
                  onClose={() => setIsDialogOpen(false)}
                  onSuccess={() => setIsDialogOpen(false)}
                />
              </div>
              <div className="flex justify-end gap-2 px-6 py-4 border-t border-gray-200 flex-shrink-0">
                <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button 
                  type="button"
                  className="bg-purple-600 hover:bg-purple-700 text-white"
                  onClick={handleScheduleAppointment}
                  disabled={formRef.current?.isPending}
                >
                  {formRef.current?.isPending ? 'Scheduling...' : 'Schedule Appointment'}
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search appointments by patient, doctor, or department..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Appointments List */}
        <Card>
          <CardHeader>
            <CardTitle>All Appointments</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Patient</TableHead>
                    <TableHead>Doctor</TableHead>
                    <TableHead>Department</TableHead>
                    <TableHead>Date & Time</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAppointments.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        <div className="text-gray-500">
                          {appointments?.length === 0 ? 'No appointments scheduled yet' : 'No appointments match your search'}
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredAppointments.map((appointment) => {
                      const patient = getPatientDetails(appointment.patient_name);
                      return (
                        <TableRow key={appointment.id}>
                          <TableCell>
                            <div className="flex flex-col">
                              <div className="flex items-center space-x-2">
                                <User className="h-4 w-4 text-gray-400" />
                                <span className="font-medium">{appointment.patient_name}</span>
                              </div>
                              {patient && (
                                <div className="text-sm text-gray-500 mt-1">
                                  ID: {patient.id?.slice(0, 8)}... | Phone: {patient.phone_number}
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="font-medium">
                              {appointment.doctor?.includes('(') ? (
                                <div>
                                  <div>{appointment.doctor.split('(')[0].trim()}</div>
                                  <div className="text-sm text-gray-500">
                                    {appointment.doctor.match(/\((.*?)\)/)?.[1]}
                                  </div>
                                </div>
                              ) : (
                                appointment.doctor
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Building2 className="h-4 w-4 text-gray-400" />
                              <span>{appointment.department || <span className="text-gray-500 italic">No department</span>}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex flex-col space-y-1">
                              <div className="flex items-center space-x-2">
                                <Calendar className="h-4 w-4 text-gray-400" />
                                <span className="text-sm">
                                  {format(new Date(appointment.date_time), 'MMM dd, yyyy')}
                                </span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <Clock className="h-4 w-4 text-gray-400" />
                                <span className="text-sm">
                                  {format(new Date(appointment.date_time), 'hh:mm a')}
                                </span>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <span className="text-sm">{appointment.type || 'Consultation'}</span>
                          </TableCell>
                          <TableCell>
                            <Badge className={getStatusBadgeColor(appointment.status)}>
                              {appointment.status}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Button variant="outline" size="sm">
                              View Details
                            </Button>
                          </TableCell>
                        </TableRow>
                      );
                    })
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default Appointments;
