import React, { useState, useRef } from 'react';
import { Layout } from '../components/Layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AppointmentForm, AppointmentFormRef } from '../components/AppointmentForm';
import {
  Plus,
  Search,
  Calendar,
  Clock,
  User,
  Building2,
  Filter,
  CalendarDays,
  Stethoscope,
  TestTube,
  Pill,
  CheckCircle,
  AlertCircle,
  Activity,
  Users,
  TrendingUp,
  Eye,
  Edit,
  Trash2
} from 'lucide-react';
import { useSupabaseData } from '@/hooks/useSupabaseData';
import { useWorkflowData } from '@/hooks/useWorkflowData';
import { format, isToday, isTomorrow, isYesterday } from 'date-fns';

const Appointments = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');

  const formRef = useRef<AppointmentFormRef>(null);

  const { useAppointments, usePatients } = useSupabaseData();
  const { usePatientWorkflows, useConsultationRecords, useLaboratoryRecords, usePharmacyRecords } = useWorkflowData();

  const { data: appointments, isLoading } = useAppointments();
  const { data: patients } = usePatients();
  const { data: workflows } = usePatientWorkflows();
  const { data: consultationRecords } = useConsultationRecords();
  const { data: laboratoryRecords } = useLaboratoryRecords();
  const { data: pharmacyRecords } = usePharmacyRecords();

  // Enhanced filtering with workflow integration
  const filteredAppointments = appointments?.filter(appointment => {
    const matchesSearch = appointment.patient_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      appointment.doctor?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      appointment.department?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || appointment.status === statusFilter;
    const matchesType = typeFilter === 'all' || appointment.type === typeFilter;

    return matchesSearch && matchesStatus && matchesType;
  }) || [];

  // Get patient details for appointment with workflow integration
  const getPatientDetails = (patientName: string) => {
    const patient = patients?.find(p => p.patient_name === patientName);
    if (!patient) return null;

    // Get workflow information
    const workflow = workflows?.find(w => w.patient_id === patient.id);
    const consultation = consultationRecords?.find(c => c.patient_id === patient.id);
    const laboratory = laboratoryRecords?.find(l => l.patient_id === patient.id);
    const pharmacy = pharmacyRecords?.find(p => p.patient_id === patient.id);

    return {
      ...patient,
      workflow,
      consultation,
      laboratory,
      pharmacy,
      workflowStatus: workflow?.current_department || 'registered'
    };
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'scheduled':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'in-progress':
        return 'bg-yellow-100 text-yellow-800';
      case 'confirmed':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getWorkflowStatusBadge = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'reception':
        return { color: 'bg-blue-100 text-blue-800', icon: User, label: 'Reception' };
      case 'consultation':
        return { color: 'bg-orange-100 text-orange-800', icon: Stethoscope, label: 'Consultation' };
      case 'laboratory':
        return { color: 'bg-purple-100 text-purple-800', icon: TestTube, label: 'Laboratory' };
      case 'pharmacy':
        return { color: 'bg-green-100 text-green-800', icon: Pill, label: 'Pharmacy' };
      case 'completed':
        return { color: 'bg-emerald-100 text-emerald-800', icon: CheckCircle, label: 'Completed' };
      default:
        return { color: 'bg-gray-100 text-gray-800', icon: AlertCircle, label: 'Registered' };
    }
  };

  const formatAppointmentDate = (dateTime: string) => {
    const date = new Date(dateTime);
    if (isToday(date)) return 'Today';
    if (isTomorrow(date)) return 'Tomorrow';
    if (isYesterday(date)) return 'Yesterday';
    return format(date, 'MMM dd, yyyy');
  };

  // Get appointment statistics
  const getAppointmentStats = () => {
    const total = appointments?.length || 0;
    const today = appointments?.filter(apt => isToday(new Date(apt.date_time))).length || 0;
    const scheduled = appointments?.filter(apt => apt.status === 'scheduled').length || 0;
    const completed = appointments?.filter(apt => apt.status === 'completed').length || 0;
    const cancelled = appointments?.filter(apt => apt.status === 'cancelled').length || 0;

    return { total, today, scheduled, completed, cancelled };
  };

  const stats = getAppointmentStats();

  // Function to handle button click and trigger form submission
  const handleScheduleAppointment = () => {
    formRef.current?.submitForm();
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg text-gray-600">Loading appointments...</div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-8">
        {/* Enhanced Header */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex items-center space-x-4">
              <div className="bg-blue-600 p-3 rounded-lg">
                <CalendarDays className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Appointments</h1>
                <p className="text-gray-600 mt-1 text-sm sm:text-base flex items-center">
                  <Activity className="h-4 w-4 mr-2" />
                  Manage patient appointments and schedules with workflow integration
                </p>
              </div>
            </div>
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl transition-all duration-200">
                  <Plus className="mr-2 h-4 w-4" />
                  New Appointment
                </Button>
              </DialogTrigger>
            <DialogContent className="sm:max-w-[800px] flex flex-col max-h-[95vh] overflow-hidden p-0">
              <DialogHeader className="px-6 pt-6 pb-4 flex-shrink-0">
                <DialogTitle>Schedule New Appointment</DialogTitle>
              </DialogHeader>
              <div className="flex-1 overflow-y-auto px-6 pb-6">
                <AppointmentForm
                  ref={formRef}
                  onClose={() => setIsDialogOpen(false)}
                  onSuccess={() => setIsDialogOpen(false)}
                />
              </div>
              <div className="flex justify-end gap-2 px-6 py-4 border-t border-gray-200 flex-shrink-0">
                <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button 
                  type="button"
                  className="bg-purple-600 hover:bg-purple-700 text-white"
                  onClick={handleScheduleAppointment}
                  disabled={formRef.current?.isPending}
                >
                  {formRef.current?.isPending ? 'Scheduling...' : 'Schedule Appointment'}
                </Button>
              </div>
            </DialogContent>
          </Dialog>

          {/* Enhanced Stats Row */}
          <div className="mt-6 grid grid-cols-2 lg:grid-cols-5 gap-4">
            <div className="bg-white rounded-lg p-4 border border-blue-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total</p>
                  <p className="text-2xl font-bold text-blue-600">{stats.total}</p>
                </div>
                <Users className="h-8 w-8 text-blue-500" />
              </div>
            </div>
            <div className="bg-white rounded-lg p-4 border border-green-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Today</p>
                  <p className="text-2xl font-bold text-green-600">{stats.today}</p>
                </div>
                <Calendar className="h-8 w-8 text-green-500" />
              </div>
            </div>
            <div className="bg-white rounded-lg p-4 border border-orange-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Scheduled</p>
                  <p className="text-2xl font-bold text-orange-600">{stats.scheduled}</p>
                </div>
                <Clock className="h-8 w-8 text-orange-500" />
              </div>
            </div>
            <div className="bg-white rounded-lg p-4 border border-emerald-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Completed</p>
                  <p className="text-2xl font-bold text-emerald-600">{stats.completed}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-emerald-500" />
              </div>
            </div>
            <div className="bg-white rounded-lg p-4 border border-red-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Cancelled</p>
                  <p className="text-2xl font-bold text-red-600">{stats.cancelled}</p>
                </div>
                <AlertCircle className="h-8 w-8 text-red-500" />
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Search and Filters */}
        <Card className="p-6 bg-white shadow-sm border border-gray-200">
          <div className="flex flex-col sm:flex-row gap-4 sm:gap-6">
            <div className="flex-1 relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
              <Input
                type="text"
                placeholder="Search by patient name, doctor, or department..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-12 pr-4 py-3 text-sm sm:text-base border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
              />
            </div>
            <div className="flex gap-3">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-4 py-3 text-sm border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
              >
                <option value="all">All Status</option>
                <option value="scheduled">Scheduled</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
                <option value="in-progress">In Progress</option>
              </select>
              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value)}
                className="px-4 py-3 text-sm border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
              >
                <option value="all">All Types</option>
                <option value="Consultation">Consultation</option>
                <option value="Follow-up">Follow-up</option>
                <option value="Check-up">Check-up</option>
                <option value="Emergency">Emergency</option>
              </select>
              <div className="flex items-center text-sm text-gray-600 bg-gray-50 px-4 py-3 rounded-xl">
                <span className="font-medium">{filteredAppointments.length}</span>
                <span className="ml-1">appointments</span>
              </div>
            </div>
          </div>
        </Card>

        {/* Enhanced Appointments List */}
        <Card className="bg-white shadow-sm border border-gray-200">
          <CardHeader className="bg-gray-50 border-b border-gray-200">
            <CardTitle className="flex items-center gap-2 text-lg font-semibold">
              <Calendar className="h-5 w-5 text-blue-600" />
              Appointments ({filteredAppointments.length})
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            {isLoading ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="text-gray-500 mt-2">Loading appointments...</p>
              </div>
            ) : filteredAppointments.length === 0 ? (
              <div className="text-center py-12">
                <div className="bg-blue-100 rounded-full p-4 w-16 h-16 mx-auto mb-4">
                  <Calendar className="h-8 w-8 text-blue-500 mx-auto" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">No appointments found</h3>
                <p className="text-gray-500 mb-6">
                  {searchTerm || statusFilter !== 'all' || typeFilter !== 'all'
                    ? 'Try adjusting your search or filter criteria.'
                    : 'Create your first appointment to get started.'}
                </p>
                <Button onClick={() => setIsDialogOpen(true)} className="bg-blue-600 hover:bg-blue-700">
                  <Plus className="mr-2 h-4 w-4" />
                  Create Appointment
                </Button>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-gray-50">
                      <TableHead className="font-semibold">Patient</TableHead>
                      <TableHead className="font-semibold">Doctor</TableHead>
                      <TableHead className="font-semibold">Department</TableHead>
                      <TableHead className="font-semibold">Date & Time</TableHead>
                      <TableHead className="font-semibold">Type</TableHead>
                      <TableHead className="font-semibold">Status</TableHead>
                      <TableHead className="font-semibold">Workflow</TableHead>
                      <TableHead className="font-semibold">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredAppointments.map((appointment) => {
                      const patientDetails = getPatientDetails(appointment.patient_name);
                      const workflowStatus = getWorkflowStatusBadge(patientDetails?.workflowStatus || 'registered');
                      const WorkflowIcon = workflowStatus.icon;

                      return (
                        <TableRow key={appointment.id} className="hover:bg-gray-50 transition-colors duration-150">
                          <TableCell>
                            <div className="flex items-center space-x-3">
                              <Avatar className="h-10 w-10">
                                <AvatarImage src="" alt={appointment.patient_name} />
                                <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white font-semibold">
                                  {appointment.patient_name?.split(' ').map((n: string) => n[0]).join('') || 'P'}
                                </AvatarFallback>
                              </Avatar>
                              <div>
                                <div className="font-semibold text-gray-900">{appointment.patient_name}</div>
                                {patientDetails && (
                                  <div className="text-sm text-gray-500">{patientDetails.email || 'No email'}</div>
                                )}
                                {patientDetails && (
                                  <div className="text-xs text-gray-400">ID: {patientDetails.id.slice(0, 8)}...</div>
                                )}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Stethoscope className="h-4 w-4 text-green-500" />
                              <div className="font-medium">
                                {appointment.doctor?.includes('(') ? (
                                  <div>
                                    <div>{appointment.doctor.split('(')[0].trim()}</div>
                                    <div className="text-sm text-gray-500">
                                      {appointment.doctor.match(/\((.*?)\)/)?.[1]}
                                    </div>
                                  </div>
                                ) : (
                                  appointment.doctor
                                )}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Building2 className="h-4 w-4 text-purple-500" />
                              <span className="font-medium">{appointment.department || <span className="text-gray-500 italic">No department</span>}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Calendar className="h-4 w-4 text-blue-500" />
                              <div>
                                <div className="font-medium text-gray-900">
                                  {formatAppointmentDate(appointment.date_time)}
                                </div>
                                <div className="text-sm text-gray-500 flex items-center">
                                  <Clock className="h-3 w-3 mr-1" />
                                  {format(new Date(appointment.date_time), 'h:mm a')}
                                </div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline" className="font-medium">
                              {appointment.type || 'Consultation'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge className={`${getStatusBadgeColor(appointment.status)} font-medium`}>
                              {appointment.status}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge className={`${workflowStatus.color} font-medium flex items-center gap-1`}>
                              <WorkflowIcon className="h-3 w-3" />
                              {workflowStatus.label}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Button variant="ghost" size="sm" className="hover:bg-blue-50 hover:text-blue-600">
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="sm" className="hover:bg-green-50 hover:text-green-600">
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="sm" className="hover:bg-red-50 hover:text-red-600">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default Appointments;
