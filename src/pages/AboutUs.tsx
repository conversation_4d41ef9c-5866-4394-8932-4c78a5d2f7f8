import React from 'react';
import { Layout } from '../components/Layout';
import { But<PERSON> } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { Users, Heart, Globe, Award, Sparkles, CheckCircle } from 'lucide-react';
import { CTASection } from '../components/landing/CTASection';

const AboutUs = () => {
  const coreValues = [
    {
      icon: Heart,
      title: 'Patient-Centered',
      description: 'We design our solutions with patients at the center of everything we do.'
    },
    {
      icon: Globe,
      title: 'Accessibility',
      description: 'Making healthcare management accessible to facilities of all sizes.'
    },
    {
      icon: Award,
      title: 'Excellence',
      description: 'Committed to the highest standards in healthcare technology.'
    },
    {
      icon: Sparkles,
      title: 'Innovation',
      description: 'Constantly evolving our platform with cutting-edge solutions.'
    }
  ];

  const teamMembers = [
    {
      name: 'Dr. <PERSON>',
      role: 'Chief Medical Officer',
      image: 'https://images.unsplash.com/photo-**********-2b71ea197ec2?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80'
    },
    {
      name: '<PERSON>',
      role: 'Lead Developer',
      image: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80'
    },
    {
      name: 'Amara Okafor',
      role: 'UX Designer',
      image: 'https://images.unsplash.com/photo-1573497019940-1c28c88b4f3e?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80'
    },
    {
      name: 'James Wilson',
      role: 'Healthcare Consultant',
      image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80'
    }
  ];

  return (
    <Layout>
      {/* Hero Section with Gradient Background */}
      <div className="bg-gradient-to-b from-purple-50 to-white py-20">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto">
            <div className="inline-block bg-purple-100 text-purple-800 px-4 py-1 rounded-full text-sm font-medium mb-6">About VertiQ</div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">Transforming Healthcare Management</h1>
            <p className="text-xl text-gray-600 mb-8">Your trusted partner in modern healthcare technology solutions.</p>
            <div className="flex flex-wrap justify-center gap-4">
              <Link to="/contact-us">
                <Button className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2">Get in Touch</Button>
              </Link>
              <Link to="/features">
                <Button variant="outline" className="border-purple-200 text-purple-700 hover:bg-purple-50 px-6 py-2">Explore Features</Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Our Story Section */}
      <div className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto">
            <div className="flex items-center justify-center mb-8">
              <div className="h-0.5 w-12 bg-purple-300 mr-4"></div>
              <h2 className="text-3xl font-bold text-gray-900">Our Story</h2>
              <div className="h-0.5 w-12 bg-purple-300 ml-4"></div>
            </div>
            
            <div className="prose prose-lg mx-auto text-gray-600">
              <p>
                VertiQ was founded with the vision of transforming healthcare operations through innovative technology.
                We recognized the challenges faced by hospitals and clinics in managing complex workflows, patient data,
                and administrative tasks. Our goal is to provide a comprehensive, intuitive, and secure virtual hospital
                management system that empowers healthcare providers to focus on what matters most: patient care.
              </p>
              <p>
                Since our inception, we have been dedicated to building a platform that is not only powerful and efficient
                but also adaptable to the unique needs of various healthcare settings, from small clinics to large hospitals.
                We believe that by streamlining operations and improving data accessibility, we can contribute to better
                patient outcomes and a more sustainable healthcare ecosystem.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Mission and Vision Section with Cards */}
      <div className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
            <div className="bg-white rounded-xl shadow-md p-8 transform transition-transform hover:scale-105">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-6">
                <CheckCircle className="h-6 w-6 text-purple-600" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Our Mission</h2>
              <p className="text-gray-600">
                To empower healthcare providers with a seamless and intelligent virtual management system that enhances
                operational efficiency, improves patient engagement, and elevates the quality of care delivered.
              </p>
            </div>
            
            <div className="bg-white rounded-xl shadow-md p-8 transform transition-transform hover:scale-105">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-6">
                <Globe className="h-6 w-6 text-blue-600" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Our Vision</h2>
              <p className="text-gray-600">
                To be the leading global provider of virtual hospital management solutions, recognized for our innovation,
                reliability, and commitment to advancing healthcare technology and patient well-being.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Core Values Section */}
      <div className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Core Values</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">The principles that guide everything we do</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
            {coreValues.map((value, index) => (
              <div key={index} className="bg-gray-50 rounded-xl p-6 text-center transform transition-transform hover:scale-105">
                <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-6">
                  <value.icon className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">{value.title}</h3>
                <p className="text-gray-600">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <CTASection />
    </Layout>
  );
};

export default AboutUs; 