import React from 'react';
import { Layout } from '../components/Layout';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent } from '@/components/ui/card';
import { SettingsFormGeneral } from '@/components/SettingsFormGeneral';
import { SettingsFormBusiness } from '@/components/SettingsFormBusiness';
import { SettingsFormModules } from '@/components/SettingsFormModules';
import { SettingsFormNotifications } from '@/components/SettingsFormNotifications';

const Settings = () => {
  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
            <p className="text-gray-600 mt-1">Manage application settings</p>
          </div>
          {/* Add settings actions here later if needed */}
        </div>

        {/* Settings Content */}
        <Tabs defaultValue="general" className="w-full">
          <TabsList className="grid w-full max-w-2xl grid-cols-2 md:grid-cols-4">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="business">Business</TabsTrigger>
            <TabsTrigger value="modules">Modules</TabsTrigger>
            <TabsTrigger value="notifications">Notifications</TabsTrigger>
          </TabsList>

          <TabsContent value="general" className="mt-6">
            <Card>
              <CardContent className="p-6">
                <SettingsFormGeneral />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="business" className="mt-6">
            <Card>
              <CardContent className="p-6">
                <SettingsFormBusiness />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="modules" className="mt-6">
            <Card>
              <CardContent className="p-6">
                <SettingsFormModules />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="notifications" className="mt-6">
            <Card>
              <CardContent className="p-6">
                <SettingsFormNotifications />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </Layout>
  );
};

export default Settings; 