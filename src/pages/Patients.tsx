import React, { useState } from 'react';
import { Layout } from '../components/Layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';

import {
  Users,
  Search,
  User,
  Phone,
  Mail,
  MapPin,
  Activity,
  TestTube,
  Pill,
  FileText,
  CheckCircle,
  Stethoscope,
  Download,
  Printer,
  Eye
} from 'lucide-react';
import { useWorkflowData } from '@/hooks/useWorkflowData';
import { useSupabaseData } from '@/hooks/useSupabaseData';
import { useNavigate } from 'react-router-dom';
import { downloadPatientPDF, printPatientRecord } from '@/utils/pdfGenerator';
import { PrescribedMedicinesList } from '@/components/PrescribedMedicinesList';
import { toast } from '@/hooks/use-toast';

const Patients = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPatient, setSelectedPatient] = useState<any>(null);
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const [statusFilter, setStatusFilter] = useState('all');
  const [departmentFilter, setDepartmentFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState('all');
  const [customDateFrom, setCustomDateFrom] = useState('');
  const [customDateTo, setCustomDateTo] = useState('');
  
  const { 
    useConsultationRecords,
    useLaboratoryRecords,
    usePharmacyRecords
  } = useWorkflowData();
  
  const { usePatients, useSettings } = useSupabaseData();

  const { data: allPatients = [], isLoading: patientsLoading } = usePatients();
  const { data: settings } = useSettings();
  const { data: consultationRecords = [] } = useConsultationRecords();
  const { data: laboratoryRecords = [] } = useLaboratoryRecords();
  const { data: pharmacyRecords = [] } = usePharmacyRecords();

  // Debug logging
  console.log('All Patients:', allPatients);
  console.log('Consultation Records:', consultationRecords);
  console.log('Laboratory Records:', laboratoryRecords);
  console.log('Pharmacy Records:', pharmacyRecords);

  // Helper function to get department information
  const getDepartmentInfo = (patient: any) => {
    if (!patient) return null;
    // Handle both flat structure (from reception function) and nested structure (from regular patients query)
    if (patient.department_name) {
      return {
        name: patient.department_name,
        color: patient.department_color,
        icon: patient.department_icon,
        notes: patient.department_notes,
        description: patient.department_description
      };
    } else if (patient.medical_departments) {
      return {
        name: patient.medical_departments.name,
        color: patient.medical_departments.color,
        icon: patient.medical_departments.icon,
        notes: patient.department_notes,
        description: patient.medical_departments.description
      };
    }
    return null;
  };

  // Get patients who have completed the workflow (consultation + laboratory)
  // Workflow now ends at laboratory, pharmacy is independent
  const completedWorkflowPatients = allPatients.filter(patient => {
    const hasConsultation = consultationRecords.some(record => record.patient_id === patient.id);
    const hasLaboratory = laboratoryRecords.some(record => record.patient_id === patient.id);
    return hasConsultation && hasLaboratory;
  });

  // Get all patients with any medical records (for comprehensive patient records)
  const allPatientsWithRecords = allPatients.filter(patient => {
    const hasConsultation = consultationRecords.some(record => record.patient_id === patient.id);
    const hasLaboratory = laboratoryRecords.some(record => record.patient_id === patient.id);
    const hasPharmacy = pharmacyRecords.some(record => record.patient_id === patient.id);
    return hasConsultation || hasLaboratory || hasPharmacy;
  });

  // Enhanced filtering with multiple criteria
  const filteredPatientRecords = allPatientsWithRecords.filter(patient => {
    // Search term filter
    const matchesSearch = patient.patient_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      patient.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      patient.phone_number.includes(searchTerm);

    // Status filter
    let matchesStatus = true;
    if (statusFilter !== 'all') {
      const hasConsultation = consultationRecords.some(record => record.patient_id === patient.id);
      const hasLaboratory = laboratoryRecords.some(record => record.patient_id === patient.id);
      const hasPharmacy = pharmacyRecords.some(record => record.patient_id === patient.id);

      switch (statusFilter) {
        case 'complete':
          matchesStatus = hasConsultation && hasLaboratory;
          break;
        case 'consultation_only':
          matchesStatus = hasConsultation && !hasLaboratory;
          break;
        case 'laboratory_only':
          matchesStatus = hasLaboratory && !hasConsultation;
          break;
        case 'with_pharmacy':
          matchesStatus = hasPharmacy;
          break;
        case 'no_records':
          matchesStatus = !hasConsultation && !hasLaboratory && !hasPharmacy;
          break;
        default:
          matchesStatus = true;
      }
    }

    // Department filter (based on consultation records)
    let matchesDepartment = true;
    if (departmentFilter !== 'all') {
      const consultationRecord = consultationRecords.find(record => record.patient_id === patient.id);
      if (consultationRecord) {
        matchesDepartment = consultationRecord.department === departmentFilter;
      } else {
        matchesDepartment = false; // No consultation record means no department
      }
    }

    // Date filter (based on registration date)
    let matchesDate = true;
    if (dateFilter !== 'all') {
      const registrationDate = new Date(patient.registration_date || patient.created_at);
      const today = new Date();

      switch (dateFilter) {
        case 'today':
          matchesDate = registrationDate.toDateString() === today.toDateString();
          break;
        case 'this_week':
          const weekAgo = new Date(today);
          weekAgo.setDate(today.getDate() - 7);
          matchesDate = registrationDate >= weekAgo && registrationDate <= today;
          break;
        case 'this_month':
          const monthAgo = new Date(today);
          monthAgo.setMonth(today.getMonth() - 1);
          matchesDate = registrationDate >= monthAgo && registrationDate <= today;
          break;
        case 'custom':
          if (customDateFrom && customDateTo) {
            const fromDate = new Date(customDateFrom);
            const toDate = new Date(customDateTo);
            toDate.setHours(23, 59, 59, 999);
            matchesDate = registrationDate >= fromDate && registrationDate <= toDate;
          } else if (customDateFrom) {
            const fromDate = new Date(customDateFrom);
            matchesDate = registrationDate >= fromDate;
          } else if (customDateTo) {
            const toDate = new Date(customDateTo);
            toDate.setHours(23, 59, 59, 999);
            matchesDate = registrationDate <= toDate;
          }
          break;
        default:
          matchesDate = true;
      }
    }

    return matchesSearch && matchesStatus && matchesDepartment && matchesDate;
  });

  const handleViewDetails = (patient: any) => {
    setSelectedPatient(patient);
    setIsDetailsOpen(true);
  };

  const handleDownloadPDF = async (patient: any) => {
    try {
      const records = getPatientRecords(patient);
      const patientRecord = {
        patient,
        consultation: records.consultation,
        laboratory: records.laboratory,
        pharmacy: records.pharmacy,
        hospitalName: settings?.hospital_name || 'Healthcare Management System',
        hospitalLogo: settings?.logo_url,
        settings: settings
      };

      await downloadPatientPDF(patientRecord);

      toast({
        title: "PDF Downloaded",
        description: `Medical record for ${patient.patient_name} has been downloaded.`,
      });
    } catch (error) {
      console.error('Error downloading PDF:', error);
      toast({
        title: "Download Failed",
        description: "Failed to download PDF. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handlePrintRecord = async (patient: any) => {
    try {
      const records = getPatientRecords(patient);
      const patientRecord = {
        patient,
        consultation: records.consultation,
        laboratory: records.laboratory,
        pharmacy: records.pharmacy,
        hospitalName: settings?.hospital_name || 'Healthcare Management System',
        hospitalLogo: settings?.logo_url,
        settings: settings
      };

      await printPatientRecord(patientRecord);

      toast({
        title: "Print Initiated",
        description: `Medical record for ${patient.patient_name} is ready for printing.`,
      });
    } catch (error) {
      console.error('Error printing record:', error);
      toast({
        title: "Print Failed",
        description: "Failed to print record. Please try again.",
        variant: "destructive"
      });
    }
  };

  const getPatientAge = (dateOfBirth: string) => {
    if (!dateOfBirth) return 'Unknown';
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  const getPatientProgress = (patient: any) => {
    const hasConsultation = consultationRecords.some(record => record.patient_id === patient.id);
    const hasLaboratory = laboratoryRecords.some(record => record.patient_id === patient.id);
    const hasPharmacy = pharmacyRecords.some(record => record.patient_id === patient.id);
    
    const steps = [
      { name: 'Consultation', completed: hasConsultation },
      { name: 'Laboratory', completed: hasLaboratory },
      { name: 'Pharmacy', completed: hasPharmacy }
    ];
    
    const completedSteps = steps.filter(step => step.completed).length;
    return { steps, completedSteps, total: 3 };
  };

  const getPatientRecords = (patient: any) => {
    return {
      consultation: consultationRecords.find(record => record.patient_id === patient.id),
      laboratory: laboratoryRecords.find(record => record.patient_id === patient.id),
      pharmacy: pharmacyRecords.find(record => record.patient_id === patient.id)
    };
  };

  // Render debug info
  console.log('Rendering patients page with:', {
    allPatients: allPatients.length,
    completedWorkflowPatients: completedWorkflowPatients.length,
    allPatientsWithRecords: allPatientsWithRecords.length,
    patientsLoading
  });

  return (
    <Layout>
      <div className="space-y-4 sm:space-y-6 p-2 sm:p-0">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Medical Records</h1>
            <p className="text-gray-600 mt-1 text-sm sm:text-base">Complete patient medical records with PDF download & print functionality</p>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
            <Button
              variant="outline"
              onClick={() => navigate('/reception')}
              className="w-full sm:w-auto text-sm"
            >
              Add New Patient
            </Button>
            <Button
              variant="outline"
              onClick={() => navigate('/workflow')}
              className="w-full sm:w-auto text-sm"
            >
              View Workflow Dashboard
            </Button>
          </div>
        </div>

        {/* PDF Features Info */}
        <Card className="bg-gradient-to-r from-blue-50 to-green-50 border-blue-200">
          <CardContent className="p-3 sm:p-4">
            <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4">
              <div className="flex items-center space-x-2">
                <Download className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600" />
                <span className="text-xs sm:text-sm font-medium text-blue-900">Download PDF</span>
              </div>
              <div className="flex items-center space-x-2">
                <Printer className="h-4 w-4 sm:h-5 sm:w-5 text-green-600" />
                <span className="text-xs sm:text-sm font-medium text-green-900">Print Records</span>
              </div>
              <div className="flex-1">
                <p className="text-xs sm:text-sm text-gray-700">
                  Complete patient medical records can now be downloaded as PDF or printed directly from this page.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Search Bar */}
        {/* Enhanced Search and Filters */}
        <Card className="p-6 bg-white shadow-sm border border-gray-200">
          <div className="space-y-4">
            {/* Search Bar */}
            <div className="flex flex-col sm:flex-row gap-4 sm:gap-6">
              <div className="flex-1 relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                <Input
                  type="text"
                  placeholder="Search by patient name, email, or phone number..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-12 pr-4 py-3 text-sm sm:text-base border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                />
              </div>
              <div className="flex items-center text-sm text-gray-600 bg-gray-50 px-4 py-3 rounded-xl">
                <span className="font-medium">{filteredPatientRecords.length}</span>
                <span className="ml-1">patients</span>
              </div>
            </div>

            {/* Filter Controls */}
            <div className="flex flex-wrap gap-3">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-4 py-3 text-sm border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
              >
                <option value="all">All Status</option>
                <option value="complete">Complete Workflow</option>
                <option value="consultation_only">Consultation Only</option>
                <option value="laboratory_only">Laboratory Only</option>
                <option value="with_pharmacy">With Pharmacy</option>
                <option value="no_records">No Records</option>
              </select>

              <select
                value={departmentFilter}
                onChange={(e) => setDepartmentFilter(e.target.value)}
                className="px-4 py-3 text-sm border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
              >
                <option value="all">All Departments</option>
                <option value="General Medicine">General Medicine</option>
                <option value="Cardiology">Cardiology</option>
                <option value="Pediatrics">Pediatrics</option>
                <option value="Orthopedics">Orthopedics</option>
                <option value="Dermatology">Dermatology</option>
                <option value="Neurology">Neurology</option>
                <option value="Emergency">Emergency</option>
              </select>

              <select
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value)}
                className="px-4 py-3 text-sm border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
              >
                <option value="all">All Dates</option>
                <option value="today">Today</option>
                <option value="this_week">This Week</option>
                <option value="this_month">This Month</option>
                <option value="custom">Custom Range</option>
              </select>
            </div>

            {/* Custom Date Range */}
            {dateFilter === 'custom' && (
              <div className="flex flex-col sm:flex-row gap-4 p-4 bg-blue-50 rounded-xl border border-blue-200">
                <div className="flex-1">
                  <label className="block text-sm font-medium text-blue-800 mb-2">From Date</label>
                  <Input
                    type="date"
                    value={customDateFrom}
                    onChange={(e) => setCustomDateFrom(e.target.value)}
                    className="border-blue-300 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div className="flex-1">
                  <label className="block text-sm font-medium text-blue-800 mb-2">To Date</label>
                  <Input
                    type="date"
                    value={customDateTo}
                    onChange={(e) => setCustomDateTo(e.target.value)}
                    className="border-blue-300 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div className="flex items-end">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setCustomDateFrom('');
                      setCustomDateTo('');
                    }}
                    className="border-blue-300 text-blue-700 hover:bg-blue-100"
                  >
                    Clear
                  </Button>
                </div>
              </div>
            )}
          </div>
        </Card>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
          <Card>
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg mr-3 sm:mr-4">
                  <CheckCircle className="h-5 w-5 sm:h-6 sm:w-6 text-green-600" />
                </div>
                <div>
                  <p className="text-xl sm:text-2xl font-bold text-gray-900">{completedWorkflowPatients.length}</p>
                  <p className="text-xs sm:text-sm text-gray-600">Completed Workflow</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center">
                <div className="p-2 bg-orange-100 rounded-lg mr-3 sm:mr-4">
                  <FileText className="h-5 w-5 sm:h-6 sm:w-6 text-orange-600" />
                </div>
                <div>
                  <p className="text-xl sm:text-2xl font-bold text-gray-900">{allPatientsWithRecords.length}</p>
                  <p className="text-xs sm:text-sm text-gray-600">Patients with Records</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg mr-3 sm:mr-4">
                  <Users className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
                </div>
                <div>
                  <p className="text-xl sm:text-2xl font-bold text-gray-900">{allPatients.length}</p>
                  <p className="text-xs sm:text-sm text-gray-600">Total Patients</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg mr-3 sm:mr-4">
                  <Activity className="h-5 w-5 sm:h-6 sm:w-6 text-purple-600" />
                </div>
                <div>
                  <p className="text-xl sm:text-2xl font-bold text-gray-900">
                    {consultationRecords.length + laboratoryRecords.length + pharmacyRecords.length}
                  </p>
                  <p className="text-sm text-gray-600">Total Medical Records</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Patient Records */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="mr-2 h-5 w-5 text-blue-600" />
              Patient Medical Records ({filteredPatientRecords.length})
            </CardTitle>
            <CardDescription>
              Complete medical records for all patients who have completed workflow or have medical history
            </CardDescription>
          </CardHeader>

          <CardContent>
            {patientsLoading ? (
              <div className="text-center py-8">
                <div className="text-gray-500">Loading patients...</div>
              </div>
            ) : filteredPatientRecords.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <p className="text-gray-500">
                  {searchTerm ? 'No patient records found matching your search' : 'No patient records available yet'}
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredPatientRecords.map((patient) => {
                  const consultationRecord = consultationRecords.find(record => record.patient_id === patient.id);
                  const laboratoryRecord = laboratoryRecords.find(record => record.patient_id === patient.id);
                  const pharmacyRecord = pharmacyRecords.find(record => record.patient_id === patient.id);

                  // Determine workflow status
                  const hasConsultation = !!consultationRecord;
                  const hasLaboratory = !!laboratoryRecord;
                  const isWorkflowComplete = hasConsultation && hasLaboratory;

                  return (
                    <div key={patient.id} className={`p-3 sm:p-4 border rounded-lg hover:shadow-md transition-shadow ${
                      isWorkflowComplete ? 'border-green-200 bg-green-50' : 'border-blue-200 bg-blue-50'
                    }`}>
                      <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-3 sm:gap-0">
                        <div className="flex-1">
                          <div className="flex items-center mb-3">
                            <div className={`p-1 rounded mr-3 ${
                              isWorkflowComplete ? 'bg-green-100' : 'bg-blue-100'
                            }`}>
                              <User className={`h-4 w-4 ${
                                isWorkflowComplete ? 'text-green-600' : 'text-blue-600'
                              }`} />
                            </div>
                            <div>
                              <h3 className="text-base sm:text-lg font-semibold text-gray-900">{patient.patient_name}</h3>
                              <p className="text-xs sm:text-sm text-gray-600">
                                Age: {getPatientAge(patient.date_of_birth)} years •
                                Blood Type: {patient.blood_type || 'Unknown'} •
                                Gender: {patient.gender}
                              </p>
                            </div>
                          </div>

                          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 mb-3">
                            <div className="flex items-center text-sm text-gray-600">
                              <Phone className="h-3 w-3 mr-2" />
                              {patient.phone_number}
                            </div>
                            <div className="flex items-center text-sm text-gray-600">
                              <Mail className="h-3 w-3 mr-2" />
                              {patient.email}
                            </div>
                            <div className="flex items-center text-sm text-gray-600">
                              <MapPin className="h-3 w-3 mr-2" />
                              {patient.address}
                            </div>
                          </div>

                          {/* Department Information */}
                          {(() => {
                            const deptInfo = getDepartmentInfo(patient);
                            return deptInfo && (
                              <div className="mb-3 p-2 bg-blue-50 border border-blue-200 rounded-lg">
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center text-sm font-medium text-blue-900">
                                    <Stethoscope className="h-4 w-4 mr-2 text-blue-600" />
                                    <span>Department:</span>
                                  </div>
                                  <Badge
                                    className="text-xs text-white font-medium"
                                    style={{ backgroundColor: deptInfo.color || '#3B82F6' }}
                                  >
                                    {deptInfo.name}
                                  </Badge>
                                </div>
                                {deptInfo.notes && (
                                  <p className="text-xs text-blue-700 mt-1">
                                    Notes: {deptInfo.notes}
                                  </p>
                                )}
                              </div>
                            );
                          })()}

                          <div className="flex flex-wrap items-center gap-2 mb-3">
                            {consultationRecord && (
                              <Badge className="bg-blue-100 text-blue-800 text-xs">
                                <Stethoscope className="h-3 w-3 mr-1" />
                                Consultation
                              </Badge>
                            )}
                            {laboratoryRecord && (
                              <Badge className="bg-purple-100 text-purple-800 text-xs">
                                <TestTube className="h-3 w-3 mr-1" />
                                Laboratory
                              </Badge>
                            )}
                            {pharmacyRecord && (
                              <Badge className="bg-orange-100 text-orange-800 text-xs">
                                <Pill className="h-3 w-3 mr-1" />
                                Pharmacy Records
                              </Badge>
                            )}
                          </div>

                          <div className="text-sm text-gray-600">
                            <p>
                              <strong>Last Visit:</strong> {patient.last_visit ? new Date(patient.last_visit).toLocaleDateString() : 'First visit'}
                            </p>
                            {consultationRecord && (
                              <p>
                                <strong>Diagnosis:</strong> {consultationRecord.diagnosis || 'Not specified'}
                              </p>
                            )}
                            {pharmacyRecord && (
                              <p>
                                <strong>Pharmacy Cost:</strong> ${pharmacyRecord.total_cost?.toFixed(2) || '0.00'}
                              </p>
                            )}
                          </div>
                        </div>

                        <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 sm:ml-4">
                          <Badge className={`text-xs ${isWorkflowComplete ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'}`}>
                            {isWorkflowComplete ? 'Workflow Complete' : 'Has Medical Records'}
                          </Badge>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              setSelectedPatient(patient);
                              setIsDetailsOpen(true);
                            }}
                            className="w-full sm:w-auto text-xs"
                          >
                            <Eye className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                            View Details
                          </Button>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </CardContent>
        </Card>


        {/* Patient Details Dialog */}
        <Dialog open={isDetailsOpen} onOpenChange={setIsDetailsOpen}>
          <DialogContent className="w-[95vw] max-w-[900px] max-h-[90vh] overflow-y-auto mx-2 sm:mx-auto">
            {selectedPatient && (
              <>
                <DialogHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <DialogTitle>
                        Patient Details - {selectedPatient.patient_name}
                      </DialogTitle>
                      <DialogDescription>
                        Complete workflow information and records
                      </DialogDescription>
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        onClick={() => handleDownloadPDF(selectedPatient)}
                        className="bg-blue-50 hover:bg-blue-100"
                      >
                        <Download className="h-4 w-4 mr-2" />
                        Download PDF
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => handlePrintRecord(selectedPatient)}
                        className="bg-green-50 hover:bg-green-100"
                      >
                        <Printer className="h-4 w-4 mr-2" />
                        Print Record
                      </Button>
                    </div>
                  </div>
                </DialogHeader>
                <div className="space-y-6">
                  {/* Patient Registration Information */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg flex items-center">
                        <User className="h-5 w-5 mr-2 text-blue-600" />
                        Patient Registration Information
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <p className="text-sm font-medium text-gray-500">Full Name</p>
                          <p className="text-gray-900 font-medium">{selectedPatient.patient_name}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Date of Birth</p>
                          <p className="text-gray-900">{new Date(selectedPatient.date_of_birth).toLocaleDateString()}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Age</p>
                          <p className="text-gray-900">{getPatientAge(selectedPatient.date_of_birth)} years</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Gender</p>
                          <p className="text-gray-900">{selectedPatient.gender || 'Not specified'}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Blood Type</p>
                          <p className="text-gray-900">{selectedPatient.blood_type || 'Unknown'}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Registration Date</p>
                          <p className="text-gray-900">{selectedPatient.registration_date ? new Date(selectedPatient.registration_date).toLocaleDateString() : 'Not available'}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Phone Number</p>
                          <p className="text-gray-900">{selectedPatient.phone_number}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Email Address</p>
                          <p className="text-gray-900">{selectedPatient.email}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Address</p>
                          <p className="text-gray-900">{selectedPatient.address || 'Not provided'}</p>
                        </div>
                        {selectedPatient.insurance && (
                          <div>
                            <p className="text-sm font-medium text-gray-500">Insurance</p>
                            <p className="text-gray-900">{selectedPatient.insurance}</p>
                          </div>
                        )}
                        {selectedPatient.emergency_contact && (
                          <div>
                            <p className="text-sm font-medium text-gray-500">Emergency Contact</p>
                            <p className="text-gray-900">{selectedPatient.emergency_contact}</p>
                          </div>
                        )}
                        {selectedPatient.notes && (
                          <div className="md:col-span-3">
                            <p className="text-sm font-medium text-gray-500">Registration Notes</p>
                            <p className="text-gray-900">{selectedPatient.notes}</p>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Consultation Records */}
                  {(() => {
                    const consultationRecord = consultationRecords.find(record => record.patient_id === selectedPatient.id);
                    return consultationRecord ? (
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-lg flex items-center">
                            <Stethoscope className="h-5 w-5 mr-2 text-blue-600" />
                            Consultation Record
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <p className="text-sm font-medium text-gray-500">Doctor Name</p>
                              <p className="text-gray-900 font-medium">{consultationRecord.doctor_name}</p>
                            </div>
                            <div>
                              <p className="text-sm font-medium text-gray-500">Consultation Date</p>
                              <p className="text-gray-900">{consultationRecord.consultation_date ? new Date(consultationRecord.consultation_date).toLocaleDateString() : 'Not specified'}</p>
                            </div>
                            {consultationRecord.symptoms && (
                              <div className="md:col-span-2">
                                <p className="text-sm font-medium text-gray-500">Symptoms</p>
                                <p className="text-gray-900">{consultationRecord.symptoms}</p>
                              </div>
                            )}
                            {consultationRecord.diagnosis && (
                              <div className="md:col-span-2">
                                <p className="text-sm font-medium text-gray-500">Diagnosis</p>
                                <p className="text-gray-900 font-medium">{consultationRecord.diagnosis}</p>
                              </div>
                            )}
                            {consultationRecord.treatment_plan && (
                              <div className="md:col-span-2">
                                <p className="text-sm font-medium text-gray-500">Treatment Plan</p>
                                <p className="text-gray-900">{consultationRecord.treatment_plan}</p>
                              </div>
                            )}
                            {consultationRecord.vital_signs && (
                              <div className="md:col-span-2">
                                <p className="text-sm font-medium text-gray-500">Vital Signs</p>
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-2 mt-1">
                                  {consultationRecord.vital_signs.blood_pressure && (
                                    <div className="bg-gray-50 p-2 rounded">
                                      <p className="text-xs text-gray-500">Blood Pressure</p>
                                      <p className="text-sm font-medium">{consultationRecord.vital_signs.blood_pressure}</p>
                                    </div>
                                  )}
                                  {consultationRecord.vital_signs.temperature && (
                                    <div className="bg-gray-50 p-2 rounded">
                                      <p className="text-xs text-gray-500">Temperature</p>
                                      <p className="text-sm font-medium">{consultationRecord.vital_signs.temperature}°C</p>
                                    </div>
                                  )}
                                  {consultationRecord.vital_signs.pulse && (
                                    <div className="bg-gray-50 p-2 rounded">
                                      <p className="text-xs text-gray-500">Pulse</p>
                                      <p className="text-sm font-medium">{consultationRecord.vital_signs.pulse} bpm</p>
                                    </div>
                                  )}
                                </div>
                              </div>
                            )}
                            {consultationRecord.notes && (
                              <div className="md:col-span-2">
                                <p className="text-sm font-medium text-gray-500">Consultation Notes</p>
                                <p className="text-gray-900">{consultationRecord.notes}</p>
                              </div>
                            )}
                            <div className="md:col-span-2">
                              <p className="text-sm font-medium text-gray-500">Record Created</p>
                              <p className="text-gray-900">{new Date(consultationRecord.created_at).toLocaleString()}</p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ) : null;
                  })()}

                  {/* Prescribed Medicines */}
                  {(() => {
                    // Get all prescribed medicines from consultation records
                    const allPrescribedMedicines: any[] = [];

                    consultationRecords
                      .filter(record => record.patient_id === selectedPatient.id)
                      .forEach(consultation => {
                        // Check if prescribed_medicines field exists
                        if (consultation.prescribed_medicines && Array.isArray(consultation.prescribed_medicines)) {
                          allPrescribedMedicines.push(...consultation.prescribed_medicines.map((med: any) => ({
                            ...med,
                            consultationDate: consultation.consultation_date,
                            doctor: consultation.doctor_name
                          })));
                        }

                        // Also check notes for prescribed medicines (fallback)
                        if (consultation.notes && consultation.notes.includes('--- PRESCRIBED MEDICINES ---')) {
                          const medicinesSection = consultation.notes.split('--- PRESCRIBED MEDICINES ---')[1];
                          if (medicinesSection) {
                            const medicineLines = medicinesSection.trim().split('\n').filter((line: string) => line.trim() && line.match(/^\d+\./));
                            medicineLines.forEach((line: string) => {
                              const match = line.match(/^\d+\.\s*(.+?)\s*-\s*(.+)/);
                              if (match) {
                                allPrescribedMedicines.push({
                                  name: match[1].trim(),
                                  dosage: match[2].trim(),
                                  consultationDate: consultation.consultation_date,
                                  doctor: consultation.doctor_name
                                });
                              }
                            });
                          }
                        }
                      });

                    return allPrescribedMedicines.length > 0 ? (
                      <PrescribedMedicinesList medicines={allPrescribedMedicines} />
                    ) : null;
                  })()}

                  {/* Laboratory Records */}
                  {(() => {
                    const laboratoryRecord = laboratoryRecords.find(record => record.patient_id === selectedPatient.id);
                    return laboratoryRecord ? (
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-lg flex items-center">
                            <TestTube className="h-5 w-5 mr-2 text-purple-600" />
                            Laboratory Record
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <p className="text-sm font-medium text-gray-500">Technician Name</p>
                              <p className="text-gray-900 font-medium">{laboratoryRecord.technician_name}</p>
                            </div>
                            <div>
                              <p className="text-sm font-medium text-gray-500">Test Date</p>
                              <p className="text-gray-900">{laboratoryRecord.test_date ? new Date(laboratoryRecord.test_date).toLocaleDateString() : 'Not specified'}</p>
                            </div>
                            <div>
                              <p className="text-sm font-medium text-gray-500">Test Type</p>
                              <p className="text-gray-900 font-medium">{laboratoryRecord.test_type}</p>
                            </div>
                            <div>
                              <p className="text-sm font-medium text-gray-500">Status</p>
                              <Badge className={
                                laboratoryRecord.status === 'completed' ? 'bg-green-100 text-green-800' :
                                laboratoryRecord.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                'bg-blue-100 text-blue-800'
                              }>
                                {laboratoryRecord.status?.charAt(0).toUpperCase() + laboratoryRecord.status?.slice(1)}
                              </Badge>
                            </div>
                            {laboratoryRecord.test_results && Object.keys(laboratoryRecord.test_results).length > 0 && (
                              <div className="md:col-span-2">
                                <p className="text-sm font-medium text-gray-500 mb-2">Test Results</p>
                                <div className="bg-gray-50 p-3 rounded-lg">
                                  {Object.entries(laboratoryRecord.test_results).map(([key, value]) => (
                                    <div key={key} className="flex justify-between items-center py-1">
                                      <span className="text-sm text-gray-600 capitalize">{key.replace(/_/g, ' ')}</span>
                                      <span className="text-sm font-medium text-gray-900">{String(value)}</span>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                            {laboratoryRecord.reference_ranges && Object.keys(laboratoryRecord.reference_ranges).length > 0 && (
                              <div className="md:col-span-2">
                                <p className="text-sm font-medium text-gray-500 mb-2">Reference Ranges</p>
                                <div className="bg-blue-50 p-3 rounded-lg">
                                  {Object.entries(laboratoryRecord.reference_ranges).map(([key, value]) => (
                                    <div key={key} className="flex justify-between items-center py-1">
                                      <span className="text-sm text-gray-600 capitalize">{key.replace(/_/g, ' ')}</span>
                                      <span className="text-sm font-medium text-blue-900">{String(value)}</span>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                            {laboratoryRecord.lab_notes && (
                              <div className="md:col-span-2">
                                <p className="text-sm font-medium text-gray-500">Laboratory Notes</p>
                                <p className="text-gray-900">{laboratoryRecord.lab_notes}</p>
                              </div>
                            )}
                            <div className="md:col-span-2">
                              <p className="text-sm font-medium text-gray-500">Record Created</p>
                              <p className="text-gray-900">{new Date(laboratoryRecord.created_at).toLocaleString()}</p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ) : null;
                  })()}

                  {/* Pharmacy Records */}
                  {(() => {
                    const pharmacyRecord = pharmacyRecords.find(record => record.patient_id === selectedPatient.id);
                    return pharmacyRecord ? (
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-lg flex items-center">
                            <Pill className="h-5 w-5 mr-2 text-orange-600" />
                            Pharmacy Record
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <p className="text-sm font-medium text-gray-500">Pharmacist Name</p>
                              <p className="text-gray-900 font-medium">{pharmacyRecord.pharmacist_name}</p>
                            </div>
                            <div>
                              <p className="text-sm font-medium text-gray-500">Dispensed Date</p>
                              <p className="text-gray-900">{pharmacyRecord.dispensed_date ? new Date(pharmacyRecord.dispensed_date).toLocaleDateString() : 'Not specified'}</p>
                            </div>
                            <div>
                              <p className="text-sm font-medium text-gray-500">Total Cost</p>
                              <p className="text-gray-900 font-medium text-green-600">${pharmacyRecord.total_cost?.toFixed(2) || '0.00'}</p>
                            </div>
                            <div>
                              <p className="text-sm font-medium text-gray-500">Status</p>
                              <Badge className="bg-green-100 text-green-800">Dispensed</Badge>
                            </div>
                            {pharmacyRecord.medications && Array.isArray(pharmacyRecord.medications) && pharmacyRecord.medications.length > 0 && (
                              <div className="md:col-span-2">
                                <p className="text-sm font-medium text-gray-500 mb-2">Medications Dispensed</p>
                                <div className="space-y-2">
                                  {pharmacyRecord.medications.map((medication: any, index: number) => (
                                    <div key={index} className="bg-orange-50 p-3 rounded-lg border border-orange-200">
                                      <div className="grid grid-cols-1 md:grid-cols-4 gap-2">
                                        <div>
                                          <p className="text-xs text-gray-500">Medication</p>
                                          <p className="text-sm font-medium text-gray-900">{medication.name || 'Not specified'}</p>
                                        </div>
                                        <div>
                                          <p className="text-xs text-gray-500">Dosage</p>
                                          <p className="text-sm text-gray-900">{medication.dosage || 'Not specified'}</p>
                                        </div>
                                        <div>
                                          <p className="text-xs text-gray-500">Quantity</p>
                                          <p className="text-sm text-gray-900">{medication.quantity || 'Not specified'}</p>
                                        </div>
                                        <div>
                                          <p className="text-xs text-gray-500">Instructions</p>
                                          <p className="text-sm text-gray-900">{medication.instructions || 'Not specified'}</p>
                                        </div>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                            {pharmacyRecord.notes && (
                              <div className="md:col-span-2">
                                <p className="text-sm font-medium text-gray-500">Pharmacy Notes</p>
                                <p className="text-gray-900">{pharmacyRecord.notes}</p>
                              </div>
                            )}
                            <div className="md:col-span-2">
                              <p className="text-sm font-medium text-gray-500">Record Created</p>
                              <p className="text-gray-900">{new Date(pharmacyRecord.created_at).toLocaleString()}</p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ) : null;
                  })()}

                  {/* Workflow Summary */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg flex items-center">
                        <Activity className="h-5 w-5 mr-2 text-green-600" />
                        Workflow Summary
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {(() => {
                          const consultationRecord = consultationRecords.find(record => record.patient_id === selectedPatient.id);
                          const laboratoryRecord = laboratoryRecords.find(record => record.patient_id === selectedPatient.id);
                          const pharmacyRecord = pharmacyRecords.find(record => record.patient_id === selectedPatient.id);
                          const isWorkflowComplete = consultationRecord && laboratoryRecord;

                          return (
                            <>
                              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div className="flex items-center space-x-3">
                                  <div className={`p-2 rounded-lg ${consultationRecord ? 'bg-green-100' : 'bg-gray-100'}`}>
                                    <Stethoscope className={`h-4 w-4 ${consultationRecord ? 'text-green-600' : 'text-gray-400'}`} />
                                  </div>
                                  <div>
                                    <p className="font-medium">Consultation</p>
                                    <p className="text-sm text-gray-600">
                                      {consultationRecord ? `Completed by Dr. ${consultationRecord.doctor_name}` : 'Not completed'}
                                    </p>
                                  </div>
                                </div>
                                <Badge className={consultationRecord ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}>
                                  {consultationRecord ? 'Complete' : 'Pending'}
                                </Badge>
                              </div>

                              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div className="flex items-center space-x-3">
                                  <div className={`p-2 rounded-lg ${laboratoryRecord ? 'bg-green-100' : 'bg-gray-100'}`}>
                                    <TestTube className={`h-4 w-4 ${laboratoryRecord ? 'text-green-600' : 'text-gray-400'}`} />
                                  </div>
                                  <div>
                                    <p className="font-medium">Laboratory</p>
                                    <p className="text-sm text-gray-600">
                                      {laboratoryRecord ? `Completed by ${laboratoryRecord.technician_name}` : 'Not completed'}
                                    </p>
                                  </div>
                                </div>
                                <Badge className={laboratoryRecord ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}>
                                  {laboratoryRecord ? 'Complete' : 'Pending'}
                                </Badge>
                              </div>

                              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div className="flex items-center space-x-3">
                                  <div className={`p-2 rounded-lg ${pharmacyRecord ? 'bg-orange-100' : 'bg-gray-100'}`}>
                                    <Pill className={`h-4 w-4 ${pharmacyRecord ? 'text-orange-600' : 'text-gray-400'}`} />
                                  </div>
                                  <div>
                                    <p className="font-medium">Pharmacy (Independent)</p>
                                    <p className="text-sm text-gray-600">
                                      {pharmacyRecord ? `Medications dispensed by ${pharmacyRecord.pharmacist_name}` : 'No pharmacy records'}
                                    </p>
                                  </div>
                                </div>
                                <Badge className={pharmacyRecord ? 'bg-orange-100 text-orange-800' : 'bg-gray-100 text-gray-600'}>
                                  {pharmacyRecord ? 'Has Records' : 'No Records'}
                                </Badge>
                              </div>

                              <div className="mt-4 p-4 bg-gradient-to-r from-blue-50 to-green-50 rounded-lg border">
                                <div className="flex items-center justify-between">
                                  <div>
                                    <p className="font-medium text-gray-900">Overall Status</p>
                                    <p className="text-sm text-gray-600">
                                      {isWorkflowComplete ? 'Medical workflow completed successfully' : 'Medical workflow in progress'}
                                    </p>
                                  </div>
                                  <Badge className={isWorkflowComplete ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'}>
                                    {isWorkflowComplete ? 'Workflow Complete' : 'In Progress'}
                                  </Badge>
                                </div>
                              </div>
                            </>
                          );
                        })()}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </Layout>
  );
};

export default Patients;
