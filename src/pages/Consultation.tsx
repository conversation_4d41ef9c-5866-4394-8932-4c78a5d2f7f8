
import React, { useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Layout } from '../components/Layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Save, ArrowLeft, User, Calendar, Phone, Mail, Stethoscope, Activity, FileText, Pill, AlertCircle } from 'lucide-react';
import { useSupabaseData } from '@/hooks/useSupabaseData';
import { useWorkflowData } from '@/hooks/useWorkflowData';
import { useNavigate } from 'react-router-dom';

const Consultation = () => {
  const [searchParams] = useSearchParams();
  const patientId = searchParams.get('patientId');
  const navigate = useNavigate();
  
  const [consultationData, setConsultationData] = useState({
    doctorName: '',
    diagnosis: '',
    treatmentPlan: '',
    symptoms: '',
    vitalSigns: {
      bloodPressure: '',
      temperature: '',
      pulse: '',
      weight: '',
      height: ''
    },
    notes: ''
  });

  const { usePatients } = useSupabaseData();
  const { data: patients = [] } = usePatients();
  const { createConsultationRecord } = useWorkflowData();

  const patient = patients.find(p => p.id === patientId);

  // Helper function to get department information
  const getDepartmentInfo = (patient: any) => {
    if (!patient) return null;
    // Handle both flat structure (from reception function) and nested structure (from regular patients query)
    if (patient.department_name) {
      return {
        name: patient.department_name,
        color: patient.department_color,
        icon: patient.department_icon,
        notes: patient.department_notes,
        description: patient.department_description
      };
    } else if (patient.medical_departments) {
      return {
        name: patient.medical_departments.name,
        color: patient.medical_departments.color,
        icon: patient.medical_departments.icon,
        notes: patient.department_notes,
        description: patient.medical_departments.description
      };
    }
    return null;
  };

  const handleInputChange = (field: string, value: string) => {
    setConsultationData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleVitalSignsChange = (field: string, value: string) => {
    setConsultationData(prev => ({
      ...prev,
      vitalSigns: {
        ...prev.vitalSigns,
        [field]: value
      }
    }));
  };

  const handleSaveConsultation = async () => {
    if (!patient) return;

    try {
      await createConsultationRecord.mutateAsync({
        patient_id: patient.id,
        doctor_name: consultationData.doctorName,
        diagnosis: consultationData.diagnosis,
        treatment_plan: consultationData.treatmentPlan,
        symptoms: consultationData.symptoms,
        vital_signs: consultationData.vitalSigns,
        notes: consultationData.notes
      });
      
      navigate('/patients');
    } catch (error) {
      console.error('Error saving consultation:', error);
    }
  };

  const calculateAge = (dateOfBirth: string) => {
    if (!dateOfBirth) return 'N/A';
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  if (!patient) {
    return (
      <Layout>
        <div className="flex flex-col items-center justify-center h-64 space-y-4">
          <div className="w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
          <div className="text-base sm:text-lg text-gray-600 text-center">
            {patients.length === 0 ? 'Loading patient information...' : 'Patient not found'}
          </div>
          {patients.length > 0 && (
            <Button
              variant="outline"
              onClick={() => navigate('/patients')}
              className="text-sm"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Patients
            </Button>
          )}
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-4 sm:space-y-6 p-2 sm:p-0">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 bg-white p-4 rounded-lg shadow-sm border">
          <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4">
            <Button
              variant="outline"
              onClick={() => navigate('/medical-records')}
              className="flex items-center w-full sm:w-auto"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Medical Records
            </Button>
            <div>
              <h1 className="text-xl sm:text-3xl font-bold text-gray-900">Consultation</h1>
              <p className="text-gray-600 mt-1 text-sm sm:text-base">Patient consultation and diagnosis</p>
            </div>
          </div>
          {/* Patient Quick Info on Mobile */}
          <div className="sm:hidden bg-blue-50 p-3 rounded-lg border border-blue-200">
            <div className="flex items-center space-x-2">
              <Avatar className="h-8 w-8">
                <AvatarFallback className="text-xs">
                  {patient.patient_name?.split(' ').map((n: string) => n[0]).join('') || 'P'}
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="font-medium text-sm text-gray-900">{patient.patient_name}</p>
                <p className="text-xs text-gray-600">Age: {calculateAge(patient.date_of_birth)} years</p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
          {/* Patient Information */}
          <Card className="lg:col-span-1 order-1 lg:order-1">
            <CardHeader className="pb-3 sm:pb-6">
              <CardTitle className="flex items-center text-sm sm:text-base">
                <User className="h-4 w-4 sm:h-5 sm:w-5 mr-2 text-blue-600" />
                Patient Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 sm:space-y-4 p-4 sm:p-6">
              <div className="flex items-center space-x-3 sm:space-x-4">
                <Avatar className="h-12 w-12 sm:h-16 sm:w-16 flex-shrink-0">
                  <AvatarFallback className="text-sm sm:text-lg">
                    {patient.patient_name?.split(' ').map((n: string) => n[0]).join('') || 'P'}
                  </AvatarFallback>
                </Avatar>
                <div className="min-w-0 flex-1">
                  <h3 className="font-bold text-base sm:text-lg truncate">{patient.patient_name}</h3>
                  <p className="text-xs sm:text-sm text-gray-500 truncate">ID: {patient.id.slice(0, 8)}...</p>
                </div>
              </div>

              <div className="space-y-2 sm:space-y-3">
                <div className="flex items-center text-xs sm:text-sm">
                  <Calendar className="h-3 w-3 sm:h-4 sm:w-4 mr-2 text-gray-500 flex-shrink-0" />
                  <span>Age: {calculateAge(patient.date_of_birth)} years</span>
                </div>
                <div className="flex items-center text-xs sm:text-sm">
                  <Mail className="h-3 w-3 sm:h-4 sm:w-4 mr-2 text-gray-500 flex-shrink-0" />
                  <span className="truncate">{patient.email}</span>
                </div>
                <div className="flex items-center text-xs sm:text-sm">
                  <Phone className="h-3 w-3 sm:h-4 sm:w-4 mr-2 text-gray-500 flex-shrink-0" />
                  <span className="truncate">{patient.phone_number}</span>
                </div>
                {patient.blood_type && (
                  <div className="flex items-center justify-between">
                    <span className="text-xs sm:text-sm text-gray-600">Blood Type:</span>
                    <Badge variant="outline" className="text-xs">{patient.blood_type}</Badge>
                  </div>
                )}

                {/* Department Information - Highlighted */}
                {(() => {
                  const deptInfo = getDepartmentInfo(patient);
                  return deptInfo && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mt-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center text-xs sm:text-sm font-medium text-blue-900">
                          <Stethoscope className="h-4 w-4 mr-2 text-blue-600" />
                          <span>Department:</span>
                        </div>
                        <Badge
                          className="text-xs text-white font-medium"
                          style={{ backgroundColor: deptInfo.color || '#3B82F6' }}
                        >
                          {deptInfo.name}
                        </Badge>
                      </div>
                      {deptInfo.description && (
                        <p className="text-xs text-blue-700 mt-2">
                          {deptInfo.description}
                        </p>
                      )}
                      {deptInfo.notes && (
                        <div className="mt-2">
                          <p className="text-xs font-medium text-blue-900">Notes:</p>
                          <p className="text-xs text-blue-700">{deptInfo.notes}</p>
                        </div>
                      )}
                    </div>
                  );
                })()}
              </div>
            </CardContent>
          </Card>

          {/* Consultation Form */}
          <Card className="lg:col-span-2 order-2 lg:order-2">
            <CardHeader className="pb-3 sm:pb-6">
              <CardTitle className="text-sm sm:text-base flex items-center">
                <Stethoscope className="h-4 w-4 sm:h-5 sm:w-5 mr-2 text-green-600" />
                Consultation Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 sm:space-y-6 p-4 sm:p-6">
              <div className="grid grid-cols-1 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="doctorName" className="text-sm">Doctor Name</Label>
                  <Input
                    id="doctorName"
                    value={consultationData.doctorName}
                    onChange={(e) => handleInputChange('doctorName', e.target.value)}
                    placeholder="Enter doctor name"
                    className="text-sm sm:text-base"
                  />
                </div>
              </div>

              {/* Symptoms Section */}
              <div className="space-y-2 p-3 sm:p-4 bg-red-50 rounded-lg border border-red-200">
                <Label htmlFor="symptoms" className="text-sm font-medium flex items-center">
                  <AlertCircle className="h-4 w-4 mr-2 text-red-600" />
                  Symptoms
                </Label>
                <Textarea
                  id="symptoms"
                  value={consultationData.symptoms}
                  onChange={(e) => handleInputChange('symptoms', e.target.value)}
                  placeholder="Describe patient symptoms..."
                  rows={3}
                  className="text-sm sm:text-base bg-white"
                />
              </div>

              {/* Vital Signs */}
              <div className="space-y-3 sm:space-y-4 p-3 sm:p-4 bg-blue-50 rounded-lg border border-blue-200">
                <h3 className="text-base sm:text-lg font-semibold flex items-center">
                  <Activity className="h-4 w-4 sm:h-5 sm:w-5 mr-2 text-blue-600" />
                  Vital Signs
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-5 gap-3 sm:gap-4">
                  <div className="space-y-2 p-2 bg-white rounded border">
                    <Label htmlFor="bloodPressure" className="text-xs sm:text-sm font-medium text-gray-700">
                      🩸 Blood Pressure
                    </Label>
                    <Input
                      id="bloodPressure"
                      value={consultationData.vitalSigns.bloodPressure}
                      onChange={(e) => handleVitalSignsChange('bloodPressure', e.target.value)}
                      placeholder="120/80"
                      className="text-sm sm:text-base"
                    />
                  </div>
                  <div className="space-y-2 p-2 bg-white rounded border">
                    <Label htmlFor="temperature" className="text-xs sm:text-sm font-medium text-gray-700">
                      🌡️ Temperature (°C)
                    </Label>
                    <Input
                      id="temperature"
                      value={consultationData.vitalSigns.temperature}
                      onChange={(e) => handleVitalSignsChange('temperature', e.target.value)}
                      placeholder="37.5"
                      className="text-sm sm:text-base"
                    />
                  </div>
                  <div className="space-y-2 p-2 bg-white rounded border">
                    <Label htmlFor="pulse" className="text-xs sm:text-sm font-medium text-gray-700">
                      💓 Pulse (bpm)
                    </Label>
                    <Input
                      id="pulse"
                      value={consultationData.vitalSigns.pulse}
                      onChange={(e) => handleVitalSignsChange('pulse', e.target.value)}
                      placeholder="72"
                      className="text-sm sm:text-base"
                    />
                  </div>
                  <div className="space-y-2 p-2 bg-white rounded border">
                    <Label htmlFor="weight" className="text-xs sm:text-sm font-medium text-gray-700">
                      ⚖️ Weight (kg)
                    </Label>
                    <Input
                      id="weight"
                      value={consultationData.vitalSigns.weight}
                      onChange={(e) => handleVitalSignsChange('weight', e.target.value)}
                      placeholder="70"
                      className="text-sm sm:text-base"
                    />
                  </div>
                  <div className="space-y-2 p-2 bg-white rounded border">
                    <Label htmlFor="height" className="text-xs sm:text-sm font-medium text-gray-700">
                      📏 Height (cm)
                    </Label>
                    <Input
                      id="height"
                      value={consultationData.vitalSigns.height}
                      onChange={(e) => handleVitalSignsChange('height', e.target.value)}
                      placeholder="175"
                      className="text-sm sm:text-base"
                    />
                  </div>
                </div>
              </div>

              {/* Diagnosis Section */}
              <div className="space-y-2 p-3 sm:p-4 bg-gray-50 rounded-lg border">
                <Label htmlFor="diagnosis" className="text-sm font-medium flex items-center">
                  <FileText className="h-4 w-4 mr-2 text-purple-600" />
                  Diagnosis
                </Label>
                <Textarea
                  id="diagnosis"
                  value={consultationData.diagnosis}
                  onChange={(e) => handleInputChange('diagnosis', e.target.value)}
                  placeholder="Enter diagnosis..."
                  rows={3}
                  className="text-sm sm:text-base bg-white"
                />
              </div>

              {/* Treatment Plan Section */}
              <div className="space-y-2 p-3 sm:p-4 bg-gray-50 rounded-lg border">
                <Label htmlFor="treatmentPlan" className="text-sm font-medium flex items-center">
                  <Pill className="h-4 w-4 mr-2 text-orange-600" />
                  Treatment Plan
                </Label>
                <Textarea
                  id="treatmentPlan"
                  value={consultationData.treatmentPlan}
                  onChange={(e) => handleInputChange('treatmentPlan', e.target.value)}
                  placeholder="Enter treatment plan..."
                  rows={4}
                  className="text-sm sm:text-base bg-white"
                />
              </div>

              {/* Additional Notes Section */}
              <div className="space-y-2 p-3 sm:p-4 bg-gray-50 rounded-lg border">
                <Label htmlFor="notes" className="text-sm font-medium flex items-center">
                  <FileText className="h-4 w-4 mr-2 text-gray-600" />
                  Additional Notes
                </Label>
                <Textarea
                  id="notes"
                  value={consultationData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  placeholder="Any additional notes..."
                  rows={3}
                  className="text-sm sm:text-base bg-white"
                />
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row justify-end gap-3 pt-4 border-t border-gray-200">
                <Button
                  variant="outline"
                  onClick={() => navigate('/patients')}
                  className="w-full sm:w-auto text-sm order-2 sm:order-1"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
                <Button
                  onClick={handleSaveConsultation}
                  disabled={!consultationData.doctorName || createConsultationRecord.isPending}
                  className="w-full sm:w-auto text-sm bg-green-600 hover:bg-green-700 order-1 sm:order-2"
                >
                  <Save className="h-4 w-4 mr-2" />
                  {createConsultationRecord.isPending ? 'Saving...' : 'Save Consultation'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </Layout>
  );
};

export default Consultation;
