import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON>Title } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  BarChart3, 
  TrendingUp, 
  Clock, 
  Users,
  Calendar,
  Download,
  Filter,
  RefreshCw,
  Activity,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { useWorkflowData } from '@/hooks/useWorkflowData';
import { useSupabaseData } from '@/hooks/useSupabaseData';

const WorkflowReports = () => {
  const [dateRange, setDateRange] = useState({
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days ago
    end: new Date().toISOString().split('T')[0] // today
  });
  const [selectedDepartment, setSelectedDepartment] = useState('all');
  const [reportType, setReportType] = useState('overview');

  const { 
    usePatientWorkflows, 
    useWorkflowStats,
    useConsultationRecords,
    useLaboratoryRecords,
    usePharmacyRecords
  } = useWorkflowData();

  const { data: workflows = [] } = usePatientWorkflows();
  const { data: stats } = useWorkflowStats();
  const { data: consultationRecords = [] } = useConsultationRecords();
  const { data: laboratoryRecords = [] } = useLaboratoryRecords();
  const { data: pharmacyRecords = [] } = usePharmacyRecords();

  // Filter data by date range
  const filteredWorkflows = workflows.filter(workflow => {
    const workflowDate = new Date(workflow.created_at).toISOString().split('T')[0];
    return workflowDate >= dateRange.start && workflowDate <= dateRange.end;
  });

  // Calculate metrics
  const calculateMetrics = () => {
    const totalWorkflows = filteredWorkflows.length;
    const completedWorkflows = filteredWorkflows.filter(w => w.current_department === 'completed').length;
    const completionRate = totalWorkflows > 0 ? (completedWorkflows / totalWorkflows) * 100 : 0;

    // Average processing time
    const completedWithTimes = filteredWorkflows.filter(w => 
      w.current_department === 'completed' && w.created_at && w.updated_at
    );
    
    const avgProcessingTime = completedWithTimes.length > 0 
      ? completedWithTimes.reduce((total, workflow) => {
          const start = new Date(workflow.created_at).getTime();
          const end = new Date(workflow.updated_at).getTime();
          return total + (end - start);
        }, 0) / completedWithTimes.length / (1000 * 60 * 60) // in hours
      : 0;

    // Department distribution
    const departmentCounts = {
      reception: filteredWorkflows.filter(w => w.current_department === 'reception').length,
      consultation: filteredWorkflows.filter(w => w.current_department === 'consultation').length,
      laboratory: filteredWorkflows.filter(w => w.current_department === 'laboratory').length,
      pharmacy: filteredWorkflows.filter(w => w.current_department === 'pharmacy').length,
      completed: completedWorkflows
    };

    // Daily workflow counts
    const dailyCounts = {};
    filteredWorkflows.forEach(workflow => {
      const date = new Date(workflow.created_at).toISOString().split('T')[0];
      dailyCounts[date] = (dailyCounts[date] || 0) + 1;
    });

    return {
      totalWorkflows,
      completedWorkflows,
      completionRate,
      avgProcessingTime,
      departmentCounts,
      dailyCounts
    };
  };

  const metrics = calculateMetrics();

  const exportReport = () => {
    const reportData = {
      dateRange,
      metrics,
      workflows: filteredWorkflows,
      generatedAt: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `workflow-report-${dateRange.start}-to-${dateRange.end}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getDepartmentColor = (department: string) => {
    switch (department) {
      case 'reception': return 'bg-blue-500';
      case 'consultation': return 'bg-green-500';
      case 'laboratory': return 'bg-orange-500';
      case 'pharmacy': return 'bg-purple-500';
      case 'completed': return 'bg-gray-500';
      default: return 'bg-gray-400';
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            <BarChart3 className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Workflow Reports</h1>
            <p className="text-gray-600">Analytics and performance insights</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={exportReport}>
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
          <Button variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-gray-400" />
              <Input
                type="date"
                value={dateRange.start}
                onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                className="w-40"
              />
              <span className="text-gray-500">to</span>
              <Input
                type="date"
                value={dateRange.end}
                onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                className="w-40"
              />
            </div>
            
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-gray-400" />
              <select
                value={selectedDepartment}
                onChange={(e) => setSelectedDepartment(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg"
              >
                <option value="all">All Departments</option>
                <option value="reception">Reception</option>
                <option value="consultation">Consultation</option>
                <option value="laboratory">Laboratory</option>
                <option value="pharmacy">Pharmacy</option>
              </select>
            </div>

            <div className="flex space-x-2">
              <Button
                variant={reportType === 'overview' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setReportType('overview')}
              >
                Overview
              </Button>
              <Button
                variant={reportType === 'performance' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setReportType('performance')}
              >
                Performance
              </Button>
              <Button
                variant={reportType === 'trends' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setReportType('trends')}
              >
                Trends
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Activity className="h-8 w-8 text-blue-600 mr-3" />
              <div>
                <p className="text-2xl font-bold">{metrics.totalWorkflows}</p>
                <p className="text-sm text-gray-600">Total Workflows</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-green-600 mr-3" />
              <div>
                <p className="text-2xl font-bold">{metrics.completedWorkflows}</p>
                <p className="text-sm text-gray-600">Completed</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <TrendingUp className="h-8 w-8 text-purple-600 mr-3" />
              <div>
                <p className="text-2xl font-bold">{metrics.completionRate.toFixed(1)}%</p>
                <p className="text-sm text-gray-600">Completion Rate</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-orange-600 mr-3" />
              <div>
                <p className="text-2xl font-bold">{metrics.avgProcessingTime.toFixed(1)}h</p>
                <p className="text-sm text-gray-600">Avg Processing Time</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Department Distribution */}
      <Card>
        <CardHeader>
          <CardTitle>Department Distribution</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Object.entries(metrics.departmentCounts).map(([department, count]) => {
              const percentage = metrics.totalWorkflows > 0 
                ? (count / metrics.totalWorkflows) * 100 
                : 0;
              
              return (
                <div key={department} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-4 h-4 rounded ${getDepartmentColor(department)}`}></div>
                    <span className="font-medium capitalize">{department}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-32 bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${getDepartmentColor(department)}`}
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>
                    <span className="text-sm text-gray-600 w-12 text-right">
                      {count} ({percentage.toFixed(1)}%)
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Performance Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Performance Insights</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium">High Completion Rate</span>
                </div>
                <Badge className="bg-green-100 text-green-800">
                  {metrics.completionRate > 80 ? 'Excellent' : 'Good'}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium">Processing Time</span>
                </div>
                <Badge className="bg-blue-100 text-blue-800">
                  {metrics.avgProcessingTime < 24 ? 'Fast' : 'Needs Improvement'}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                <div className="flex items-center space-x-2">
                  <Users className="h-4 w-4 text-orange-600" />
                  <span className="text-sm font-medium">Daily Volume</span>
                </div>
                <Badge className="bg-orange-100 text-orange-800">
                  {Object.keys(metrics.dailyCounts).length > 0 
                    ? `${(metrics.totalWorkflows / Object.keys(metrics.dailyCounts).length).toFixed(1)}/day`
                    : '0/day'
                  }
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recommendations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {metrics.departmentCounts.reception > metrics.departmentCounts.consultation * 2 && (
                <div className="flex items-start space-x-2 p-3 bg-yellow-50 rounded-lg">
                  <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-yellow-800">Reception Bottleneck</p>
                    <p className="text-xs text-yellow-700">Consider adding more consultation capacity</p>
                  </div>
                </div>
              )}
              
              {metrics.avgProcessingTime > 48 && (
                <div className="flex items-start space-x-2 p-3 bg-red-50 rounded-lg">
                  <AlertTriangle className="h-4 w-4 text-red-600 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-red-800">Long Processing Times</p>
                    <p className="text-xs text-red-700">Review workflow efficiency and staffing</p>
                  </div>
                </div>
              )}
              
              {metrics.completionRate > 90 && (
                <div className="flex items-start space-x-2 p-3 bg-green-50 rounded-lg">
                  <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-green-800">Excellent Performance</p>
                    <p className="text-xs text-green-700">Workflow is operating efficiently</p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default WorkflowReports;
