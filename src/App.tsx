import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "./contexts/AuthContext";
import { CurrencyProvider } from "./contexts/CurrencyContext";
import { ProtectedRoute } from "./components/ProtectedRoute";
import ErrorBoundary from "./components/ErrorBoundary";

// Page imports
import Landing from "./pages/Landing";
import Auth from "./pages/Auth";
import Index from "./pages/Index";
import MedicalRecords from "./pages/Patients"; // Renamed from Patients to MedicalRecords
import Doctors from "./pages/Doctors";
import Appointments from "./pages/Appointments";

import BillingAccounting from "./pages/BillingAccounting";
import Inventory from "./pages/Inventory";
import Reports from "./pages/Reports";
import Settings from "./pages/Settings";
import Departments from "./pages/Departments";
import Profile from "./pages/Profile";
import NotFound from "./pages/NotFound";
import AboutUs from "./pages/AboutUs";
import Reception from "./pages/Reception";
import ConsultationDepartment from "./pages/ConsultationDepartment";
import LaboratoryDepartment from "./pages/LaboratoryDepartment";
import PharmacyDepartment from "./pages/PharmacyDepartment";
import WorkflowReports from "./pages/WorkflowReports";
import Features from "./pages/Features";
import ContactUs from "./pages/ContactUs";
import DemoLogin from "./pages/DemoLogin";
import Workflow from "./pages/Workflow";
import Consultation from "./pages/Consultation";

const queryClient = new QueryClient();

function App() {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <CurrencyProvider>
            <TooltipProvider>
            <Toaster />
            <Sonner />
            <BrowserRouter>
            <Routes>
              {/* Public routes */}
              <Route path="/" element={<Landing />} />
              <Route path="/auth" element={<Auth />} />
              <Route path="/login" element={<Auth />} />
              <Route path="/register" element={<Auth />} />
              <Route path="/demo-login" element={<DemoLogin />} />
              <Route path="/about-us" element={<AboutUs />} />
              <Route path="/features" element={<Features />} />
              <Route path="/contact-us" element={<ContactUs />} />

              {/* Protected routes */}
              <Route path="/dashboard" element={<ProtectedRoute><Index /></ProtectedRoute>} />
              <Route path="/reception" element={<ProtectedRoute><Reception /></ProtectedRoute>} />
              <Route path="/medical-records" element={<ProtectedRoute><MedicalRecords /></ProtectedRoute>} />
              <Route path="/consultation" element={<ProtectedRoute><Consultation /></ProtectedRoute>} />
              <Route path="/doctors" element={<ProtectedRoute><Doctors /></ProtectedRoute>} />
              <Route path="/appointments" element={<ProtectedRoute><Appointments /></ProtectedRoute>} />
              {/* Legacy route redirect */}
              <Route path="/patients" element={<ProtectedRoute><MedicalRecords /></ProtectedRoute>} />

              <Route path="/billing-accounting" element={<ProtectedRoute><BillingAccounting /></ProtectedRoute>} />
              <Route path="/inventory" element={<ProtectedRoute><Inventory /></ProtectedRoute>} />
              <Route path="/reports" element={<ProtectedRoute><Reports /></ProtectedRoute>} />
              <Route path="/settings" element={<ProtectedRoute><Settings /></ProtectedRoute>} />
              <Route path="/departments" element={<ProtectedRoute><Departments /></ProtectedRoute>} />
              <Route path="/profile" element={<ProtectedRoute><Profile /></ProtectedRoute>} />
              <Route path="/workflow" element={<ProtectedRoute><Workflow /></ProtectedRoute>} />
              <Route path="/reception" element={<ProtectedRoute><Reception /></ProtectedRoute>} />
              <Route path="/consultation-department" element={<ProtectedRoute><ConsultationDepartment /></ProtectedRoute>} />
              <Route path="/laboratory-department" element={<ProtectedRoute><LaboratoryDepartment /></ProtectedRoute>} />
              <Route path="/pharmacy-department" element={<ProtectedRoute><PharmacyDepartment /></ProtectedRoute>} />
              <Route path="/workflow-reports" element={<ProtectedRoute><WorkflowReports /></ProtectedRoute>} />

              {/* 404 and fallback */}
              <Route path="/404" element={<NotFound />} />
              <Route path="*" element={<NotFound />} />
            </Routes>
            </BrowserRouter>
          </TooltipProvider>
          </CurrencyProvider>
        </AuthProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App;
