// Comprehensive currency configuration
export const currencies = [
  // African currencies (primary focus)
  { value: 'KES', label: 'KES - Kenyan Shilling (KSh)', symbol: 'KSh', locale: 'en-KE' },
  { value: 'NGN', label: 'NGN - Nigerian Naira (₦)', symbol: '₦', locale: 'en-NG' },
  { value: 'ZAR', label: 'ZAR - South African Rand (R)', symbol: 'R', locale: 'en-ZA' },
  { value: 'EGP', label: 'EGP - Egyptian Pound (E£)', symbol: 'E£', locale: 'ar-EG' },
  { value: 'MAD', label: 'MAD - Moroccan Dirham (DH)', symbol: 'DH', locale: 'ar-MA' },
  { value: 'TND', label: 'TND - Tunisian Dinar (DT)', symbol: 'DT', locale: 'ar-TN' },
  { value: 'GHS', label: 'GHS - Ghanaian <PERSON> (GH₵)', symbol: 'GH₵', locale: 'en-GH' },
  { value: 'UGX', label: 'UGX - Uganda<PERSON> (USh)', symbol: 'USh', locale: 'en-UG' },
  { value: 'TZS', label: 'TZS - Tanzanian <PERSON>lling (TSh)', symbol: 'TSh', locale: 'en-TZ' },
  { value: 'ETB', label: 'ETB - Ethiopian Birr (Br)', symbol: 'Br', locale: 'en-ET' },
  { value: 'RWF', label: 'RWF - Rwandan Franc (RF)', symbol: 'RF', locale: 'en-RW' },
  { value: 'XOF', label: 'XOF - West African CFA Franc (CFA)', symbol: 'CFA', locale: 'fr-SN' },
  { value: 'XAF', label: 'XAF - Central African CFA Franc (FCFA)', symbol: 'FCFA', locale: 'fr-CM' },
  { value: 'BWP', label: 'BWP - Botswana Pula (P)', symbol: 'P', locale: 'en-BW' },
  { value: 'MUR', label: 'MUR - Mauritian Rupee (₨)', symbol: '₨', locale: 'en-MU' },

  // International currencies (secondary)
  { value: 'USD', label: 'USD - US Dollar ($)', symbol: '$', locale: 'en-US' },
  { value: 'EUR', label: 'EUR - Euro (€)', symbol: '€', locale: 'en-EU' },
  { value: 'GBP', label: 'GBP - British Pound (£)', symbol: '£', locale: 'en-GB' },
];

// Helper functions for currency operations
export const getCurrencySymbol = (currencyCode: string): string => {
  const currency = currencies.find(c => c.value === currencyCode);
  return currency?.symbol || currencyCode;
};

export const getCurrencyLocale = (currencyCode: string): string => {
  const currency = currencies.find(c => c.value === currencyCode);
  return currency?.locale || 'en-US';
};

export const formatCurrency = (amount: number, currencyCode: string): string => {
  const symbol = getCurrencySymbol(currencyCode);
  const locale = getCurrencyLocale(currencyCode);

  try {
    // For currencies that don't have built-in Intl support, use custom formatting
    if (['XOF', 'XAF', 'RWF', 'UGX', 'TZS', 'ETB'].includes(currencyCode)) {
      const formattedNumber = new Intl.NumberFormat(locale, {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2,
      }).format(amount);
      return `${symbol} ${formattedNumber}`;
    }

    // Use standard Intl.NumberFormat for supported currencies
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currencyCode,
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(amount);
  } catch (error) {
    // Fallback formatting if Intl fails
    const formattedNumber = new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(amount);
    return `${symbol} ${formattedNumber}`;
  }
};