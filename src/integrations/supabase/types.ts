export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      appointments: {
        Row: {
          created_at: string | null
          date_time: string
          department: string
          doctor: string
          id: string
          patient_name: string
          status: string
          type: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          date_time: string
          department: string
          doctor: string
          id?: string
          patient_name: string
          status: string
          type?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          date_time?: string
          department?: string
          doctor?: string
          id?: string
          patient_name?: string
          status?: string
          type?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      billing_invoices: {
        Row: {
          created_at: string | null
          due_date: string | null
          id: string
          invoice_date: string | null
          invoice_items: Json | null
          net_profit_loss: number | null
          notes: string | null
          patient: string
          status: string | null
          total_expenses: number | null
          total_revenue: number | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          due_date?: string | null
          id?: string
          invoice_date?: string | null
          invoice_items?: Json | null
          net_profit_loss?: number | null
          notes?: string | null
          patient: string
          status?: string | null
          total_expenses?: number | null
          total_revenue?: number | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          due_date?: string | null
          id?: string
          invoice_date?: string | null
          invoice_items?: Json | null
          net_profit_loss?: number | null
          notes?: string | null
          patient?: string
          status?: string | null
          total_expenses?: number | null
          total_revenue?: number | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      consultation_records: {
        Row: {
          consultation_date: string | null
          created_at: string | null
          diagnosis: string | null
          doctor_name: string
          id: string
          notes: string | null
          patient_id: string | null
          symptoms: string | null
          treatment_plan: string | null
          updated_at: string | null
          user_id: string
          vital_signs: Json | null
          workflow_id: string | null
        }
        Insert: {
          consultation_date?: string | null
          created_at?: string | null
          diagnosis?: string | null
          doctor_name: string
          id?: string
          notes?: string | null
          patient_id?: string | null
          symptoms?: string | null
          treatment_plan?: string | null
          updated_at?: string | null
          user_id: string
          vital_signs?: Json | null
          workflow_id?: string | null
        }
        Update: {
          consultation_date?: string | null
          created_at?: string | null
          diagnosis?: string | null
          doctor_name?: string
          id?: string
          notes?: string | null
          patient_id?: string | null
          symptoms?: string | null
          treatment_plan?: string | null
          updated_at?: string | null
          user_id?: string
          vital_signs?: Json | null
          workflow_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "consultation_records_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "patients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "consultation_records_workflow_id_fkey"
            columns: ["workflow_id"]
            isOneToOne: false
            referencedRelation: "patient_workflows"
            referencedColumns: ["id"]
          },
        ]
      }
      currencies: {
        Row: {
          code: string
          created_at: string
          id: string
          name: string
          symbol: string
        }
        Insert: {
          code: string
          created_at?: string
          id?: string
          name: string
          symbol: string
        }
        Update: {
          code?: string
          created_at?: string
          id?: string
          name?: string
          symbol?: string
        }
        Relationships: []
      }
      departments: {
        Row: {
          created_at: string | null
          department_head: string | null
          department_name: string
          description: string | null
          email_address: string
          id: string
          phone_number: string
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          department_head?: string | null
          department_name: string
          description?: string | null
          email_address: string
          id?: string
          phone_number: string
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          department_head?: string | null
          department_name?: string
          description?: string | null
          email_address?: string
          id?: string
          phone_number?: string
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      doctors: {
        Row: {
          bio: string | null
          created_at: string | null
          email: string
          experience: number | null
          id: string
          name: string
          phone_number: string
          photo: string | null
          qualification: string | null
          specialty: string
          status: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          bio?: string | null
          created_at?: string | null
          email: string
          experience?: number | null
          id?: string
          name: string
          phone_number: string
          photo?: string | null
          qualification?: string | null
          specialty: string
          status?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          bio?: string | null
          created_at?: string | null
          email?: string
          experience?: number | null
          id?: string
          name?: string
          phone_number?: string
          photo?: string | null
          qualification?: string | null
          specialty?: string
          status?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      expense_entries: {
        Row: {
          amount: number
          category: string
          created_at: string
          date: string
          description: string
          id: string
          notes: string | null
          payment_method: string
          updated_at: string
          user_id: string
          vendor: string
        }
        Insert: {
          amount: number
          category: string
          created_at?: string
          date: string
          description: string
          id?: string
          notes?: string | null
          payment_method: string
          updated_at?: string
          user_id: string
          vendor: string
        }
        Update: {
          amount?: number
          category?: string
          created_at?: string
          date?: string
          description?: string
          id?: string
          notes?: string | null
          payment_method?: string
          updated_at?: string
          user_id?: string
          vendor?: string
        }
        Relationships: []
      }
      lab_tests: {
        Row: {
          created_at: string | null
          id: string
          lab_technician: string | null
          patient_id: string | null
          patient_name: string
          status: string | null
          summary: string | null
          test_date: string
          test_name: string
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          lab_technician?: string | null
          patient_id?: string | null
          patient_name: string
          status?: string | null
          summary?: string | null
          test_date: string
          test_name: string
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          lab_technician?: string | null
          patient_id?: string | null
          patient_name?: string
          status?: string | null
          summary?: string | null
          test_date?: string
          test_name?: string
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      laboratory_records: {
        Row: {
          created_at: string | null
          id: string
          lab_notes: string | null
          patient_id: string | null
          reference_ranges: Json | null
          status: string | null
          technician_name: string
          test_date: string | null
          test_results: Json | null
          test_type: string
          updated_at: string | null
          user_id: string
          workflow_id: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          lab_notes?: string | null
          patient_id?: string | null
          reference_ranges?: Json | null
          status?: string | null
          technician_name: string
          test_date?: string | null
          test_results?: Json | null
          test_type: string
          updated_at?: string | null
          user_id: string
          workflow_id?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          lab_notes?: string | null
          patient_id?: string | null
          reference_ranges?: Json | null
          status?: string | null
          technician_name?: string
          test_date?: string | null
          test_results?: Json | null
          test_type?: string
          updated_at?: string | null
          user_id?: string
          workflow_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "laboratory_records_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "patients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "laboratory_records_workflow_id_fkey"
            columns: ["workflow_id"]
            isOneToOne: false
            referencedRelation: "patient_workflows"
            referencedColumns: ["id"]
          },
        ]
      }
      medical_records: {
        Row: {
          created_at: string | null
          date: string
          doctor: string
          id: string
          patient_name: string
          record_type: string | null
          summary: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          date: string
          doctor: string
          id?: string
          patient_name: string
          record_type?: string | null
          summary?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          date?: string
          doctor?: string
          id?: string
          patient_name?: string
          record_type?: string | null
          summary?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      notifications: {
        Row: {
          created_at: string
          id: string
          message: string
          read: boolean
          title: string
          type: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          message: string
          read?: boolean
          title: string
          type?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          message?: string
          read?: boolean
          title?: string
          type?: string
          user_id?: string
        }
        Relationships: []
      }
      medical_departments: {
        Row: {
          color: string | null
          created_at: string | null
          description: string | null
          icon: string | null
          id: string
          is_active: boolean | null
          name: string
          updated_at: string | null
        }
        Insert: {
          color?: string | null
          created_at?: string | null
          description?: string | null
          icon?: string | null
          id?: string
          is_active?: boolean | null
          name: string
          updated_at?: string | null
        }
        Update: {
          color?: string | null
          created_at?: string | null
          description?: string | null
          icon?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      patient_workflows: {
        Row: {
          assigned_to: string | null
          created_at: string | null
          current_department: Database["public"]["Enums"]["workflow_status"]
          id: string
          notes: string | null
          patient_id: string | null
          previous_department:
            | Database["public"]["Enums"]["workflow_status"]
            | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          assigned_to?: string | null
          created_at?: string | null
          current_department?: Database["public"]["Enums"]["workflow_status"]
          id?: string
          notes?: string | null
          patient_id?: string | null
          previous_department?:
            | Database["public"]["Enums"]["workflow_status"]
            | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          assigned_to?: string | null
          created_at?: string | null
          current_department?: Database["public"]["Enums"]["workflow_status"]
          id?: string
          notes?: string | null
          patient_id?: string | null
          previous_department?:
            | Database["public"]["Enums"]["workflow_status"]
            | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "patient_workflows_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "patients"
            referencedColumns: ["id"]
          },
        ]
      }
      patients: {
        Row: {
          blood_type: string | null
          consultation_completed_at: string | null
          created_at: string | null
          date_of_birth: string
          department_id: string | null
          department_notes: string | null
          email: string
          emergency_contact: string | null
          id: string
          insurance: string | null
          laboratory_completed_at: string | null
          last_status_update: string | null
          notes: string | null
          patient_name: string
          phone_number: string
          registration_date: string | null
          sent_to_consultation_at: string | null
          sent_to_laboratory_at: string | null
          updated_at: string | null
          user_id: string | null
          workflow_completed_at: string | null
          workflow_id: string | null
          workflow_status: Database["public"]["Enums"]["workflow_status_enum"] | null
        }
        Insert: {
          blood_type?: string | null
          consultation_completed_at?: string | null
          created_at?: string | null
          date_of_birth: string
          department_id?: string | null
          department_notes?: string | null
          email: string
          emergency_contact?: string | null
          id?: string
          insurance?: string | null
          laboratory_completed_at?: string | null
          last_status_update?: string | null
          notes?: string | null
          patient_name: string
          phone_number: string
          registration_date?: string | null
          sent_to_consultation_at?: string | null
          sent_to_laboratory_at?: string | null
          updated_at?: string | null
          user_id?: string | null
          workflow_completed_at?: string | null
          workflow_id?: string | null
          workflow_status?: Database["public"]["Enums"]["workflow_status_enum"] | null
        }
        Update: {
          blood_type?: string | null
          consultation_completed_at?: string | null
          created_at?: string | null
          date_of_birth?: string
          department_id?: string | null
          department_notes?: string | null
          email?: string
          emergency_contact?: string | null
          id?: string
          insurance?: string | null
          laboratory_completed_at?: string | null
          last_status_update?: string | null
          notes?: string | null
          patient_name?: string
          phone_number?: string
          registration_date?: string | null
          sent_to_consultation_at?: string | null
          sent_to_laboratory_at?: string | null
          updated_at?: string | null
          user_id?: string | null
          workflow_completed_at?: string | null
          workflow_id?: string | null
          workflow_status?: Database["public"]["Enums"]["workflow_status_enum"] | null
        }
        Relationships: [
          {
            foreignKeyName: "patients_workflow_id_fkey"
            columns: ["workflow_id"]
            isOneToOne: false
            referencedRelation: "patient_workflows"
            referencedColumns: ["id"]
          },
        ]
      }
      pharmacy_inventory: {
        Row: {
          created_at: string | null
          expiry_date: string
          id: string
          medication_name: string
          price: number
          status: string | null
          stock_quantity: number
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          expiry_date: string
          id?: string
          medication_name: string
          price: number
          status?: string | null
          stock_quantity: number
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          expiry_date?: string
          id?: string
          medication_name?: string
          price?: number
          status?: string | null
          stock_quantity?: number
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      pharmacy_records: {
        Row: {
          created_at: string | null
          dispensed_date: string | null
          id: string
          medications: Json
          notes: string | null
          patient_id: string | null
          pharmacist_name: string
          total_cost: number | null
          updated_at: string | null
          user_id: string
          workflow_id: string | null
        }
        Insert: {
          created_at?: string | null
          dispensed_date?: string | null
          id?: string
          medications: Json
          notes?: string | null
          patient_id?: string | null
          pharmacist_name: string
          total_cost?: number | null
          updated_at?: string | null
          user_id: string
          workflow_id?: string | null
        }
        Update: {
          created_at?: string | null
          dispensed_date?: string | null
          id?: string
          medications?: Json
          notes?: string | null
          patient_id?: string | null
          pharmacist_name?: string
          total_cost?: number | null
          updated_at?: string | null
          user_id?: string
          workflow_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "pharmacy_records_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "patients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pharmacy_records_workflow_id_fkey"
            columns: ["workflow_id"]
            isOneToOne: false
            referencedRelation: "patient_workflows"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          address: string | null
          avatar_url: string | null
          created_at: string
          currency: string | null
          email: string | null
          first_name: string | null
          hospital_name: string | null
          id: string
          is_restricted: boolean | null
          last_name: string | null
          phone: string | null
          updated_at: string
        }
        Insert: {
          address?: string | null
          avatar_url?: string | null
          created_at?: string
          currency?: string | null
          email?: string | null
          first_name?: string | null
          hospital_name?: string | null
          id: string
          is_restricted?: boolean | null
          last_name?: string | null
          phone?: string | null
          updated_at?: string
        }
        Update: {
          address?: string | null
          avatar_url?: string | null
          created_at?: string
          currency?: string | null
          email?: string | null
          first_name?: string | null
          hospital_name?: string | null
          id?: string
          is_restricted?: boolean | null
          last_name?: string | null
          phone?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      revenue_entries: {
        Row: {
          amount: number
          category: string
          created_at: string
          date: string
          description: string
          id: string
          notes: string | null
          payment_method: string
          updated_at: string
          user_id: string
        }
        Insert: {
          amount: number
          category: string
          created_at?: string
          date: string
          description: string
          id?: string
          notes?: string | null
          payment_method: string
          updated_at?: string
          user_id: string
        }
        Update: {
          amount?: number
          category?: string
          created_at?: string
          date?: string
          description?: string
          id?: string
          notes?: string | null
          payment_method?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      settings: {
        Row: {
          address: string | null
          appointment_duration: number | null
          auto_backup_enabled: boolean | null
          auto_deduct_inventory: boolean | null
          auto_generate_lab_numbers: boolean | null
          auto_invoice_numbering: boolean | null
          backup_frequency: string | null
          business_hours_end: string | null
          business_hours_start: string | null
          city: string | null
          consultation_enabled: boolean | null
          consultation_fee: number | null
          country: string | null
          created_at: string | null
          currency: string | null
          custom_fields: Json | null
          data_retention_months: number | null
          date_format: string | null
          email: string | null
          email_notifications: boolean | null
          email_smtp_host: string | null
          email_smtp_password: string | null
          email_smtp_port: number | null
          email_smtp_username: string | null
          hospital_code: string | null
          hospital_name: string | null
          id: string
          invoice_prefix: string | null
          lab_enabled: boolean | null
          lab_report_template: string | null
          lab_result_approval_required: boolean | null
          language: string | null
          logo_url: string | null
          low_stock_alerts: boolean | null
          low_stock_threshold: number | null
          max_appointments_per_day: number | null
          max_login_attempts: number | null
          password_expiry_days: number | null
          payment_terms_days: number | null
          paystack_public_key: string | null
          paystack_secret_key: string | null
          pharmacy_enabled: boolean | null
          pharmacy_markup_percentage: number | null
          phone_number: string | null
          postal_code: string | null
          receipt_prefix: string | null
          reminder_hours_before: number | null
          require_appointment: boolean | null
          require_prescription: boolean | null
          session_timeout_minutes: number | null
          sms_api_key: string | null
          sms_notifications: boolean | null
          state: string | null
          tax_name: string | null
          tax_rate: number | null
          time_format: string | null
          timezone: string | null
          two_factor_auth: boolean | null
          updated_at: string | null
          user_id: string
          website: string | null
          working_days: string[] | null
          allow_walk_ins: boolean | null
          appointment_reminders: boolean | null
        }
        Insert: {
          address?: string | null
          appointment_duration?: number | null
          auto_backup_enabled?: boolean | null
          auto_deduct_inventory?: boolean | null
          auto_generate_lab_numbers?: boolean | null
          auto_invoice_numbering?: boolean | null
          backup_frequency?: string | null
          business_hours_end?: string | null
          business_hours_start?: string | null
          city?: string | null
          consultation_enabled?: boolean | null
          consultation_fee?: number | null
          country?: string | null
          created_at?: string | null
          currency?: string | null
          custom_fields?: Json | null
          data_retention_months?: number | null
          date_format?: string | null
          email?: string | null
          email_notifications?: boolean | null
          email_smtp_host?: string | null
          email_smtp_password?: string | null
          email_smtp_port?: number | null
          email_smtp_username?: string | null
          hospital_code?: string | null
          hospital_name?: string | null
          id?: string
          invoice_prefix?: string | null
          lab_enabled?: boolean | null
          lab_report_template?: string | null
          lab_result_approval_required?: boolean | null
          language?: string | null
          logo_url?: string | null
          low_stock_alerts?: boolean | null
          low_stock_threshold?: number | null
          max_appointments_per_day?: number | null
          max_login_attempts?: number | null
          password_expiry_days?: number | null
          payment_terms_days?: number | null
          paystack_public_key?: string | null
          paystack_secret_key?: string | null
          pharmacy_enabled?: boolean | null
          pharmacy_markup_percentage?: number | null
          phone_number?: string | null
          postal_code?: string | null
          receipt_prefix?: string | null
          reminder_hours_before?: number | null
          require_appointment?: boolean | null
          require_prescription?: boolean | null
          session_timeout_minutes?: number | null
          sms_api_key?: string | null
          sms_notifications?: boolean | null
          state?: string | null
          tax_name?: string | null
          tax_rate?: number | null
          time_format?: string | null
          timezone?: string | null
          two_factor_auth?: boolean | null
          updated_at?: string | null
          user_id: string
          website?: string | null
          working_days?: string[] | null
          allow_walk_ins?: boolean | null
          appointment_reminders?: boolean | null
        }
        Update: {
          address?: string | null
          appointment_duration?: number | null
          auto_backup_enabled?: boolean | null
          auto_deduct_inventory?: boolean | null
          auto_generate_lab_numbers?: boolean | null
          auto_invoice_numbering?: boolean | null
          backup_frequency?: string | null
          business_hours_end?: string | null
          business_hours_start?: string | null
          city?: string | null
          consultation_enabled?: boolean | null
          consultation_fee?: number | null
          country?: string | null
          created_at?: string | null
          currency?: string | null
          custom_fields?: Json | null
          data_retention_months?: number | null
          date_format?: string | null
          email?: string | null
          email_notifications?: boolean | null
          email_smtp_host?: string | null
          email_smtp_password?: string | null
          email_smtp_port?: number | null
          email_smtp_username?: string | null
          hospital_code?: string | null
          hospital_name?: string | null
          id?: string
          invoice_prefix?: string | null
          lab_enabled?: boolean | null
          lab_report_template?: string | null
          lab_result_approval_required?: boolean | null
          language?: string | null
          logo_url?: string | null
          low_stock_alerts?: boolean | null
          low_stock_threshold?: number | null
          max_appointments_per_day?: number | null
          max_login_attempts?: number | null
          password_expiry_days?: number | null
          payment_terms_days?: number | null
          paystack_public_key?: string | null
          paystack_secret_key?: string | null
          pharmacy_enabled?: boolean | null
          pharmacy_markup_percentage?: number | null
          phone_number?: string | null
          postal_code?: string | null
          receipt_prefix?: string | null
          reminder_hours_before?: number | null
          require_appointment?: boolean | null
          require_prescription?: boolean | null
          session_timeout_minutes?: number | null
          sms_api_key?: string | null
          sms_notifications?: boolean | null
          state?: string | null
          tax_name?: string | null
          tax_rate?: number | null
          time_format?: string | null
          timezone?: string | null
          two_factor_auth?: boolean | null
          updated_at?: string | null
          user_id?: string
          website?: string | null
          working_days?: string[] | null
          allow_walk_ins?: boolean | null
          appointment_reminders?: boolean | null
        }
        Relationships: [
          {
            foreignKeyName: "settings_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      user_access: {
        Row: {
          created_at: string
          id: string
          is_admin: boolean
          is_enabled: boolean
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          is_admin?: boolean
          is_enabled?: boolean
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          is_admin?: boolean
          is_enabled?: boolean
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      advance_patient_workflow: {
        Args: {
          p_workflow_id: string
          p_user_id: string
          p_assigned_to?: string
          p_notes?: string
        }
        Returns: {
          success: boolean
          message: string
          new_department: string
        }[]
      }
      get_dashboard_stats: {
        Args: { user_uuid: string }
        Returns: {
          total_patients: number
          today_appointments: number
          total_doctors: number
          total_departments: number
          total_revenue: number
          total_expenses: number
          occupancy_rate: number
        }[]
      }
      get_demo_appointments: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          patient_name: string
          doctor: string
          department: string
          date_time: string
          type: string
          status: string
          created_at: string
        }[]
      }
      get_demo_departments: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          department_name: string
          description: string
          department_head: string
          email_address: string
          phone_number: string
          created_at: string
        }[]
      }
      get_demo_doctors: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          name: string
          specialty: string
          email: string
          phone_number: string
          qualification: string
          experience: number
          bio: string
          status: string
          created_at: string
        }[]
      }
      get_demo_lab_tests: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          patient_name: string
          patient_id: string
          test_name: string
          test_date: string
          status: string
          lab_technician: string
          summary: string
          created_at: string
        }[]
      }
      get_demo_medical_records: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          patient_name: string
          doctor: string
          date: string
          record_type: string
          summary: string
          created_at: string
        }[]
      }
      get_demo_patients: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          patient_name: string
          email: string
          phone_number: string
          date_of_birth: string
          blood_type: string
          insurance: string
          registration_date: string
          created_at: string
        }[]
      }
      get_demo_pharmacy_inventory: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          medication_name: string
          stock_quantity: number
          price: number
          expiry_date: string
          status: string
          created_at: string
        }[]
      }
      get_patient_workflow_summary: {
        Args: { p_patient_id: string; p_user_id: string }
        Returns: {
          workflow_id: string
          current_department: string
          previous_department: string
          assigned_to: string
          consultation_count: number
          lab_test_count: number
          pharmacy_record_count: number
          workflow_notes: string
          last_updated: string
        }[]
      }
      get_user_subscription: {
        Args: { user_uuid: string }
        Returns: {
          subscription_id: string
          plan_type: string
          status: string
          amount: number
          start_date: string
          end_date: string
        }[]
      }
    }
    Enums: {
      workflow_status:
        | "reception"
        | "consultation"
        | "laboratory"
        | "pharmacy"
        | "completed"
      workflow_status_enum:
        | "registered"
        | "sent_to_consultation"
        | "in_consultation"
        | "consultation_completed"
        | "sent_to_laboratory"
        | "in_laboratory"
        | "laboratory_completed"
        | "workflow_completed"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      workflow_status: [
        "reception",
        "consultation",
        "laboratory",
        "pharmacy",
        "completed",
      ],
      workflow_status_enum: [
        "registered",
        "sent_to_consultation",
        "in_consultation",
        "consultation_completed",
        "sent_to_laboratory",
        "in_laboratory",
        "laboratory_completed",
        "workflow_completed",
      ],
    },
  },
} as const
