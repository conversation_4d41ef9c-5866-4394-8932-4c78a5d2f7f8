// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://ahnptskrmadropiaoeqe.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFobnB0c2tybWFkcm9waWFvZXFlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg5NDkyMjEsImV4cCI6MjA2NDUyNTIyMX0.pZiR_y2dClerW6bczUx0WPl4t1AtF3ItGoNDOr6t49o";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);